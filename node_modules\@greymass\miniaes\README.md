miniaes-js
==========

Fast AES encryption and decryption in JavaScript. Only CBC mode, because that's what we need, this library is largely based on [asmcrypto.js](https://github.com/asmcrypto/asmcrypto.js).

## Installation

The `@greymass/miniaes` package is distributed as a module on [npm](https://www.npmjs.com/package/@greymass/miniaes).

```
yarn add @greymass/miniaes
# or
npm install --save @greymass/miniaes
```

## Usage

TODO

## Developing

You need [Make](https://www.gnu.org/software/make/), [node.js](https://nodejs.org/en/) and [yarn](https://classic.yarnpkg.com/en/docs/install) installed.

Clone the repository and run `make` to checkout all dependencies and build the project. See the [Makefile](./Makefile) for other useful targets. Before submitting a pull request make sure to run `make lint`.

---

Made with ☕️ & ❤️ by [Greymass](https://greymass.com), if you find this useful please consider [supporting us](https://greymass.com/support-us).
