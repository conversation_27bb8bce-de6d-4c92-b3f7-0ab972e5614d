Quickstart Guide
Getting started with creating your first assets in the AtomicAssets standard is a slightly more complex task than with other existing asset standards, but it still shouldn't take more than a couple of minutes and it is well worth it. This guide is meant to lead your through the process of getting you started from zero.
If you want to learn more about the structure of the AtomicAssets standard, you can check out Structure.

1. Creating a collection
Collections are meant to group assets together and also handle the permissions for these assets. They also contain a data field that follows a unified schema, where you can later add things like a description or a website URL. This information then helps 3rd party websites and applications to display more relevant information about your collection.

Since each asset needs to belong to a collection, you should create one using the createcol action. Make sure to include the account that you want to create your assets with in the authorized_accounts parameter, as only the accounts within this list will be able to create and edit assets. As you can still change everything except the collection_name later, you don't need to worry about the remaining parameters for now. If you're not sure about the allow_notify parameter, set it to true. You can still change it to false later, but once it is false, you can't change it back to true.

2. Creating a schema
Assets and Templates use a custom data serialization, and for that to work, you need a schema. A schema contains a vector of FORMATs, each of which describes an attribute that your assets will later be able to have.

Use the createschema action to create a schema, and include all the attributes that you want your assets to be able to have in the schema_format parameter. You can however still extend the format later using the extendschema action.

3. (Optional) Creating a template
Templates are optional. If you want to get started quickly, feel free to ignore them for now and skip to Step 4.

Templates' main purpose is to save RAM costs by storing data that is duplicate in a lot of similar assets only once. If you for example planned to create a collection of car collectibles, you might decide to create a template for each car brand, which you then wouldn't have to store in each individual asset anymore.

You can create a template using the createtempl action. Make sure to reference the schema and collection you previously created in this action. On top of the immutable data, you can also choose if the assets of this template should be transferable or burnable and you could set a maximum supply of assets that can be created within the template (0 if you don't want a max supply).

After sending the transaction including the createtempl action, check out the transaction in a block explorer like bloks.io. In the Traces section, you will see a lognewtempl action. This action includes the parameter template_id, which is what you will have to reference when minting an asset belonging to this template.

4. Minting an asset
You can now mint your first asset using the mintasset action. If you created a template in Step 3, enter the template_id that you got from the last step, otherwise enter -1 in the template_id parameter.