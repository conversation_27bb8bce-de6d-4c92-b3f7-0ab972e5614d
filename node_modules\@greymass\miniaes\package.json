{"name": "@greymass/miniaes", "description": "Small and fast AES (CBC mode only)", "version": "1.0.0", "homepage": "https://github.com/greymass/miniaes-js", "license": "BSD-3-<PERSON><PERSON>", "main": "lib/miniaes.js", "module": "lib/miniaes.m.js", "types": "lib/miniaes.d.ts", "sideEffects": false, "files": ["lib/*", "src/*"], "scripts": {"prepare": "make"}, "dependencies": {"tslib": "^2.1.0"}, "devDependencies": {"@rollup/plugin-alias": "^3.1.4", "@rollup/plugin-commonjs": "^21.1.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.2.1", "@rollup/plugin-typescript": "^8.3.2", "@rollup/plugin-virtual": "^2.0.3", "@types/chai": "^4.3.1", "@types/mocha": "^9.0.0", "@types/node": "^16.4.0", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "chai": "^4.3.4", "eslint": "^8.13.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0", "gh-pages": "^3.1.0", "mocha": "^9.0.2", "nyc": "^15.1.0", "prettier": "^2.2.1", "rollup": "^2.70.2", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-terser": "^7.0.2", "ts-node": "^10.1.0", "tsconfig-paths": "^3.10.1", "typedoc": "^0.22.15", "typescript": "^4.1.2"}}