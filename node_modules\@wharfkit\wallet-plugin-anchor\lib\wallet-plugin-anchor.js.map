{"version": 3, "file": "wallet-plugin-anchor.js", "sources": ["../node_modules/tslib/tslib.es6.js", "../src/translations/index.ts", "../src/index.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.push(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.push(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n", null, null], "names": ["AbstractWalletPlugin", "WalletPluginMetadata", "Logo", "createIdentityRequest", "isKnownMobile", "waitForCallback", "verifyLoginCallbackResponse", "PublicKey", "ResolvedSigningRequest", "Checksum256", "PermissionLevel", "LinkInfo", "setTransactionCallback", "generateReturnUrl", "isAppleHandheld", "sealMessage", "Private<PERSON><PERSON>", "send", "Serializer", "is<PERSON><PERSON>back", "extractSignaturesFromCallback"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAoGA;AACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;AAC7D,IAAI,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;AAChH,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;AAC/D,QAAQ,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;AACnG,QAAQ,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;AACtG,QAAQ,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;AACtH,QAAQ,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAC9E,KAAK,CAAC,CAAC;AACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrHA,0BAAe;IACX,EAAE;IACF,EAAE;AACF,IAAA,SAAS,EAAE,OAAO;AAClB,IAAA,SAAS,EAAE,OAAO;CACrB;;ACiCK,MAAO,kBAAmB,SAAQA,4BAAoB,CAAA;AAqBxD,IAAA,WAAA,CAAY,OAA6B,EAAA;AACrC,QAAA,KAAK,EAAE,CAAA;AAXX;;AAEG;QACH,IAAE,CAAA,EAAA,GAAG,QAAQ,CAAA;AAEb;;AAEG;QACH,IAAY,CAAA,YAAA,GAAG,mBAAmB,CAAA;AASlC;;AAEG;AACM,QAAA,IAAA,CAAA,MAAM,GAAuB;;AAElC,YAAA,mBAAmB,EAAE,KAAK;;AAE1B,YAAA,wBAAwB,EAAE,KAAK;SAClC,CAAA;AACD;;AAEG;AACM,QAAA,IAAA,CAAA,QAAQ,GAAyBC,4BAAoB,CAAC,IAAI,CAAC;AAChE,YAAA,IAAI,EAAE,QAAQ;AACd,YAAA,WAAW,EAAE,EAAE;AACf,YAAA,IAAI,EAAEC,YAAI,CAAC,IAAI,CAAC;AACZ,gBAAA,IAAI,EAAE,w8CAAw8C;AAC98C,gBAAA,KAAK,EAAE,ggCAAggC;aAC1gC,CAAC;AACF,YAAA,QAAQ,EAAE,6BAA6B;AACvC,YAAA,QAAQ,EAAE,sCAAsC;AACnD,SAAA,CAAC,CAAA;AAzBE,QAAA,IAAI,CAAC,OAAO,GAAG,CAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,OAAO,KAAI,wBAAwB,CAAA;QAC3D,IAAI,CAAC,MAAM,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,MAAM,CAAA;KAChC;AAwBD;;;;;AAKG;AACH,IAAA,KAAK,CAAC,OAAqB,EAAA;QACvB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACnC,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACpB,iBAAA,IAAI,CAAC,CAAC,QAAQ,KAAI;gBACf,OAAO,CAAC,QAAQ,CAAC,CAAA;AACrB,aAAC,CAAC;AACD,iBAAA,KAAK,CAAC,CAAC,KAAK,KAAI;gBACb,MAAM,CAAC,KAAK,CAAC,CAAA;AACjB,aAAC,CAAC,CAAA;AACV,SAAC,CAAC,CAAA;KACL;AAEK,IAAA,WAAW,CAAC,OAAqB,EAAA;;;AACnC,YAAA,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;AACb,gBAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;AACrC,aAAA;;AAGD,YAAA,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;;YAG1C,MAAM,EAAC,QAAQ,EAAE,OAAO,EAAE,iBAAiB,EAAE,UAAU,EAAE,UAAU,EAAC,GAChE,MAAMC,iCAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;;AAGtD,YAAA,MAAM,QAAQ,GAAoB;AAC9B,gBAAA;AACI,oBAAA,IAAI,EAAE,MAAM;oBACZ,KAAK,EAAE,CAAC,CAAC,YAAY,EAAE,EAAC,OAAO,EAAE,eAAe,EAAC,CAAC;AAClD,oBAAA,IAAI,EAAE;wBACF,IAAI,EAAE,iBAAiB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC;wBACnD,KAAK,EAAE,CAAC,CAAC,YAAY,EAAE,EAAC,OAAO,EAAE,eAAe,EAAC,CAAC;AAClD,wBAAA,OAAO,EAAE,SAAS;AACrB,qBAAA;AACJ,iBAAA;aACJ,CAAA;;YAGD,IAAI,CAACC,yBAAa,EAAE,EAAE;gBAClB,QAAQ,CAAC,OAAO,CAAC;AACb,oBAAA,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC;AAC5C,iBAAA,CAAC,CAAA;AACL,aAAA;;AAGD,YAAA,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;;YAGpE,MAAM,cAAc,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,EAAE,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,CAAC;gBACtC,KAAK,EAAE,CAAC,CAAC,aAAa,EAAE,EAAC,OAAO,EAAE,qBAAqB,EAAC,CAAC;AACzD,gBAAA,IAAI,EAAE,CAAC,CAAC,YAAY,EAAE;AAClB,oBAAA,OAAO,EACH,0FAA0F;iBACjG,CAAC;gBACF,QAAQ;AACX,aAAA,CAAC,CAAA;AAEF,YAAA,cAAc,CAAC,KAAK,CAAC,MAAK;;AAEtB,gBAAA,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;AAChC,aAAC,CAAC,CAAA;;AAGF,YAAA,MAAM,gBAAgB,GAAoB,MAAMC,2BAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;AACzF,YAAAC,uCAA2B,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAA;AAEtD,YAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE;AACvE,gBAAA,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;AAC/C,aAAA;YAED,IAAI,gBAAgB,CAAC,OAAO,IAAI,gBAAgB,CAAC,QAAQ,IAAI,gBAAgB,CAAC,SAAS,EAAE;AACrF,gBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;AACjC,gBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,SAAS;oBACf,gBAAgB,CAAC,QAAQ,IAAIC,iBAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAA;gBAC1E,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAA;gBAC/C,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,gBAAgB,CAAC,SAAS,CAAA;gBAElD,IAAI;oBACA,IAAI,gBAAgB,CAAC,SAAS,EAAE;wBAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;wBACvD,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAA;wBAC1C,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAA;wBACxC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAA;AAC7C,qBAAA;AACJ,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;;AAEX,iBAAA;AACJ,aAAA;AAED,YAAA,MAAM,gBAAgB,GAAG,MAAMC,8BAAsB,CAAC,WAAW,CAC7D,gBAAgB,EAChB,OAAO,CAAC,UAAU,CACrB,CAAA;YAED,MAAM,aAAa,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA;YAE7E,OAAO;gBACH,KAAK,EAAEC,mBAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;AAC7C,gBAAA,eAAe,EAAEC,uBAAe,CAAC,IAAI,CAAC;oBAClC,KAAK,EAAE,gBAAgB,CAAC,EAAE;oBAC1B,UAAU,EAAE,gBAAgB,CAAC,EAAE;iBAClC,CAAC;gBACF,aAAa;aAChB,CAAA;;AACJ,KAAA;AAED;;;;;;AAMG;;IAEH,IAAI,CACA,QAAgC,EAChC,OAAwB,EAAA;QAExB,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;KACtD;IAEa,oBAAoB,CAC9B,QAAgC,EAChC,OAAwB,EAAA;;AAExB,YAAA,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;AACb,gBAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;AACrC,aAAA;;AAGD,YAAA,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;;YAG1C,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,CAAA;AAC3D,YAAA,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAA;AACtB,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,CAAA;;AAGlE,YAAA,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,aAAa,CAAC,EAAC,WAAW,EAAE,QAAQ,CAAC,WAAW,EAAC,CAAC,CAAA;;YAGxF,eAAe,CAAC,UAAU,CACtB,MAAM,EACNC,oBAAQ,CAAC,IAAI,CAAC;gBACV,UAAU;AACb,aAAA,CAAC,CACL,CAAA;;YAGD,MAAM,QAAQ,GAAGC,kCAAsB,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;YAEtE,MAAM,OAAO,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;;YAGnD,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,KAAK,CAAA;;AAGnD,YAAA,MAAM,iBAAiB,GAAG,eAAe,CAAC,KAAK,EAAE,CAAA;AACjD,YAAA,MAAM,SAAS,GAAGC,6BAAiB,EAAE,CAAA;AACrC,YAAA,iBAAiB,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;AACjD,YAAA,IAAI,SAAS,EAAE;AACX,gBAAA,iBAAiB,CAAC,UAAU,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;AACzD,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACtB,gBAAA,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACrB,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;AAC7C,iBAAA;qBAAM,IAAIC,2BAAe,EAAE,EAAE;AAC1B,oBAAA,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,eAAe,CAAA;AACzC,iBAAA;AACJ,aAAA;YAED,MAAM,YAAY,GAAG,MAAK;;AACtB,gBAAA,CAAA,EAAA,GAAA,OAAO,CAAC,EAAE,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,CAAC;oBACf,KAAK,EAAE,CAAC,CAAC,8BAA8B,EAAE,EAAC,OAAO,EAAE,eAAe,EAAC,CAAC;AACpE,oBAAA,IAAI,EAAE,CAAC,CAAC,6BAA6B,EAAE;AACnC,wBAAA,OAAO,EACH,mFAAmF;qBAC1F,CAAC;AACF,oBAAA,QAAQ,EAAE;AACN,wBAAA;AACI,4BAAA,IAAI,EAAE,IAAI;AACV,4BAAA,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC;AACxB,yBAAA;AACD,wBAAA;AACI,4BAAA,IAAI,EAAE,MAAM;4BACZ,KAAK,EAAE,CAAC,CAAC,mCAAmC,EAAE,EAAC,OAAO,EAAE,aAAa,EAAC,CAAC;AACvE,4BAAA,IAAI,EAAE;AACF,gCAAA,IAAI,EAAE,MAAM,CAAC,iBAAiB,CAAC;gCAC/B,KAAK,EAAE,CAAC,CAAC,mCAAmC,EAAE,EAAC,OAAO,EAAE,aAAa,EAAC,CAAC;AAC1E,6BAAA;AACJ,yBAAA;AACJ,qBAAA;AACJ,iBAAA,CAAC,CAAA;AACN,aAAC,CAAA;;AAGD,YAAA,MAAM,aAAa,GAA+B,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC;gBAChE,KAAK,EAAE,CAAC,CAAC,gBAAgB,EAAE,EAAC,OAAO,EAAE,uBAAuB,EAAC,CAAC;AAC9D,gBAAA,IAAI,EAAE,CAAC,CAAC,eAAe,EAAE;AACrB,oBAAA,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW;AAClC,oBAAA,OAAO,EAAE,CAAsC,mCAAA,EAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAA2C,yCAAA,CAAA;iBAClH,CAAC;AACF,gBAAA,QAAQ,EAAE;AACN,oBAAA;AACI,wBAAA,IAAI,EAAE,WAAW;AACjB,wBAAA,IAAI,EAAE;4BACF,KAAK,EAAE,CAAC,CAAC,gBAAgB,EAAE,EAAC,OAAO,EAAE,kCAAkC,EAAC,CAAC;AACzE,4BAAA,GAAG,EAAE,UAAU,CAAC,WAAW,EAAE;AAChC,yBAAA;AACJ,qBAAA;AACD,oBAAA;AACI,wBAAA,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,CAAC,CAAC,gBAAgB,EAAE,EAAC,OAAO,EAAE,sCAAsC,EAAC,CAAC;AAC7E,wBAAA,IAAI,EAAE;AACF,4BAAA,OAAO,EAAE,YAAY;AACjB,kCAAE,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC;AAC3D,kCAAE,YAAY;AAClB,4BAAA,KAAK,EAAE,CAAC,CAAC,gBAAgB,EAAE;AACvB,gCAAA,OAAO,EAAE,sCAAsC;6BAClD,CAAC;AACL,yBAAA;AACJ,qBAAA;AACJ,iBAAA;AACJ,aAAA,CAAC,CAAA;;AAGF,YAAA,MAAM,KAAK,GAAG,UAAU,CAAC,MAAK;AAC1B,gBAAA,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;AACb,oBAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;AACrC,iBAAA;AACD,gBAAA,aAAa,CAAC,MAAM,CAChB,CAAC,CAAC,eAAe,EAAE,EAAC,OAAO,EAAE,wCAAwC,EAAC,CAAC,CAC1E,CAAA;aACJ,EAAE,SAAS,CAAC,CAAA;;YAGb,aAAa,CAAC,KAAK,CAAC,MAAM,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;;AAG9C,YAAA,MAAM,eAAe,GAAGT,2BAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;;AAGjE,YAAA,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACtB,gBAAA,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAA;AACpD,gBAAA,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;gBACnE,MAAM,aAAa,GAAGU,uBAAW,CAC7B,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,iBAAiB,GAAG,eAAe,EAAE,MAAM,CAC/D,IAAI,EACJ,KAAK,EACL,MAAM,CACT,EACDC,kBAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EACrCT,iBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CACtC,CAAA;AAED,gBAAAU,SAAI,CAACC,kBAAU,CAAC,MAAM,CAAC,EAAC,MAAM,EAAE,aAAa,EAAC,CAAC,CAAC,KAAK,EAAE;oBACnD,OAAO;oBACP,OAAO;AACV,iBAAA,CAAC,CAAA;AACL,aAAA;AAAM,iBAAA;;gBAEH,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAA;AACpD,aAAA;;AAGD,YAAA,MAAM,gBAAgB,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC,CAAC,OAAO,CACjF,MAAK;;gBAED,YAAY,CAAC,KAAK,CAAC,CAAA;AACvB,aAAC,CACJ,CAAA;AAED,YAAA,MAAM,aAAa,GACfC,sBAAU,CAAC,gBAAgB,CAAC;AAC5B,gBAAAC,yCAA6B,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAA;AAE9D,YAAA,IAAI,aAAa,EAAE;;AAEf,gBAAA,MAAM,eAAe,GAAG,MAAMZ,8BAAsB,CAAC,WAAW,CAC5D,gBAAgB,EAChB,OAAO,CAAC,UAAU,CACrB,CAAA;;gBAGD,OAAO;AACH,oBAAA,UAAU,EAAEY,yCAA6B,CAAC,gBAAgB,CAAC;AAC3D,oBAAA,QAAQ,EAAE,eAAe;iBAC5B,CAAA;AACJ,aAAA;AAED,YAAA,MAAM,WAAW,GAAG,CAAC,CAAC,qBAAqB,EAAE,EAAC,OAAO,EAAE,gCAAgC,EAAC,CAAC,CAAA;AAEzF,YAAA,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;;AAGjC,YAAA,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAA;SAC/B,CAAA,CAAA;AAAA,KAAA;AACJ;;;;"}