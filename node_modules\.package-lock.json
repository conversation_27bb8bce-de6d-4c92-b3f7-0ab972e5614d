{"name": "nft-access-demo", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"node_modules/@esbuild/win32-x64": {"version": "0.18.20", "resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.18.20.tgz", "integrity": "sha512-kTdfRcSiDfQca/y9QIkng02avJ+NCaQvrMejlsB3RRv5sE9rRoeBPISaZpKxHELzRxZyLvNts1P27W3wV+8geQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@greymass/buoy": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/@greymass/buoy/-/buoy-1.0.4.tgz", "integrity": "sha512-/O9EsjWJw81TiJcvKqMKxjrJpEfHKh8WcUK07gFT+uS0+JUKfoVrYVQn1qNOAdYWU+AZrhZJop8xP/LYmKfGHg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"eventemitter3": "^4.0.7", "tslib": "^2.1.0"}}, "node_modules/@greymass/miniaes": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/@greymass/miniaes/-/miniaes-1.0.0.tgz", "integrity": "sha512-xKYTnAyyoq3gpJS11JvU4dkju28M8Zbp06CuP3aKR4DMH6B2z64LauGGGzKksKh5Qd770JA/dzhLbImnBlymbg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/@wharfkit/abicache": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/@wharfkit/abicache/-/abicache-1.2.2.tgz", "integrity": "sha512-yOsYz2qQpQy7Nb8XZj62pZqp8YnmWDqFlrenYksBb9jl+1aWIpFhWd+14VEez4tUAezRH4UWW+w1SX5vhmUY9A==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@wharfkit/antelope": "^1.0.2", "@wharfkit/signing-request": "^3.1.0", "pako": "^2.0.4", "tslib": "^2.1.0"}}, "node_modules/@wharfkit/antelope": {"version": "1.0.13", "resolved": "https://registry.npmjs.org/@wharfkit/antelope/-/antelope-1.0.13.tgz", "integrity": "sha512-f4O5O8+6Bd5BHpMUHTmlWQmlhX5xYb4AfzT2NJweyqIPqQOstm+aInF42AtUhSALDa8fvoY80YZoqwM0L8TUyw==", "license": "BSD-3-Clause-No-Military-License", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "elliptic": "^6.5.4", "hash.js": "^1.0.0", "pako": "^2.1.0", "tslib": "^2.0.3"}}, "node_modules/@wharfkit/common": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@wharfkit/common/-/common-1.5.0.tgz", "integrity": "sha512-eqXkOy+vshcEzK8kED+EsoTPJjlBKHYglgV9CBnZQgIlGrWIRXWH4YaXH3W7EbI/nCRJCaNqxm5fC+pgpFcp8g==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tslib": "^2.1.0"}, "peerDependencies": {"@wharfkit/antelope": "^1.0.0"}}, "node_modules/@wharfkit/protocol-esr": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@wharfkit/protocol-esr/-/protocol-esr-1.4.0.tgz", "integrity": "sha512-RxNMkzL0JH9aH/u/6ffaGhqplHHKqHl6VaM7bpatq6Gw2scvvr23WX8vieIo4TVoKJQUwDi45ENJjRtvOAqy2w==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@greymass/buoy": "^1.0.3", "@greymass/miniaes": "^1.0.0", "@wharfkit/antelope": "^1.0.5", "isomorphic-ws": "^5.0.0", "ws": "^8.13.0"}, "peerDependencies": {"@wharfkit/antelope": "^1.0.5", "@wharfkit/session": "^1.2.7"}}, "node_modules/@wharfkit/session": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@wharfkit/session/-/session-1.6.0.tgz", "integrity": "sha512-yrCDcW42Dqd387usVHa3qjnttc65OzHK3REKovzo6wLpQsPn709xzx4J6B9e9Xjb0WC/GQLghtr9EX+T+rRH6w==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@wharfkit/abicache": "^1.2.1", "@wharfkit/antelope": "^1.0.11", "@wharfkit/common": "^1.2.0", "@wharfkit/signing-request": "^3.1.0", "pako": "^2.0.4", "tslib": "^2.1.0"}}, "node_modules/@wharfkit/signing-request": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/@wharfkit/signing-request/-/signing-request-3.2.0.tgz", "integrity": "sha512-rIMzqwAKA5vb09+1BI+9fUXbj73JIkYcD1XT/Tom+k/+bqi51JcmC0trjCOjTUOK9UYDabpxYFixrf1ZvQymKw==", "license": "MIT", "dependencies": {"@wharfkit/antelope": "^1.0.7", "tslib": "^2.0.3"}}, "node_modules/@wharfkit/wallet-plugin-anchor": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@wharfkit/wallet-plugin-anchor/-/wallet-plugin-anchor-1.5.0.tgz", "integrity": "sha512-n2WIK2LvejI+/RC9vHB8GYzO/PO/M/jx8s67n8IUKXmY2kVKGhcL55mwUJTg158x6Zsk9GIdoBx/xyosgsJeCQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@wharfkit/antelope": "^1.0.5", "@wharfkit/protocol-esr": "^1.4.0", "isomorphic-ws": "^5.0.0", "ws": "^8.13.0"}, "peerDependencies": {"@wharfkit/session": "^1.2.7"}}, "node_modules/@wharfkit/web-renderer": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/@wharfkit/web-renderer/-/web-renderer-1.4.1.tgz", "integrity": "sha512-ky/keS0rHxJcdIWLo7d9H6eo+rK0l2mQj/3xOCwVaDKBAd/dTQwuqYIzOYMC9azdyMglgNcTRgYWm5PJrU4lIA==", "license": "BSD-3-<PERSON><PERSON>", "peerDependencies": {"@wharfkit/session": "^1.4.0"}}, "node_modules/bn.js": {"version": "4.12.2", "resolved": "https://registry.npmjs.org/bn.js/-/bn.js-4.12.2.tgz", "integrity": "sha512-n4DSx829VRTRByMRGdjQ9iqsN0Bh4OolPsFnaZBLcbi8iXcB+kJ9s7EnRt4wILZNV3kPLHkRVfOc/HvhC3ovDw==", "license": "MIT"}, "node_modules/brorand": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/brorand/-/brorand-1.1.0.tgz", "integrity": "sha512-cKV8tMCEpQs4hK/ik71d6LrPOnpkpGBR0wzxqr68g2m/LB2GxVYQroAjMJZRVM1Y4BCjCKc3vAamxSzOY2RP+w==", "license": "MIT"}, "node_modules/elliptic": {"version": "6.6.1", "resolved": "https://registry.npmjs.org/elliptic/-/elliptic-6.6.1.tgz", "integrity": "sha512-RaddvvMatK2LJHqFJ+YA4WysVN5Ita9E35botqIYspQ4TkRAlCicdzKOjlyv/1Za5RyTNn7di//eEV0uTAfe3g==", "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/esbuild": {"version": "0.18.20", "resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.18.20.tgz", "integrity": "sha512-ceqxoedUrcayh7Y7ZX6NdbbDzGROiyVBgC4PriJThBKSVPWnnFHZAkfI1lJT8QFkOwH4qOS2SJkS4wvpGl8BpA==", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/android-arm": "0.18.20", "@esbuild/android-arm64": "0.18.20", "@esbuild/android-x64": "0.18.20", "@esbuild/darwin-arm64": "0.18.20", "@esbuild/darwin-x64": "0.18.20", "@esbuild/freebsd-arm64": "0.18.20", "@esbuild/freebsd-x64": "0.18.20", "@esbuild/linux-arm": "0.18.20", "@esbuild/linux-arm64": "0.18.20", "@esbuild/linux-ia32": "0.18.20", "@esbuild/linux-loong64": "0.18.20", "@esbuild/linux-mips64el": "0.18.20", "@esbuild/linux-ppc64": "0.18.20", "@esbuild/linux-riscv64": "0.18.20", "@esbuild/linux-s390x": "0.18.20", "@esbuild/linux-x64": "0.18.20", "@esbuild/netbsd-x64": "0.18.20", "@esbuild/openbsd-x64": "0.18.20", "@esbuild/sunos-x64": "0.18.20", "@esbuild/win32-arm64": "0.18.20", "@esbuild/win32-ia32": "0.18.20", "@esbuild/win32-x64": "0.18.20"}}, "node_modules/eventemitter3": {"version": "4.0.7", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==", "license": "MIT"}, "node_modules/hash.js": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/hash.js/-/hash.js-1.1.7.tgz", "integrity": "sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA==", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/hmac-drbg": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "integrity": "sha512-Tti3gMqLdZfhOQY1Mzf/AanLiqh1WTiJgEj26ZuYQ9fbkLomzGchCws4FyrSd4VkpBfiNhaE1On+lOz894jvXg==", "license": "MIT", "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "license": "ISC"}, "node_modules/isomorphic-ws": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/isomorphic-ws/-/isomorphic-ws-5.0.0.tgz", "integrity": "sha512-muId7Zzn9ywDsyXgTIafTry2sV3nySZeUDe6YedVd1Hvuuep5AsIlqK+XefWpYTyJG5e503F2xIuT2lcU6rCSw==", "license": "MIT", "peerDependencies": {"ws": "*"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "integrity": "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==", "license": "ISC"}, "node_modules/minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha512-JIYlbt6g8i5jKfJ3xz7rF0LXmv2TkDxBLUkiBeZ7bAx4GnnNMr8xFpGnOxn6GhTEHx3SjRrZEoU+j04prX1ktg==", "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+**************************==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/pako": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/pako/-/pako-2.1.0.tgz", "integrity": "sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug==", "license": "(MIT AND Zlib)"}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true, "license": "ISC"}, "node_modules/postcss": {"version": "8.5.6", "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/rollup": {"version": "3.29.5", "resolved": "https://registry.npmjs.org/rollup/-/rollup-3.29.5.tgz", "integrity": "sha512-GVsDdsbJzzy4S/v3dqWPJ7EfvZJfCHiDqe80IyrF59LYuP+e6U1LJoUqeuqRbwAWoMNoXivMNeNAOf5E22VA1w==", "dev": true, "license": "MIT", "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/vite": {"version": "4.5.14", "resolved": "https://registry.npmjs.org/vite/-/vite-4.5.14.tgz", "integrity": "sha512-+v57oAaoYNnO3hIu5Z/tJRZjq5aHM2zDve9YZ8HngVHbhk66RStobhb1sqPMIPEleV6cNKYK4eGrAbE9Ulbl2g==", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.18.10", "postcss": "^8.4.27", "rollup": "^3.27.1"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "peerDependencies": {"@types/node": ">= 14", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}}, "node_modules/ws": {"version": "8.18.2", "resolved": "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz", "integrity": "sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}}}