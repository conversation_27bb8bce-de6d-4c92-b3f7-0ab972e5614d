{"version": 3, "file": "buoy-client.js", "sources": ["../src/errors.ts", "../src/listener.ts", "../src/receive.ts", "../src/send.ts"], "sourcesContent": [null, null, null, null], "names": ["globalBuoy", "ListenerEncoding", "EventEmitter", "SendResult"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;MACa,WAAY,SAAQ,KAAK;IAElC,YAAqB,KAAY;QAC7B,KAAK,CAAC,cAAc,CAAC,CAAA;QADJ,UAAK,GAAL,KAAK,CAAO;QADjC,SAAI,GAAG,WAAW,CAAA;KAGjB;CACJ;AAED;MACa,YAAa,SAAQ,KAAK;IAEnC,YAAqB,MAAc,EAAW,eAAuB;QACjE,KAAK,CAAC,MAAM,CAAC,CAAA;QADI,WAAM,GAAN,MAAM,CAAQ;QAAW,oBAAe,GAAf,eAAe,CAAQ;QADrE,SAAI,GAAG,WAAW,CAAA;KAGjB;;;ACRL,MAAMA,YAAU,GAAG,UAAU,IAAI,MAAM,CAAA;AAE3BC;AAAZ,WAAY,gBAAgB;IACxB,qCAAiB,CAAA;IACjB,iCAAa,CAAA;IACb,iCAAa,CAAA;AACjB,CAAC,EAJWA,wBAAgB,KAAhBA,wBAAgB,QAI3B;MAaY,QAAS,SAAQC,gCAAY;IAUtC,YAAY,OAAwB;QAChC,KAAK,EAAE,CAAA;QARH,WAAM,GAAG,KAAK,CAAA;QASlB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA;SACxD;QACD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAA;SACzD;QACD,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;QACzE,IAAI,CAAC,GAAG,GAAG,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO,MAAM,CAAA;QAC9C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAID,wBAAgB,CAAC,IAAI,CAAA;QACzD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAID,YAAU,CAAC,SAAS,CAAA;QAC1D,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,EAAE;YAC/B,IAAI,CAAC,OAAO,EAAE,CAAA;SACjB;KACJ;IAED,OAAO;QACH,IAAI,IAAI,CAAC,MAAM;YAAE,OAAM;QACvB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QAClB,IAAI,OAAO,GAAG,CAAC,CAAA;QACf,IAAI,SAAc,CAAA;QAElB,MAAM,OAAO,GAAG;YACZ,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC3C,MAAM,CAAC,SAAS,GAAG,CAAC,KAAK;gBACrB,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,KAAK,CAAC,IAAI,YAAY,IAAI,EAAE;oBAC3D,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAA;oBAC/B,MAAM,CAAC,MAAM,GAAG;wBACZ,IAAI,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,MAAqB,CAAC,CAAC,CAAA;qBACnE,CAAA;oBACD,MAAM,CAAC,OAAO,GAAG;wBACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAAA;qBACjE,CAAA;oBACD,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;iBACvC;qBAAM,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;oBACvC,IAAI,CAAC,aAAa,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;iBAC3D;qBAAM,IACH,OAAOA,YAAU,CAAC,MAAM,KAAK,WAAW;qBACvC,KAAK,CAAC,IAAI,YAAYA,YAAU,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EACxE;oBACE,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,CAAA;oBACvB,IAAI,CAACA,YAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;wBACrC,MAAM,GAAGA,YAAU,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;qBAC5C;oBACD,IAAI,CAAC,aAAa,CACd,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,CACtE,CAAA;iBACJ;qBAAM,IAAI,KAAK,CAAC,IAAI,YAAY,UAAU,EAAE;oBACzC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;iBACjC;qBAAM,IAAI,KAAK,CAAC,IAAI,YAAY,WAAW,EAAE;oBAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;iBACjD;qBAAM;oBACH,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,YAAY,CAAC,2BAA2B,CAAC,CAAC,CAAA;iBACpE;aACJ,CAAA;YACD,MAAM,CAAC,OAAO,GAAG,CAAC,KAAK;gBACnB,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE;oBACvC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,CAAA;iBAC7C;aACJ,CAAA;YACD,MAAM,CAAC,MAAM,GAAG;gBACZ,OAAO,GAAG,CAAC,CAAA;gBACX,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;aACvB,CAAA;YACD,MAAM,CAAC,OAAO,GAAG;gBACb,IAAI,IAAI,CAAC,MAAM,EAAE;oBACb,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;oBACxB,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;iBACvD;gBACD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;gBACvB,YAAY,CAAC,SAAS,CAAC,CAAA;gBACvB,IAAI,IAAI,CAAC,cAAc,EAAE;oBACrB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;iBACrC;gBACD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;aAC1B,CAAA;;YAGD,IAAI,CAAC,sBAAsB,EAAE,CAAA;;;YAG7B,MAAM,UAAU,GAAG,MAAa,CAAA;YAChC,IAAI,OAAO,UAAU,CAAC,EAAE,KAAK,UAAU,IAAI,OAAO,UAAU,CAAC,SAAS,KAAK,UAAU,EAAE;gBACnF,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE;oBAClB,YAAY,CAAC,SAAS,CAAC,CAAA;oBACvB,SAAS,GAAG,UAAU,CAAC;wBACnB,UAAU,CAAC,SAAS,EAAE,CAAA;qBACzB,EAAE,EAAE,GAAG,IAAI,CAAC,CAAA;iBAChB,CAAC,CAAA;aACL;YACD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;SACvB,CAAA;QACD,OAAO,EAAE,CAAA;KACZ;IAED,UAAU;QACN,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QACnB,IACI,IAAI,CAAC,MAAM;aACV,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI;gBAC3C,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAC3D;YACE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;SAC1B;KACJ;IAED,IAAI,WAAW;;QACX,OAAO,IAAI,CAAC,MAAM,IAAI,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,UAAU,KAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAA;KACvE;IAEO,aAAa,CAAC,KAAiB;;QACnC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YAC7D,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,CAAC,IAAI,UAAU,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC/D,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;SAC5B;QACD,IAAI,OAAY,CAAA;QAChB,QAAQ,IAAI,CAAC,QAAQ;YACjB,KAAKC,wBAAgB,CAAC,MAAM;gBACxB,OAAO,GAAG,KAAK,CAAA;gBACf,MAAK;YACT,KAAKA,wBAAgB,CAAC,IAAI;gBACtB,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;gBACzC,MAAK;YACT,KAAKA,wBAAgB,CAAC,IAAI,EAAE;gBACxB,IAAI;oBACA,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;iBACxD;gBAAC,OAAO,KAAK,EAAE;oBACZ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,YAAY,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC,CAAA;oBACpE,OAAM;iBACT;aACJ;SACJ;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;KAChC;IAEO,sBAAsB;QAC1B,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;;YAC9B,MAAA,IAAI,CAAC,MAAM,0CAAE,KAAK,CAAC,IAAI,CAAC,CAAA;SAC3B,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;KACrB;CACJ;AAED;;;;AAIA,SAAS,OAAO,CAAC,KAAa;IAC1B,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAA;AACrD;;AC1KA;;;;SAIgB,OAAO,CAAC,OAAuB,EAAE,GAAoB;IACjE,OAAO,IAAI,OAAO,CAAM,CAAC,OAAO,EAAE,MAAM;QACpC,MAAM,QAAQ,GAAG,IAAI,QAAQ,iCAAK,OAAO,KAAE,WAAW,EAAE,IAAI,IAAE,CAAA;QAC9D,IAAI,KAAU,CAAA;QACd,IAAI,SAA4B,CAAA;QAChC,MAAM,IAAI,GAAG,CAAC,KAAa,EAAE,OAAa;YACtC,YAAY,CAAC,KAAK,CAAC,CAAA;YACnB,IAAI,KAAK,EAAE;gBACP,MAAM,CAAC,KAAK,CAAC,CAAA;aAChB;iBAAM;gBACH,OAAO,CAAC,OAAO,CAAC,CAAA;aACnB;YACD,QAAQ,CAAC,UAAU,EAAE,CAAA;SACxB,CAAA;QACD,IAAI,GAAG,EAAE;YACL,GAAG,CAAC,MAAM,GAAG;gBACT,IAAI,CAAC,IAAI,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAA;aACjD,CAAA;SACJ;QACD,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,KAAK,GAAG,UAAU,CAAC;gBACf,IAAI,CAAC,IAAI,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAA;aACjD,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA;SACtB;QACD,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK;YACvB,IAAI,EAAE,KAAK,YAAY,WAAW,CAAC,EAAE;gBACjC,IAAI,CAAC,KAAK,CAAC,CAAA;aACd;iBAAM;gBACH,SAAS,GAAG,KAAK,CAAA;aACpB;SACJ,CAAC,CAAA;QACF,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,OAAO;YAC7B,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;SAC3B,CAAC,CAAA;KACL,CAAC,CAAA;AACN;;AClDA,MAAM,UAAU,GAAG,UAAU,IAAI,MAAM,CAAA;AAgBvC;AACYE;AAAZ,WAAY,UAAU;;IAElB,mCAAqB,CAAA;;IAErB,qCAAuB,CAAA;AAC3B,CAAC,EALWA,kBAAU,KAAVA,kBAAU,QAKrB;AAQD;;;;;SAKsB,IAAI,CAAC,OAAiB,EAAE,OAAoB;;QAC9D,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAA;QAC/C,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;QACzE,MAAM,GAAG,GAAG,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE,CAAA;QAE3C,MAAM,OAAO,GAA2B,EAAE,CAAA;QAC1C,IAAI,OAAO,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;gBAClB,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAA;aACnE;YACD,OAAO,CAAC,aAAa,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAA;SAClE;aAAM,IAAI,OAAO,CAAC,OAAO,EAAE;YACxB,OAAO,CAAC,kBAAkB,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAA;SACvE;QAED,IAAI,IAAyB,CAAA;QAC7B,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,YAAY,UAAU,EAAE;YAC9D,IAAI,GAAG,OAAO,CAAA;SACjB;aAAM;YACH,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;SACjC;QACD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,EAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAC,CAAC,CAAA;QAElE,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;YACzC,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;gBACzB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;aAC/C;iBAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;gBAChC,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAA;aACvC;iBAAM;gBACH,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;aAC/D;SACJ;QAED,QAAQ,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAIA,kBAAU,CAAC,QAAQ,EAAe;KACxF;;;;;;;;;"}