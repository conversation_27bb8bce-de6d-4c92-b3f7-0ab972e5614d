'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var antelope = require('@wharfkit/antelope');

class Canceled extends Error {
    constructor(reason, silent = false) {
        super(reason);
        this.silent = false;
        this.silent = silent;
        Object.setPrototypeOf(this, Canceled.prototype);
    }
}
function cancelable(promise, onCancel) {
    let cancel = null;
    const cancelable = new Promise((resolve, reject) => {
        cancel = (reason = '', silent = false) => {
            try {
                if (onCancel) {
                    onCancel(new Canceled(reason, silent));
                }
            }
            catch (e) {
                reject(e);
            }
            return cancelable;
        };
        promise.then(resolve, reject);
    });
    if (cancel) {
        cancelable.cancel = cancel;
    }
    return cancelable;
}

function __decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
typeof SuppressedError === "function" ? SuppressedError : function (error, suppressed, message) {
    var e = new Error(message);
    return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
};

exports.ExplorerDefinition = class ExplorerDefinition extends antelope.Struct {
    url(id) {
        return `${this.prefix}${id}${this.suffix}`;
    }
};
__decorate([
    antelope.Struct.field('string')
], exports.ExplorerDefinition.prototype, "prefix", void 0);
__decorate([
    antelope.Struct.field('string')
], exports.ExplorerDefinition.prototype, "suffix", void 0);
exports.ExplorerDefinition = __decorate([
    antelope.Struct.type('explorer_definition')
], exports.ExplorerDefinition);

var Logo_1;
exports.Logo = Logo_1 = class Logo extends antelope.Struct {
    static from(data) {
        if (typeof data === 'string') {
            return new Logo_1({ light: data, dark: data });
        }
        return super.from(data);
    }
    getVariant(variant) {
        return this[variant];
    }
    toString() {
        return this.light;
    }
};
__decorate([
    antelope.Struct.field('string')
], exports.Logo.prototype, "dark", void 0);
__decorate([
    antelope.Struct.field('string')
], exports.Logo.prototype, "light", void 0);
exports.Logo = Logo_1 = __decorate([
    antelope.Struct.type('logo')
], exports.Logo);

exports.TokenIdentifier = class TokenIdentifier extends antelope.Struct {
};
__decorate([
    antelope.Struct.field(antelope.Checksum256)
], exports.TokenIdentifier.prototype, "chain", void 0);
__decorate([
    antelope.Struct.field(antelope.Name)
], exports.TokenIdentifier.prototype, "contract", void 0);
__decorate([
    antelope.Struct.field(antelope.Asset.Symbol)
], exports.TokenIdentifier.prototype, "symbol", void 0);
exports.TokenIdentifier = __decorate([
    antelope.Struct.type('token_identifier')
], exports.TokenIdentifier);
exports.TokenMeta = class TokenMeta extends antelope.Struct {
};
__decorate([
    antelope.Struct.field(exports.TokenIdentifier)
], exports.TokenMeta.prototype, "id", void 0);
__decorate([
    antelope.Struct.field('string', { optional: true })
], exports.TokenMeta.prototype, "logo", void 0);
exports.TokenMeta = __decorate([
    antelope.Struct.type('token_meta')
], exports.TokenMeta);
exports.TokenBalance = class TokenBalance extends antelope.Struct {
};
__decorate([
    antelope.Struct.field(antelope.Asset)
], exports.TokenBalance.prototype, "asset", void 0);
__decorate([
    antelope.Struct.field(antelope.Name)
], exports.TokenBalance.prototype, "contract", void 0);
__decorate([
    antelope.Struct.field(exports.TokenMeta)
], exports.TokenBalance.prototype, "metadata", void 0);
exports.TokenBalance = __decorate([
    antelope.Struct.type('token_balance')
], exports.TokenBalance);

class ChainDefinition {
    constructor(data) {
        this.id = antelope.Checksum256.from(data.id);
        this.url = data.url;
        this.logo = data.logo;
        this.explorer = data.explorer;
        this.accountDataType = data.accountDataType;
        this.coinType = data.coinType;
        if (data.systemTokenContract && data.systemTokenSymbol) {
            this.systemToken = exports.TokenIdentifier.from({
                chain: this.id,
                contract: data.systemTokenContract,
                symbol: data.systemTokenSymbol,
            });
        }
        if (data.systemToken) {
            this.systemToken = data.systemToken;
        }
    }
    static from(data) {
        return new ChainDefinition({
            ...data,
            explorer: data.explorer ? exports.ExplorerDefinition.from(data.explorer) : undefined,
            logo: data.logo ? exports.Logo.from(data.logo) : undefined,
        });
    }
    get name() {
        const indice = chainIdsToIndices.get(String(this.id));
        if (!indice) {
            return 'Unknown blockchain';
        }
        return ChainNames[indice];
    }
    getLogo() {
        const id = String(this.id);
        if (this.logo) {
            return exports.Logo.from(this.logo);
        }
        if (chainLogos.has(id)) {
            const logo = chainLogos.get(id);
            if (logo) {
                return exports.Logo.from(logo);
            }
        }
        return undefined;
    }
    equals(def) {
        const other = ChainDefinition.from(def);
        return this.id.equals(other.id) && this.url === other.url;
    }
}
const ChainNames = {
    EOS: 'EOS',
    FIO: 'FIO',
    FIOTestnet: 'FIO (Testnet)',
    Jungle4: 'Jungle 4 (Testnet)',
    KylinTestnet: 'Kylin (Testnet)',
    Libre: 'Libre',
    LibreTestnet: 'Libre (Testnet)',
    Proton: 'XPR Network',
    ProtonTestnet: 'XPR Network (Testnet)',
    Telos: 'Telos',
    TelosTestnet: 'Telos (Testnet)',
    UX: 'UX Network',
    Vaulta: 'Vaulta',
    WAX: 'WAX',
    WAXTestnet: 'WAX (Testnet)',
    XPR: 'XPR Network',
    XPRTestnet: 'XPR Network (Testnet)',
};
exports.TelosAccountVoterInfo = class TelosAccountVoterInfo extends antelope.API.v1.AccountVoterInfo {
};
__decorate([
    antelope.Struct.field(antelope.Int64)
], exports.TelosAccountVoterInfo.prototype, "last_stake", void 0);
exports.TelosAccountVoterInfo = __decorate([
    antelope.Struct.type('telos_account_voter_info')
], exports.TelosAccountVoterInfo);
exports.TelosAccountObject = class TelosAccountObject extends antelope.API.v1.AccountObject {
};
__decorate([
    antelope.Struct.field(exports.TelosAccountVoterInfo, { optional: true })
], exports.TelosAccountObject.prototype, "voter_info", void 0);
exports.TelosAccountObject = __decorate([
    antelope.Struct.type('telos_account_object')
], exports.TelosAccountObject);
exports.WAXAccountVoterInfo = class WAXAccountVoterInfo extends antelope.API.v1.AccountVoterInfo {
};
__decorate([
    antelope.Struct.field(antelope.Float64)
], exports.WAXAccountVoterInfo.prototype, "unpaid_voteshare", void 0);
__decorate([
    antelope.Struct.field(antelope.TimePoint)
], exports.WAXAccountVoterInfo.prototype, "unpaid_voteshare_last_updated", void 0);
__decorate([
    antelope.Struct.field(antelope.Float64)
], exports.WAXAccountVoterInfo.prototype, "unpaid_voteshare_change_rate", void 0);
__decorate([
    antelope.Struct.field(antelope.TimePoint)
], exports.WAXAccountVoterInfo.prototype, "last_claim_time", void 0);
exports.WAXAccountVoterInfo = __decorate([
    antelope.Struct.type('wax_account_voter_info')
], exports.WAXAccountVoterInfo);
exports.WAXAccountObject = class WAXAccountObject extends antelope.API.v1.AccountObject {
};
__decorate([
    antelope.Struct.field(exports.WAXAccountVoterInfo, { optional: true })
], exports.WAXAccountObject.prototype, "voter_info", void 0);
exports.WAXAccountObject = __decorate([
    antelope.Struct.type('wax_account_object')
], exports.WAXAccountObject);
exports.Chains = void 0;
(function (Chains) {
    Chains.EOS = ChainDefinition.from({
        id: 'aca376f206b8fc25a6ed44dbdc66547c36c6c33e3a119ffbeaef943642f0e906',
        url: 'https://eos.greymass.com',
        coinType: 194,
        systemTokenSymbol: '4,EOS',
        systemTokenContract: 'eosio.token',
    });
    Chains.FIO = ChainDefinition.from({
        id: '21dcae42c0182200e93f954a074011f9048a7624c6fe81d3c9541a614a88bd1c',
        url: 'https://fio.greymass.com',
        coinType: 235,
        systemTokenSymbol: '9,FIO',
        systemTokenContract: 'eosio.token',
    });
    Chains.FIOTestnet = ChainDefinition.from({
        id: 'b20901380af44ef59c5918439a1f9a41d83669020319a80574b804a5f95cbd7e',
        url: 'https://fiotestnet.greymass.com',
        systemTokenSymbol: '9,FIO',
        systemTokenContract: 'fio.token',
    });
    Chains.Jungle4 = ChainDefinition.from({
        id: '73e4385a2708e6d7048834fbc1079f2fabb17b3c125b146af438971e90716c4d',
        url: 'https://jungle4.greymass.com',
        coinType: 194,
        systemTokenSymbol: '4,EOS',
        systemTokenContract: 'eosio.token',
    });
    Chains.KylinTestnet = ChainDefinition.from({
        id: '5fff1dae8dc8e2fc4d5b23b2c7665c97f9e9d8edf2b6485a86ba311c25639191',
        url: 'https://kylintestnet.greymass.com',
        coinType: 194,
        systemTokenSymbol: '4,EOS',
        systemTokenContract: 'eosio.token',
    });
    Chains.Libre = ChainDefinition.from({
        id: '38b1d7815474d0c60683ecbea321d723e83f5da6ae5f1c1f9fecc69d9ba96465',
        url: 'https://libre.greymass.com',
        systemTokenSymbol: '4,LIBRE',
        systemTokenContract: 'eosio.token',
    });
    Chains.LibreTestnet = ChainDefinition.from({
        id: 'b64646740308df2ee06c6b72f34c0f7fa066d940e831f752db2006fcc2b78dee',
        url: 'https://libretestnet.greymass.com',
        systemTokenSymbol: '4,LIBRE',
        systemTokenContract: 'eosio.token',
    });
    Chains.Proton = ChainDefinition.from({
        id: '384da888112027f0321850a169f737c33e53b388aad48b5adace4bab97f437e0',
        url: 'https://proton.greymass.com',
        systemTokenSymbol: '4,XPR',
        systemTokenContract: 'eosio.token',
    });
    Chains.ProtonTestnet = ChainDefinition.from({
        id: '71ee83bcf52142d61019d95f9cc5427ba6a0d7ff8accd9e2088ae2abeaf3d3dd',
        url: 'https://proton-testnet.greymass.com',
        systemTokenSymbol: '4,XPR',
        systemTokenContract: 'eosio.token',
    });
    Chains.Telos = ChainDefinition.from({
        id: '4667b205c6838ef70ff7988f6e8257e8be0e1284a2f59699054a018f743b1d11',
        url: 'https://telos.greymass.com',
        accountDataType: exports.TelosAccountObject,
        coinType: 977,
        systemTokenSymbol: '4,TLOS',
        systemTokenContract: 'eosio.token',
    });
    Chains.TelosTestnet = ChainDefinition.from({
        id: '1eaa0824707c8c16bd25145493bf062aecddfeb56c736f6ba6397f3195f33c9f',
        url: 'https://telostestnet.greymass.com',
        accountDataType: exports.TelosAccountObject,
        coinType: 977,
        systemTokenSymbol: '4,TLOS',
        systemTokenContract: 'eosio.token',
    });
    Chains.UX = ChainDefinition.from({
        id: '8fc6dce7942189f842170de953932b1f66693ad3788f766e777b6f9d22335c02',
        url: 'https://api.uxnetwork.io',
        systemTokenSymbol: '4,UTX',
        systemTokenContract: 'eosio.token',
    });
    Chains.Vaulta = ChainDefinition.from({
        id: 'aca376f206b8fc25a6ed44dbdc66547c36c6c33e3a119ffbeaef943642f0e906',
        url: 'https://eos.greymass.com',
        coinType: 194,
        systemTokenSymbol: '4,A',
        systemTokenContract: 'core.vaulta',
    });
    Chains.WAX = ChainDefinition.from({
        id: '1064487b3cd1a897ce03ae5b6a865651747e2e152090f99c1d19d44e01aea5a4',
        url: 'https://wax.greymass.com',
        accountDataType: exports.WAXAccountObject,
        coinType: 14001,
        systemTokenSymbol: '8,WAX',
        systemTokenContract: 'eosio.token',
    });
    Chains.WAXTestnet = ChainDefinition.from({
        id: 'f16b1833c747c43682f4386fca9cbb327929334a762755ebec17f6f23c9b8a12',
        url: 'https://waxtestnet.greymass.com',
        accountDataType: exports.WAXAccountObject,
        coinType: 14001,
        systemTokenSymbol: '8,WAX',
        systemTokenContract: 'eosio.token',
    });
    Chains.XPR = ChainDefinition.from({
        id: '384da888112027f0321850a169f737c33e53b388aad48b5adace4bab97f437e0',
        url: 'https://proton.greymass.com',
        systemTokenSymbol: '4,XPR',
        systemTokenContract: 'eosio.token',
    });
    Chains.XPRTestnet = ChainDefinition.from({
        id: '71ee83bcf52142d61019d95f9cc5427ba6a0d7ff8accd9e2088ae2abeaf3d3dd',
        url: 'https://proton-testnet.greymass.com',
        systemTokenSymbol: '4,XPR',
        systemTokenContract: 'eosio.token',
    });
})(exports.Chains || (exports.Chains = {}));
const chainIdsToIndices = new Map([
    ['21dcae42c0182200e93f954a074011f9048a7624c6fe81d3c9541a614a88bd1c', 'FIO'],
    ['b20901380af44ef59c5918439a1f9a41d83669020319a80574b804a5f95cbd7e', 'FIOTestnet'],
    ['73e4385a2708e6d7048834fbc1079f2fabb17b3c125b146af438971e90716c4d', 'Jungle4'],
    ['5fff1dae8dc8e2fc4d5b23b2c7665c97f9e9d8edf2b6485a86ba311c25639191', 'KylinTestnet'],
    ['38b1d7815474d0c60683ecbea321d723e83f5da6ae5f1c1f9fecc69d9ba96465', 'Libre'],
    ['b64646740308df2ee06c6b72f34c0f7fa066d940e831f752db2006fcc2b78dee', 'LibreTestnet'],
    ['384da888112027f0321850a169f737c33e53b388aad48b5adace4bab97f437e0', 'XPR'],
    ['71ee83bcf52142d61019d95f9cc5427ba6a0d7ff8accd9e2088ae2abeaf3d3dd', 'XPRTestnet'],
    ['4667b205c6838ef70ff7988f6e8257e8be0e1284a2f59699054a018f743b1d11', 'Telos'],
    ['1eaa0824707c8c16bd25145493bf062aecddfeb56c736f6ba6397f3195f33c9f', 'TelosTestnet'],
    ['8fc6dce7942189f842170de953932b1f66693ad3788f766e777b6f9d22335c02', 'UX'],
    ['aca376f206b8fc25a6ed44dbdc66547c36c6c33e3a119ffbeaef943642f0e906', 'Vaulta'],
    ['1064487b3cd1a897ce03ae5b6a865651747e2e152090f99c1d19d44e01aea5a4', 'WAX'],
    ['f16b1833c747c43682f4386fca9cbb327929334a762755ebec17f6f23c9b8a12', 'WAXTestnet'],
]);
const chainLogos = new Map([
    [
        '21dcae42c0182200e93f954a074011f9048a7624c6fe81d3c9541a614a88bd1c',
        'https://assets.wharfkit.com/chain/fio.png',
    ],
    [
        'b20901380af44ef59c5918439a1f9a41d83669020319a80574b804a5f95cbd7e',
        'https://assets.wharfkit.com/chain/fio.png',
    ],
    [
        '2a02a0053e5a8cf73a56ba0fda11e4d92e0238a4a2aa74fccf46d5a910746840',
        'https://assets.wharfkit.com/chain/jungle.png',
    ],
    [
        '73e4385a2708e6d7048834fbc1079f2fabb17b3c125b146af438971e90716c4d',
        'https://assets.wharfkit.com/chain/jungle.png',
    ],
    [
        '38b1d7815474d0c60683ecbea321d723e83f5da6ae5f1c1f9fecc69d9ba96465',
        'https://assets.wharfkit.com/chain/libre.png',
    ],
    [
        'b64646740308df2ee06c6b72f34c0f7fa066d940e831f752db2006fcc2b78dee',
        'https://assets.wharfkit.com/chain/libre.png',
    ],
    [
        '384da888112027f0321850a169f737c33e53b388aad48b5adace4bab97f437e0',
        'https://assets.wharfkit.com/chain/xprnetwork.png',
    ],
    [
        '71ee83bcf52142d61019d95f9cc5427ba6a0d7ff8accd9e2088ae2abeaf3d3dd',
        'https://assets.wharfkit.com/chain/xprnetwork.png',
    ],
    [
        '4667b205c6838ef70ff7988f6e8257e8be0e1284a2f59699054a018f743b1d11',
        'https://assets.wharfkit.com/chain/telos.png',
    ],
    [
        '1eaa0824707c8c16bd25145493bf062aecddfeb56c736f6ba6397f3195f33c9f',
        'https://assets.wharfkit.com/chain/telos.png',
    ],
    [
        'aca376f206b8fc25a6ed44dbdc66547c36c6c33e3a119ffbeaef943642f0e906',
        'https://assets.wharfkit.com/chain/vaulta.png',
    ],
    [
        '8fc6dce7942189f842170de953932b1f66693ad3788f766e777b6f9d22335c02',
        'https://assets.wharfkit.com/chain/ux.png',
    ],
    [
        '1064487b3cd1a897ce03ae5b6a865651747e2e152090f99c1d19d44e01aea5a4',
        'https://assets.wharfkit.com/chain/wax.png',
    ],
    [
        'f16b1833c747c43682f4386fca9cbb327929334a762755ebec17f6f23c9b8a12',
        'https://assets.wharfkit.com/chain/wax.png',
    ],
]);

exports.Canceled = Canceled;
exports.ChainDefinition = ChainDefinition;
exports.ChainNames = ChainNames;
exports.cancelable = cancelable;
exports.chainIdsToIndices = chainIdsToIndices;
exports.chainLogos = chainLogos;
//# sourceMappingURL=common.js.map
