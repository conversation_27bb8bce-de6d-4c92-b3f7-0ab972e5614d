Notifications
AtomicAssets allows you to receive on-chain notifications for all actions related to a collection by adding the accounts that should receive these notifications to the notify_accounts list of a collection. This allows smart contracts to react to things like asset transfers and burns and is especially useful for applications that associate some functionalities to the ownership of assets.

The notifications are implemented using the require_recipient() function. Therefore, unlike with deferred transactions, smart contracts are 100% guaranteed to receive notifications. This also means that the notified accounts could make any actions throw. We believe that this is not an inherently bad thing, however it means that collection authors should be careful only to add trusted accounts to the notify_accounts list.

How to receive notifications
Notifications are handled on a per collection basis. Any account within the notify_accounts value within a collections table entry will receive notifications for this collection.

Accounts can be added and removed from the list using the addnotifyacc and remnotifyacc actions.

Which actions are you notified of
The log actions are not called directly from the outside, but are called inline by the atomicassets contract when their respective original actions are called. They contain more information in their parameters than the respective original action.

logtransfer
ACTION logtransfer(
  name collection_name,
  name from,
  name to,
  vector<uint64_t> asset_ids,
  string memo
);
logtransfer is called when a transfer action (including accepted offers) transfers at least one item of the subscribed collection. The asset_ids vector only includes the assets of the subsribed collection.

logmint
ACTION logmint(
  uint64_t asset_id,
  name authorized_minter,
  name collection_name,
  name schema_name,
  int32_t template_id,
  name new_asset_owner,
  ATTRIBUTE_MAP immutable_data,
  ATTRIBUTE_MAP mutable_data,
  vector<asset> tokens_to_back
);
logburnasset
ACTION logburnasset(
  name asset_owner,
  uint64_t asset_id,
  name collection_name,
  name schema_name,
  int32_t template_id,
  vector<asset> backed_tokens,
  ATTRIBUTE_MAP old_immutable_data,
  ATTRIBUTE_MAP old_mutable_data,
  name asset_ram_payer
);
All the asset's data is also copied in the parameters, because smart contracts will only be notified after the asset has already been deleted and would therefore not be able to retrieve this data.

logbackasset
ACTION logbackasset(
  name asset_owner,
  uint64_t asset_id,
  asset backed_token;
);
logsetdata
ACTION logsetdata(
  name asset_owner,
  uint64_t asset_id,
  ATTRIBUTE_MAP old_data,
  ATTRIBUTE_MAP new_data
);
The old_data is also included because it would otherwise not be accessible to the smart contracts notified.

lognewtempl
ACTION lognewtempl(
  int32_t template_id;
  name authorized_creator,
  name collection_name,
  name schema_name,
  bool transferable,
  bool burnable,
  uint32_t max_supply,
  ATTRIBUTE_MAP immutable_data
);