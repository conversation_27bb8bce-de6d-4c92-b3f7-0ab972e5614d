import * as _wharfkit_antelope from '@wharfkit/antelope';
import { Struct, Checksum256Type, Asset, NameType, AssetType, Checksum256, Name, API, Int64, Float64, TimePoint } from '@wharfkit/antelope';

/**
 * Cancelable promises
 *
 * https://stackoverflow.com/questions/46461801/possible-to-add-a-cancel-method-to-promise-in-typescript/46464377#46464377
 */
declare class Canceled extends Error {
    silent: boolean;
    constructor(reason: any, silent?: boolean);
}
interface Cancelable<T> extends Promise<T> {
    cancel(reason?: string, silent?: boolean): Cancelable<T>;
}
declare function cancelable<T>(promise: Promise<T>, onCancel?: (canceled: Canceled) => void): Cancelable<T>;

declare class ExplorerDefinition extends Struct {
    prefix: string;
    suffix: string;
    url(id: string): string;
}

type Fetch = (input: any, init?: any) => Promise<any>;
type LogoType = Logo | {
    dark: string;
    light: string;
} | string;
type ExplorerDefinitionType = ExplorerDefinition | {
    prefix: string;
    suffix: string;
    url?: (id: string) => string;
};
type ChainDefinitionType = ChainDefinition | {
    id: Checksum256Type;
    url: string;
    explorer?: ExplorerDefinitionType;
    logo?: LogoType;
    systemTokenSymbol?: Asset.SymbolType;
    systemTokenContract?: NameType;
};
type LocaleDefinitions = Record<string, any>;

declare class Logo extends Struct {
    dark: string;
    light: string;
    static from(data: LogoType): Logo;
    getVariant(variant: 'dark' | 'light'): string | undefined;
    toString(): string;
}

interface TokenBalanceType {
    asset: AssetType;
    contract: NameType;
    metadata: TokenMetaType;
}
interface TokenIdentifierType {
    chain: Checksum256Type;
    contract: NameType;
    symbol: Asset.SymbolType;
}
interface TokenMetaType {
    id: TokenIdentifierType;
    logo?: string;
}
declare class TokenIdentifier extends Struct {
    chain: Checksum256;
    contract: Name;
    symbol: Asset.Symbol;
}
declare class TokenMeta extends Struct {
    id: TokenIdentifier;
    logo?: string;
}
declare class TokenBalance extends Struct {
    asset: Asset;
    contract: Name;
    metadata: TokenMeta;
}

interface ChainDefinitionArgs {
    id: Checksum256Type;
    url: string;
    logo?: LogoType;
    explorer?: ExplorerDefinitionType;
    accountDataType?: typeof API.v1.AccountObject;
    coinType?: number;
    systemToken?: TokenIdentifier;
    systemTokenSymbol?: Asset.SymbolType;
    systemTokenContract?: NameType;
}
/**
 * The information required to interact with a given chain.
 */
declare class ChainDefinition<AccountDataType extends API.v1.AccountObject = API.v1.AccountObject> {
    /**
     * The chain ID.
     */
    id: Checksum256;
    /**
     * The base URL of the chain's API endpoint (e.g. https://jungle4.greymass.com).
     */
    url: string;
    /**
     * The absolute URL(s) to the chain's logo.
     */
    logo?: LogoType;
    /**
     * The explorer definition for the chain.
     */
    explorer?: ExplorerDefinitionType;
    /**
     * The account data type for the chain.
     */
    accountDataType?: typeof API.v1.AccountObject;
    /**
     *  The SLIP-44 coin type for the chain.
     */
    coinType?: number;
    /**
     * The system token symbol for the chain.
     */
    systemToken?: TokenIdentifier;
    constructor(data: ChainDefinitionArgs);
    static from<AccountDataType extends API.v1.AccountObject = API.v1.AccountObject>(data: ChainDefinitionArgs): ChainDefinition<AccountDataType>;
    get name(): string;
    getLogo(): Logo | undefined;
    equals(def: ChainDefinitionType): boolean;
}
/**
 * A list of string-based chain names to assist autocompletion
 */
type ChainIndices = 'EOS' | 'FIO' | 'FIOTestnet' | 'Jungle4' | 'KylinTestnet' | 'Libre' | 'LibreTestnet' | 'Proton' | 'ProtonTestnet' | 'Telos' | 'TelosTestnet' | 'Vaulta' | 'UX' | 'WAX' | 'WAXTestnet' | 'XPR' | 'XPRTestnet';
/**
 * List of human readable chain names based on the ChainIndices type.
 */
declare const ChainNames: Record<ChainIndices, string>;
declare class TelosAccountVoterInfo extends API.v1.AccountVoterInfo {
    last_stake: Int64;
}
declare class TelosAccountObject extends API.v1.AccountObject {
    voter_info?: TelosAccountVoterInfo;
}
declare class WAXAccountVoterInfo extends API.v1.AccountVoterInfo {
    unpaid_voteshare: Float64;
    unpaid_voteshare_last_updated: TimePoint;
    unpaid_voteshare_change_rate: Float64;
    last_claim_time: TimePoint;
}
declare class WAXAccountObject extends API.v1.AccountObject {
    voter_info?: WAXAccountVoterInfo;
}
/**
 * An exported list of ChainDefinition entries for select chains.
 */
declare namespace Chains {
    const EOS: ChainDefinition<{
        account_name: _wharfkit_antelope.Name;
        head_block_num: _wharfkit_antelope.UInt32;
        head_block_time: TimePoint;
        privileged: boolean;
        last_code_update: TimePoint;
        created: TimePoint;
        core_liquid_balance?: Asset | undefined;
        ram_quota: Int64;
        net_weight: Int64;
        cpu_weight: Int64;
        net_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        cpu_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        subjective_cpu_bill_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        ram_usage: _wharfkit_antelope.UInt64;
        permissions: {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        }[];
        total_resources: {
            owner: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            ram_bytes: _wharfkit_antelope.UInt64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        self_delegated_bandwidth?: {
            from: _wharfkit_antelope.Name;
            to: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        refund_request?: {
            owner: _wharfkit_antelope.Name;
            request_time: TimePoint;
            net_amount: Asset;
            cpu_amount: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        voter_info?: {
            owner: _wharfkit_antelope.Name;
            proxy: _wharfkit_antelope.Name;
            producers: _wharfkit_antelope.Name[];
            staked?: Int64 | undefined;
            last_vote_weight: Float64;
            proxied_vote_weight: Float64;
            is_proxy: boolean;
            flags1?: _wharfkit_antelope.UInt32 | undefined;
            reserved2: _wharfkit_antelope.UInt32;
            reserved3: string;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        rex_info?: {
            version: _wharfkit_antelope.UInt32;
            owner: _wharfkit_antelope.Name;
            vote_stake: Asset;
            rex_balance: Asset;
            matured_rex: Int64;
            rex_maturities: {
                key?: TimePoint | undefined;
                value?: Int64 | undefined;
                first?: TimePoint | undefined;
                second?: Int64 | undefined;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        getPermission(permission: NameType): {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        };
        equals(other: any): boolean;
        toJSON(): any;
    }>;
    const FIO: ChainDefinition<{
        account_name: _wharfkit_antelope.Name;
        head_block_num: _wharfkit_antelope.UInt32;
        head_block_time: TimePoint;
        privileged: boolean;
        last_code_update: TimePoint;
        created: TimePoint;
        core_liquid_balance?: Asset | undefined;
        ram_quota: Int64;
        net_weight: Int64;
        cpu_weight: Int64;
        net_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        cpu_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        subjective_cpu_bill_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        ram_usage: _wharfkit_antelope.UInt64;
        permissions: {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        }[];
        total_resources: {
            owner: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            ram_bytes: _wharfkit_antelope.UInt64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        self_delegated_bandwidth?: {
            from: _wharfkit_antelope.Name;
            to: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        refund_request?: {
            owner: _wharfkit_antelope.Name;
            request_time: TimePoint;
            net_amount: Asset;
            cpu_amount: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        voter_info?: {
            owner: _wharfkit_antelope.Name;
            proxy: _wharfkit_antelope.Name;
            producers: _wharfkit_antelope.Name[];
            staked?: Int64 | undefined;
            last_vote_weight: Float64;
            proxied_vote_weight: Float64;
            is_proxy: boolean;
            flags1?: _wharfkit_antelope.UInt32 | undefined;
            reserved2: _wharfkit_antelope.UInt32;
            reserved3: string;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        rex_info?: {
            version: _wharfkit_antelope.UInt32;
            owner: _wharfkit_antelope.Name;
            vote_stake: Asset;
            rex_balance: Asset;
            matured_rex: Int64;
            rex_maturities: {
                key?: TimePoint | undefined;
                value?: Int64 | undefined;
                first?: TimePoint | undefined;
                second?: Int64 | undefined;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        getPermission(permission: NameType): {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        };
        equals(other: any): boolean;
        toJSON(): any;
    }>;
    const FIOTestnet: ChainDefinition<{
        account_name: _wharfkit_antelope.Name;
        head_block_num: _wharfkit_antelope.UInt32;
        head_block_time: TimePoint;
        privileged: boolean;
        last_code_update: TimePoint;
        created: TimePoint;
        core_liquid_balance?: Asset | undefined;
        ram_quota: Int64;
        net_weight: Int64;
        cpu_weight: Int64;
        net_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        cpu_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        subjective_cpu_bill_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        ram_usage: _wharfkit_antelope.UInt64;
        permissions: {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        }[];
        total_resources: {
            owner: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            ram_bytes: _wharfkit_antelope.UInt64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        self_delegated_bandwidth?: {
            from: _wharfkit_antelope.Name;
            to: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        refund_request?: {
            owner: _wharfkit_antelope.Name;
            request_time: TimePoint;
            net_amount: Asset;
            cpu_amount: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        voter_info?: {
            owner: _wharfkit_antelope.Name;
            proxy: _wharfkit_antelope.Name;
            producers: _wharfkit_antelope.Name[];
            staked?: Int64 | undefined;
            last_vote_weight: Float64;
            proxied_vote_weight: Float64;
            is_proxy: boolean;
            flags1?: _wharfkit_antelope.UInt32 | undefined;
            reserved2: _wharfkit_antelope.UInt32;
            reserved3: string;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        rex_info?: {
            version: _wharfkit_antelope.UInt32;
            owner: _wharfkit_antelope.Name;
            vote_stake: Asset;
            rex_balance: Asset;
            matured_rex: Int64;
            rex_maturities: {
                key?: TimePoint | undefined;
                value?: Int64 | undefined;
                first?: TimePoint | undefined;
                second?: Int64 | undefined;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        getPermission(permission: NameType): {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        };
        equals(other: any): boolean;
        toJSON(): any;
    }>;
    const Jungle4: ChainDefinition<{
        account_name: _wharfkit_antelope.Name;
        head_block_num: _wharfkit_antelope.UInt32;
        head_block_time: TimePoint;
        privileged: boolean;
        last_code_update: TimePoint;
        created: TimePoint;
        core_liquid_balance?: Asset | undefined;
        ram_quota: Int64;
        net_weight: Int64;
        cpu_weight: Int64;
        net_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        cpu_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        subjective_cpu_bill_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        ram_usage: _wharfkit_antelope.UInt64;
        permissions: {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        }[];
        total_resources: {
            owner: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            ram_bytes: _wharfkit_antelope.UInt64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        self_delegated_bandwidth?: {
            from: _wharfkit_antelope.Name;
            to: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        refund_request?: {
            owner: _wharfkit_antelope.Name;
            request_time: TimePoint;
            net_amount: Asset;
            cpu_amount: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        voter_info?: {
            owner: _wharfkit_antelope.Name;
            proxy: _wharfkit_antelope.Name;
            producers: _wharfkit_antelope.Name[];
            staked?: Int64 | undefined;
            last_vote_weight: Float64;
            proxied_vote_weight: Float64;
            is_proxy: boolean;
            flags1?: _wharfkit_antelope.UInt32 | undefined;
            reserved2: _wharfkit_antelope.UInt32;
            reserved3: string;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        rex_info?: {
            version: _wharfkit_antelope.UInt32;
            owner: _wharfkit_antelope.Name;
            vote_stake: Asset;
            rex_balance: Asset;
            matured_rex: Int64;
            rex_maturities: {
                key?: TimePoint | undefined;
                value?: Int64 | undefined;
                first?: TimePoint | undefined;
                second?: Int64 | undefined;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        getPermission(permission: NameType): {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        };
        equals(other: any): boolean;
        toJSON(): any;
    }>;
    const KylinTestnet: ChainDefinition<{
        account_name: _wharfkit_antelope.Name;
        head_block_num: _wharfkit_antelope.UInt32;
        head_block_time: TimePoint;
        privileged: boolean;
        last_code_update: TimePoint;
        created: TimePoint;
        core_liquid_balance?: Asset | undefined;
        ram_quota: Int64;
        net_weight: Int64;
        cpu_weight: Int64;
        net_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        cpu_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        subjective_cpu_bill_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        ram_usage: _wharfkit_antelope.UInt64;
        permissions: {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        }[];
        total_resources: {
            owner: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            ram_bytes: _wharfkit_antelope.UInt64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        self_delegated_bandwidth?: {
            from: _wharfkit_antelope.Name;
            to: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        refund_request?: {
            owner: _wharfkit_antelope.Name;
            request_time: TimePoint;
            net_amount: Asset;
            cpu_amount: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        voter_info?: {
            owner: _wharfkit_antelope.Name;
            proxy: _wharfkit_antelope.Name;
            producers: _wharfkit_antelope.Name[];
            staked?: Int64 | undefined;
            last_vote_weight: Float64;
            proxied_vote_weight: Float64;
            is_proxy: boolean;
            flags1?: _wharfkit_antelope.UInt32 | undefined;
            reserved2: _wharfkit_antelope.UInt32;
            reserved3: string;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        rex_info?: {
            version: _wharfkit_antelope.UInt32;
            owner: _wharfkit_antelope.Name;
            vote_stake: Asset;
            rex_balance: Asset;
            matured_rex: Int64;
            rex_maturities: {
                key?: TimePoint | undefined;
                value?: Int64 | undefined;
                first?: TimePoint | undefined;
                second?: Int64 | undefined;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        getPermission(permission: NameType): {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        };
        equals(other: any): boolean;
        toJSON(): any;
    }>;
    const Libre: ChainDefinition<{
        account_name: _wharfkit_antelope.Name;
        head_block_num: _wharfkit_antelope.UInt32;
        head_block_time: TimePoint;
        privileged: boolean;
        last_code_update: TimePoint;
        created: TimePoint;
        core_liquid_balance?: Asset | undefined;
        ram_quota: Int64;
        net_weight: Int64;
        cpu_weight: Int64;
        net_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        cpu_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        subjective_cpu_bill_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        ram_usage: _wharfkit_antelope.UInt64;
        permissions: {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        }[];
        total_resources: {
            owner: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            ram_bytes: _wharfkit_antelope.UInt64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        self_delegated_bandwidth?: {
            from: _wharfkit_antelope.Name;
            to: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        refund_request?: {
            owner: _wharfkit_antelope.Name;
            request_time: TimePoint;
            net_amount: Asset;
            cpu_amount: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        voter_info?: {
            owner: _wharfkit_antelope.Name;
            proxy: _wharfkit_antelope.Name;
            producers: _wharfkit_antelope.Name[];
            staked?: Int64 | undefined;
            last_vote_weight: Float64;
            proxied_vote_weight: Float64;
            is_proxy: boolean;
            flags1?: _wharfkit_antelope.UInt32 | undefined;
            reserved2: _wharfkit_antelope.UInt32;
            reserved3: string;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        rex_info?: {
            version: _wharfkit_antelope.UInt32;
            owner: _wharfkit_antelope.Name;
            vote_stake: Asset;
            rex_balance: Asset;
            matured_rex: Int64;
            rex_maturities: {
                key?: TimePoint | undefined;
                value?: Int64 | undefined;
                first?: TimePoint | undefined;
                second?: Int64 | undefined;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        getPermission(permission: NameType): {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        };
        equals(other: any): boolean;
        toJSON(): any;
    }>;
    const LibreTestnet: ChainDefinition<{
        account_name: _wharfkit_antelope.Name;
        head_block_num: _wharfkit_antelope.UInt32;
        head_block_time: TimePoint;
        privileged: boolean;
        last_code_update: TimePoint;
        created: TimePoint;
        core_liquid_balance?: Asset | undefined;
        ram_quota: Int64;
        net_weight: Int64;
        cpu_weight: Int64;
        net_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        cpu_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        subjective_cpu_bill_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        ram_usage: _wharfkit_antelope.UInt64;
        permissions: {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        }[];
        total_resources: {
            owner: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            ram_bytes: _wharfkit_antelope.UInt64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        self_delegated_bandwidth?: {
            from: _wharfkit_antelope.Name;
            to: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        refund_request?: {
            owner: _wharfkit_antelope.Name;
            request_time: TimePoint;
            net_amount: Asset;
            cpu_amount: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        voter_info?: {
            owner: _wharfkit_antelope.Name;
            proxy: _wharfkit_antelope.Name;
            producers: _wharfkit_antelope.Name[];
            staked?: Int64 | undefined;
            last_vote_weight: Float64;
            proxied_vote_weight: Float64;
            is_proxy: boolean;
            flags1?: _wharfkit_antelope.UInt32 | undefined;
            reserved2: _wharfkit_antelope.UInt32;
            reserved3: string;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        rex_info?: {
            version: _wharfkit_antelope.UInt32;
            owner: _wharfkit_antelope.Name;
            vote_stake: Asset;
            rex_balance: Asset;
            matured_rex: Int64;
            rex_maturities: {
                key?: TimePoint | undefined;
                value?: Int64 | undefined;
                first?: TimePoint | undefined;
                second?: Int64 | undefined;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        getPermission(permission: NameType): {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        };
        equals(other: any): boolean;
        toJSON(): any;
    }>;
    const Proton: ChainDefinition<{
        account_name: _wharfkit_antelope.Name;
        head_block_num: _wharfkit_antelope.UInt32;
        head_block_time: TimePoint;
        privileged: boolean;
        last_code_update: TimePoint;
        created: TimePoint;
        core_liquid_balance?: Asset | undefined;
        ram_quota: Int64;
        net_weight: Int64;
        cpu_weight: Int64;
        net_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        cpu_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        subjective_cpu_bill_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        ram_usage: _wharfkit_antelope.UInt64;
        permissions: {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        }[];
        total_resources: {
            owner: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            ram_bytes: _wharfkit_antelope.UInt64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        self_delegated_bandwidth?: {
            from: _wharfkit_antelope.Name;
            to: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        refund_request?: {
            owner: _wharfkit_antelope.Name;
            request_time: TimePoint;
            net_amount: Asset;
            cpu_amount: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        voter_info?: {
            owner: _wharfkit_antelope.Name;
            proxy: _wharfkit_antelope.Name;
            producers: _wharfkit_antelope.Name[];
            staked?: Int64 | undefined;
            last_vote_weight: Float64;
            proxied_vote_weight: Float64;
            is_proxy: boolean;
            flags1?: _wharfkit_antelope.UInt32 | undefined;
            reserved2: _wharfkit_antelope.UInt32;
            reserved3: string;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        rex_info?: {
            version: _wharfkit_antelope.UInt32;
            owner: _wharfkit_antelope.Name;
            vote_stake: Asset;
            rex_balance: Asset;
            matured_rex: Int64;
            rex_maturities: {
                key?: TimePoint | undefined;
                value?: Int64 | undefined;
                first?: TimePoint | undefined;
                second?: Int64 | undefined;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        getPermission(permission: NameType): {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        };
        equals(other: any): boolean;
        toJSON(): any;
    }>;
    const ProtonTestnet: ChainDefinition<{
        account_name: _wharfkit_antelope.Name;
        head_block_num: _wharfkit_antelope.UInt32;
        head_block_time: TimePoint;
        privileged: boolean;
        last_code_update: TimePoint;
        created: TimePoint;
        core_liquid_balance?: Asset | undefined;
        ram_quota: Int64;
        net_weight: Int64;
        cpu_weight: Int64;
        net_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        cpu_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        subjective_cpu_bill_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        ram_usage: _wharfkit_antelope.UInt64;
        permissions: {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        }[];
        total_resources: {
            owner: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            ram_bytes: _wharfkit_antelope.UInt64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        self_delegated_bandwidth?: {
            from: _wharfkit_antelope.Name;
            to: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        refund_request?: {
            owner: _wharfkit_antelope.Name;
            request_time: TimePoint;
            net_amount: Asset;
            cpu_amount: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        voter_info?: {
            owner: _wharfkit_antelope.Name;
            proxy: _wharfkit_antelope.Name;
            producers: _wharfkit_antelope.Name[];
            staked?: Int64 | undefined;
            last_vote_weight: Float64;
            proxied_vote_weight: Float64;
            is_proxy: boolean;
            flags1?: _wharfkit_antelope.UInt32 | undefined;
            reserved2: _wharfkit_antelope.UInt32;
            reserved3: string;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        rex_info?: {
            version: _wharfkit_antelope.UInt32;
            owner: _wharfkit_antelope.Name;
            vote_stake: Asset;
            rex_balance: Asset;
            matured_rex: Int64;
            rex_maturities: {
                key?: TimePoint | undefined;
                value?: Int64 | undefined;
                first?: TimePoint | undefined;
                second?: Int64 | undefined;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        getPermission(permission: NameType): {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        };
        equals(other: any): boolean;
        toJSON(): any;
    }>;
    const Telos: ChainDefinition<TelosAccountObject>;
    const TelosTestnet: ChainDefinition<TelosAccountObject>;
    const UX: ChainDefinition<{
        account_name: _wharfkit_antelope.Name;
        head_block_num: _wharfkit_antelope.UInt32;
        head_block_time: TimePoint;
        privileged: boolean;
        last_code_update: TimePoint;
        created: TimePoint;
        core_liquid_balance?: Asset | undefined;
        ram_quota: Int64;
        net_weight: Int64;
        cpu_weight: Int64;
        net_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        cpu_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        subjective_cpu_bill_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        ram_usage: _wharfkit_antelope.UInt64;
        permissions: {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        }[];
        total_resources: {
            owner: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            ram_bytes: _wharfkit_antelope.UInt64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        self_delegated_bandwidth?: {
            from: _wharfkit_antelope.Name;
            to: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        refund_request?: {
            owner: _wharfkit_antelope.Name;
            request_time: TimePoint;
            net_amount: Asset;
            cpu_amount: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        voter_info?: {
            owner: _wharfkit_antelope.Name;
            proxy: _wharfkit_antelope.Name;
            producers: _wharfkit_antelope.Name[];
            staked?: Int64 | undefined;
            last_vote_weight: Float64;
            proxied_vote_weight: Float64;
            is_proxy: boolean;
            flags1?: _wharfkit_antelope.UInt32 | undefined;
            reserved2: _wharfkit_antelope.UInt32;
            reserved3: string;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        rex_info?: {
            version: _wharfkit_antelope.UInt32;
            owner: _wharfkit_antelope.Name;
            vote_stake: Asset;
            rex_balance: Asset;
            matured_rex: Int64;
            rex_maturities: {
                key?: TimePoint | undefined;
                value?: Int64 | undefined;
                first?: TimePoint | undefined;
                second?: Int64 | undefined;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        getPermission(permission: NameType): {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        };
        equals(other: any): boolean;
        toJSON(): any;
    }>;
    const Vaulta: ChainDefinition<{
        account_name: _wharfkit_antelope.Name;
        head_block_num: _wharfkit_antelope.UInt32;
        head_block_time: TimePoint;
        privileged: boolean;
        last_code_update: TimePoint;
        created: TimePoint;
        core_liquid_balance?: Asset | undefined;
        ram_quota: Int64;
        net_weight: Int64;
        cpu_weight: Int64;
        net_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        cpu_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        subjective_cpu_bill_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        ram_usage: _wharfkit_antelope.UInt64;
        permissions: {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        }[];
        total_resources: {
            owner: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            ram_bytes: _wharfkit_antelope.UInt64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        self_delegated_bandwidth?: {
            from: _wharfkit_antelope.Name;
            to: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        refund_request?: {
            owner: _wharfkit_antelope.Name;
            request_time: TimePoint;
            net_amount: Asset;
            cpu_amount: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        voter_info?: {
            owner: _wharfkit_antelope.Name;
            proxy: _wharfkit_antelope.Name;
            producers: _wharfkit_antelope.Name[];
            staked?: Int64 | undefined;
            last_vote_weight: Float64;
            proxied_vote_weight: Float64;
            is_proxy: boolean;
            flags1?: _wharfkit_antelope.UInt32 | undefined;
            reserved2: _wharfkit_antelope.UInt32;
            reserved3: string;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        rex_info?: {
            version: _wharfkit_antelope.UInt32;
            owner: _wharfkit_antelope.Name;
            vote_stake: Asset;
            rex_balance: Asset;
            matured_rex: Int64;
            rex_maturities: {
                key?: TimePoint | undefined;
                value?: Int64 | undefined;
                first?: TimePoint | undefined;
                second?: Int64 | undefined;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        getPermission(permission: NameType): {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        };
        equals(other: any): boolean;
        toJSON(): any;
    }>;
    const WAX: ChainDefinition<WAXAccountObject>;
    const WAXTestnet: ChainDefinition<WAXAccountObject>;
    const XPR: ChainDefinition<{
        account_name: _wharfkit_antelope.Name;
        head_block_num: _wharfkit_antelope.UInt32;
        head_block_time: TimePoint;
        privileged: boolean;
        last_code_update: TimePoint;
        created: TimePoint;
        core_liquid_balance?: Asset | undefined;
        ram_quota: Int64;
        net_weight: Int64;
        cpu_weight: Int64;
        net_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        cpu_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        subjective_cpu_bill_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        ram_usage: _wharfkit_antelope.UInt64;
        permissions: {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        }[];
        total_resources: {
            owner: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            ram_bytes: _wharfkit_antelope.UInt64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        self_delegated_bandwidth?: {
            from: _wharfkit_antelope.Name;
            to: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        refund_request?: {
            owner: _wharfkit_antelope.Name;
            request_time: TimePoint;
            net_amount: Asset;
            cpu_amount: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        voter_info?: {
            owner: _wharfkit_antelope.Name;
            proxy: _wharfkit_antelope.Name;
            producers: _wharfkit_antelope.Name[];
            staked?: Int64 | undefined;
            last_vote_weight: Float64;
            proxied_vote_weight: Float64;
            is_proxy: boolean;
            flags1?: _wharfkit_antelope.UInt32 | undefined;
            reserved2: _wharfkit_antelope.UInt32;
            reserved3: string;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        rex_info?: {
            version: _wharfkit_antelope.UInt32;
            owner: _wharfkit_antelope.Name;
            vote_stake: Asset;
            rex_balance: Asset;
            matured_rex: Int64;
            rex_maturities: {
                key?: TimePoint | undefined;
                value?: Int64 | undefined;
                first?: TimePoint | undefined;
                second?: Int64 | undefined;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        getPermission(permission: NameType): {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        };
        equals(other: any): boolean;
        toJSON(): any;
    }>;
    const XPRTestnet: ChainDefinition<{
        account_name: _wharfkit_antelope.Name;
        head_block_num: _wharfkit_antelope.UInt32;
        head_block_time: TimePoint;
        privileged: boolean;
        last_code_update: TimePoint;
        created: TimePoint;
        core_liquid_balance?: Asset | undefined;
        ram_quota: Int64;
        net_weight: Int64;
        cpu_weight: Int64;
        net_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        cpu_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        subjective_cpu_bill_limit: {
            used: Int64;
            available: Int64;
            max: Int64;
            last_usage_update_time: TimePoint;
            current_used: Int64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        ram_usage: _wharfkit_antelope.UInt64;
        permissions: {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        }[];
        total_resources: {
            owner: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            ram_bytes: _wharfkit_antelope.UInt64;
            equals(other: any): boolean;
            toJSON(): any;
        };
        self_delegated_bandwidth?: {
            from: _wharfkit_antelope.Name;
            to: _wharfkit_antelope.Name;
            net_weight: Asset;
            cpu_weight: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        refund_request?: {
            owner: _wharfkit_antelope.Name;
            request_time: TimePoint;
            net_amount: Asset;
            cpu_amount: Asset;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        voter_info?: {
            owner: _wharfkit_antelope.Name;
            proxy: _wharfkit_antelope.Name;
            producers: _wharfkit_antelope.Name[];
            staked?: Int64 | undefined;
            last_vote_weight: Float64;
            proxied_vote_weight: Float64;
            is_proxy: boolean;
            flags1?: _wharfkit_antelope.UInt32 | undefined;
            reserved2: _wharfkit_antelope.UInt32;
            reserved3: string;
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        rex_info?: {
            version: _wharfkit_antelope.UInt32;
            owner: _wharfkit_antelope.Name;
            vote_stake: Asset;
            rex_balance: Asset;
            matured_rex: Int64;
            rex_maturities: {
                key?: TimePoint | undefined;
                value?: Int64 | undefined;
                first?: TimePoint | undefined;
                second?: Int64 | undefined;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        } | undefined;
        getPermission(permission: NameType): {
            perm_name: _wharfkit_antelope.Name;
            parent: _wharfkit_antelope.Name;
            required_auth: _wharfkit_antelope.Authority;
            linked_actions: {
                account: _wharfkit_antelope.Name;
                action: _wharfkit_antelope.Name;
                equals(other: any): boolean;
                toJSON(): any;
            }[];
            equals(other: any): boolean;
            toJSON(): any;
        };
        equals(other: any): boolean;
        toJSON(): any;
    }>;
}
/**
 * A list of chain IDs and their ChainIndices for reference lookups
 */
declare const chainIdsToIndices: Map<Checksum256Type, ChainIndices>;
/**
 * A list of known chain IDs and their logos.
 */
declare const chainLogos: Map<Checksum256Type, LogoType>;

export { Cancelable, Canceled, ChainDefinition, ChainDefinitionArgs, ChainDefinitionType, ChainIndices, ChainNames, Chains, ExplorerDefinition, ExplorerDefinitionType, Fetch, LocaleDefinitions, Logo, LogoType, TelosAccountObject, TelosAccountVoterInfo, TokenBalance, TokenBalanceType, TokenIdentifier, TokenIdentifierType, TokenMeta, TokenMetaType, WAXAccountObject, WAXAccountVoterInfo, cancelable, chainIdsToIndices, chainLogos };
