FORMAT
struct FORMAT {
  std::string name;
  std::string type;
};
Valid types are:

int8/ int16/ int32/ int64
uint8/ uint16/ uint32/ uint64
fixed8/ fixed16/ fixed32/ fixed64
float/ double/ string/ image/ ipfs/ bool/ byte

or any valid type followed by [] to describe a vector.
nested vectors (e.g. uint64[][]) are not allowed
image is an alias for string and signals to NFT explorers to display the attribute as an image. It is expected to either include a URL starting with http or an IPFS hash, possibly followed by a folder structure (e.g. <HASH>/common/10.jpg).

FORMAT is a struct with a name and type value, needed for serialization.

To pass it to nodes/ eosjs, use the following format: {"name": "MYNA<PERSON>", "type": "MYTYPE"}

ATOMIC_ATTRBIUTE
(Code taken from atomicdata.hpp)

    //Custom vector types need to be defined because otherwise a bug in Nodeos (tested for 1.8.0)
    //would cause all get_table_row calls to return an error
    typedef std::vector<int8_t> INT8_VEC;
    typedef std::vector<int16_t> INT16_VEC;
    typedef std::vector<int32_t> INT32_VEC;
    typedef std::vector<int64_t> INT64_VEC;
    typedef std::vector<uint8_t> UINT8_VEC;
    typedef std::vector<uint16_t> UINT16_VEC;
    typedef std::vector<uint32_t> UINT32_VEC;
    typedef std::vector<uint64_t> UINT64_VEC;
    typedef std::vector<float> FLOAT_VEC;
    typedef std::vector<double> DOUBLE_VEC;
    typedef std::vector<std::string> STRING_VEC;

//Here, define needs to be used instead of typedef in order to prevent the eosio abigen from creating an extra type
//for this, because of another bug in Nodeos (https://github.com/EOSIO/eos/issues/7254)
#define ATOMIC_ATTRIBUTE std::variant< \
    int8_t, int16_t, int32_t, int64_t, \
    uint8_t, uint16_t, uint32_t, uint64_t, \
    float, double, std::string, \
    atomicdata::INT8_VEC, atomicdata::INT16_VEC, atomicdata::INT32_VEC, atomicdata::INT64_VEC, \
    atomicdata::UINT8_VEC, atomicdata::UINT16_VEC, atomicdata::UINT32_VEC, atomicdata::UINT64_VEC, \
    atomicdata::FLOAT_VEC, atomicdata::DOUBLE_VEC, atomicdata::STRING_VEC>
ATOMIC_ATTRBIUTE is a variant containing all types that are available in serialization.

To pass variants to nodes/ eosjs, use the following format: ["MYTYPE", MYVALUE], so for example:

["uint32", 1000] or [FLOAT_VEC, [1.0f, 0.001f, 999.9f]]

Note that the int/ uint types do not have a "_t" suffix like they do in C++.

ATTRIBUTE_MAP
typedef std::map<std::string, ATOMIC_ATTRIBUTE> ATTRIBUTE_MAP;
FORMAT is a map that maps strings to ATOMIC_ATTRIBUTEs, which is used to store all the data for an asset/ template that is then serialized and stored.

To pass maps to nodes/ eosjs, use the following format: [{"key": MYKEY, value: MYVALUE}, ...]. For this map, keys are strings and values are variants, so for example:

[{"key": "id", "value": ["uint64", 1024]}, {"key": "color", "value": ["string", "pink"]}]