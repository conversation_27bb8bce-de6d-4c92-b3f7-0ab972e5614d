/**
 * @wharfkit/protocol-esr v1.4.0
 * https://github.com/wharfkit/protocol-esr
 *
 * @license
 * Copyright (c) 2023 Greymass Inc. All Rights Reserved.
 * 
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 
 * 1.  Redistribution of source code must retain the above copyright notice, this
 *     list of conditions and the following disclaimer.
 * 
 * 2.  Redistribution in binary form must reproduce the above copyright notice,
 *     this list of conditions and the following disclaimer in the documentation
 *     and/or other materials provided with the distribution.
 * 
 * 3.  Neither the name of the copyright holder nor the names of its contributors
 *     may be used to endorse or promote products derived from this software without
 *     specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
 * OF THE POSSIBILITY OF SUCH DAMAGE.
 * 
 * YOU ACKNOWLEDGE THAT THIS SOFTWARE IS NOT DESIGNED, LICENSED OR INTENDED FOR USE
 * IN THE DESIGN, CONSTRUCTION, OPERATION OR MAINTENANCE OF ANY MILITARY FACILITY.
 */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var session = require('@wharfkit/session');
var buoy = require('@greymass/buoy');
var miniaes = require('@greymass/miniaes');

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

function __decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}

function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}

typeof SuppressedError === "function" ? SuppressedError : function (error, suppressed, message) {
    var e = new Error(message);
    return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
};

exports.SealedMessage = class SealedMessage extends session.Struct {
};
__decorate([
    session.Struct.field('public_key')
], exports.SealedMessage.prototype, "from", void 0);
__decorate([
    session.Struct.field('uint64')
], exports.SealedMessage.prototype, "nonce", void 0);
__decorate([
    session.Struct.field('bytes')
], exports.SealedMessage.prototype, "ciphertext", void 0);
__decorate([
    session.Struct.field('uint32')
], exports.SealedMessage.prototype, "checksum", void 0);
exports.SealedMessage = __decorate([
    session.Struct.type('sealed_message')
], exports.SealedMessage);
exports.LinkCreate = class LinkCreate extends session.Struct {
};
__decorate([
    session.Struct.field('name')
], exports.LinkCreate.prototype, "session_name", void 0);
__decorate([
    session.Struct.field('public_key')
], exports.LinkCreate.prototype, "request_key", void 0);
__decorate([
    session.Struct.field('string', { extension: true })
], exports.LinkCreate.prototype, "user_agent", void 0);
exports.LinkCreate = __decorate([
    session.Struct.type('link_create')
], exports.LinkCreate);
exports.LinkInfo = class LinkInfo extends session.Struct {
};
__decorate([
    session.Struct.field('time_point_sec')
], exports.LinkInfo.prototype, "expiration", void 0);
exports.LinkInfo = __decorate([
    session.Struct.type('link_info')
], exports.LinkInfo);

exports.BuoyMessage = class BuoyMessage extends session.Struct {
};
__decorate([
    session.Struct.field('public_key')
], exports.BuoyMessage.prototype, "from", void 0);
__decorate([
    session.Struct.field('uint64')
], exports.BuoyMessage.prototype, "nonce", void 0);
__decorate([
    session.Struct.field('bytes')
], exports.BuoyMessage.prototype, "ciphertext", void 0);
__decorate([
    session.Struct.field('uint32')
], exports.BuoyMessage.prototype, "checksum", void 0);
exports.BuoyMessage = __decorate([
    session.Struct.type('buoy_message')
], exports.BuoyMessage);
exports.BuoySession = class BuoySession extends session.Struct {
};
__decorate([
    session.Struct.field('name')
], exports.BuoySession.prototype, "session_name", void 0);
__decorate([
    session.Struct.field('public_key')
], exports.BuoySession.prototype, "request_key", void 0);
__decorate([
    session.Struct.field('string', { extension: true })
], exports.BuoySession.prototype, "user_agent", void 0);
exports.BuoySession = __decorate([
    session.Struct.type('buoy_session')
], exports.BuoySession);
exports.BuoyInfo = class BuoyInfo extends session.Struct {
};
__decorate([
    session.Struct.field('time_point_sec')
], exports.BuoyInfo.prototype, "expiration", void 0);
exports.BuoyInfo = __decorate([
    session.Struct.type('buoy_info')
], exports.BuoyInfo);

function waitForCallback(callbackArgs, buoyWs, t) {
    return __awaiter(this, void 0, void 0, function* () {
        // Use the buoy-client to create a promise and wait for a response to the identity request
        const callbackResponse = yield buoy.receive(Object.assign(Object.assign({}, callbackArgs), { WebSocket: buoyWs || WebSocket }));
        if (!callbackResponse) {
            // If the promise was rejected, throw an error
            throw new Error(callbackResponse.rejected);
        }
        // If the promise was rejected, throw an error
        if (typeof callbackResponse.rejected === 'string') {
            throw new Error(callbackResponse.rejected);
        }
        // Process the identity request callback payload
        const payload = JSON.parse(callbackResponse);
        if (payload.sa === undefined || payload.sp === undefined || payload.cid === undefined) {
            throw new Error(t('error.cancelled', { default: 'The request was cancelled from Anchor.' }));
        }
        return payload;
    });
}

/**
 * Return PascalCase version of snake_case string.
 * @internal
 */
function snakeToPascal(name) {
    return name
        .split('_')
        .map((v) => (v[0] ? v[0].toUpperCase() : '') + v.slice(1))
        .join('');
}
/**
 * Return camelCase version of snake_case string.
 * @internal
 */
function snakeToCamel(name) {
    const pascal = snakeToPascal(name);
    return (pascal[0] ? pascal[0].toLowerCase() : '') + pascal.slice(1);
}
/**
 * Print a warning message to console.
 * @internal
 **/
function logWarn(...args) {
    // eslint-disable-next-line no-console
    console.warn('[anchor-link]', ...args);
}
/**
 * Generate a UUID.
 *  @internal
 * */
function uuid() {
    let uuid = '', ii;
    const chars = '0123456789abcdef';
    for (ii = 0; ii < 36; ii += 1) {
        switch (ii) {
            case 8:
            case 13:
            case 18:
            case 23:
                uuid += '-';
                break;
            case 14:
                uuid += '4';
                break;
            case 19:
                uuid += chars[(Math.random() * 4) | (0 + 8)];
                break;
            default:
                uuid += chars[(Math.random() * 16) | 0];
        }
    }
    return uuid;
}
/** Generate a return url that Anchor will redirect back to w/o reload. */
function generateReturnUrl() {
    // Return undefined for iOS React Native apps to prevent redirect to Safari
    if (isAppleHandheld() && isReactNativeApp()) {
        return undefined;
    }
    if (isChromeiOS()) {
        // google chrome on iOS will always open new tab so we just ask it to open again as a workaround
        return 'googlechrome://';
    }
    if (isFirefoxiOS()) {
        // same for firefox
        return 'firefox:://';
    }
    if (isAppleHandheld() && isBrave()) {
        // and brave ios
        return 'brave://';
    }
    if (isAppleHandheld()) {
        // return url with unique fragment required for iOS safari to trigger the return url
        const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let rv = window.location.href.split('#')[0] + '#';
        for (let i = 0; i < 8; i++) {
            rv += alphabet.charAt(Math.floor(Math.random() * alphabet.length));
        }
        return rv;
    }
    if (isAndroid() && isFirefox()) {
        return 'android-app://org.mozilla.firefox';
    }
    if (isAndroid() && isEdge()) {
        return 'android-app://com.microsoft.emmx';
    }
    if (isAndroid() && isOpera()) {
        return 'android-app://com.opera.browser';
    }
    if (isAndroid() && isBrave()) {
        return 'android-app://com.brave.browser';
    }
    if (isAndroid() && isAndroidWebView()) {
        return 'android-app://webview';
    }
    if (isAndroid() && isChromeMobile()) {
        return 'android-app://com.android.chrome';
    }
    return window.location.href;
}
function isAppleHandheld() {
    return /iP(ad|od|hone)/i.test(navigator.userAgent);
}
function isChromeiOS() {
    return /CriOS/.test(navigator.userAgent);
}
function isChromeMobile() {
    return /Chrome\/[.0-9]* Mobile/i.test(navigator.userAgent);
}
function isFirefox() {
    return /Firefox/i.test(navigator.userAgent);
}
function isFirefoxiOS() {
    return /FxiOS/.test(navigator.userAgent);
}
function isOpera() {
    return /OPR/.test(navigator.userAgent) || /Opera/.test(navigator.userAgent);
}
function isEdge() {
    return /Edg/.test(navigator.userAgent);
}
function isBrave() {
    return navigator['brave'] && typeof navigator['brave'].isBrave === 'function';
}
function isAndroid() {
    return /Android/.test(navigator.userAgent);
}
function isReactNativeApp() {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    return !!window.ReactNativeWebView;
}
function isAndroidWebView() {
    return (/wv/.test(navigator.userAgent) ||
        (/Android/.test(navigator.userAgent) && isReactNativeApp()));
}
function isKnownMobile() {
    return (isAppleHandheld() ||
        isChromeiOS() ||
        isChromeMobile() ||
        isFirefoxiOS() ||
        isAndroid() ||
        isAndroidWebView());
}

/**
 * createIdentityRequest
 *
 * @param context LoginContext
 * @returns
 */
function createIdentityRequest(context, buoyUrl) {
    return __awaiter(this, void 0, void 0, function* () {
        // Create a new private key and public key to act as the request key
        const privateKey = session.PrivateKey.generate('K1');
        const requestKey = privateKey.toPublic();
        // Create a new BuoySession struct to be used as the info field
        const createInfo = exports.BuoySession.from({
            session_name: context.appName,
            request_key: requestKey,
            user_agent: getUserAgent(),
        });
        // Determine based on the options whether this is a multichain request
        const isMultiChain = !(context.chain || context.chains.length === 1);
        // Create the callback
        const callbackChannel = prepareCallbackChannel(buoyUrl);
        // Determine the chain id(s) to use
        const chainId = isMultiChain
            ? null
            : context.chain
                ? session.ChainId.from(context.chain.id.array)
                : null;
        const chainIds = isMultiChain
            ? context.chains.map((c) => session.ChainId.from(c.id.array))
            : [];
        // Create the request
        const request = session.SigningRequest.identity({
            callback: prepareCallback(callbackChannel),
            scope: String(context.appName),
            chainId,
            chainIds,
            info: {
                link: createInfo,
                scope: String(context.appName),
            },
        }, context.esrOptions);
        const sameDeviceRequest = request.clone();
        if (typeof window !== 'undefined') {
            const returnUrl = generateReturnUrl();
            sameDeviceRequest.setInfoKey('same_device', true);
            if (returnUrl !== undefined) {
                sameDeviceRequest.setInfoKey('return_path', returnUrl);
            }
        }
        // Return the request and the callback data
        return {
            callback: callbackChannel,
            request,
            sameDeviceRequest,
            requestKey,
            privateKey,
        };
    });
}
/**
 * prepareTransactionRequest
 *
 * @param resolved ResolvedSigningRequest
 * @returns
 */
function setTransactionCallback(request, buoyUrl) {
    const callback = prepareCallbackChannel(buoyUrl);
    request.setCallback(`${callback.service}/${callback.channel}`, true);
    return callback;
}
function getUserAgent() {
    const version = '1.4.0';
    let agent = `@wharfkit/protocol-esr ${version}`;
    if (typeof navigator !== 'undefined') {
        agent += ' ' + navigator.userAgent;
    }
    return agent;
}
function prepareCallback(callbackChannel) {
    const { service, channel } = callbackChannel;
    return {
        url: `${service}/${channel}`,
        background: true,
    };
}
function prepareCallbackChannel(buoyUrl) {
    return {
        service: buoyUrl,
        channel: uuid(),
    };
}
function sealMessage(message, privateKey, publicKey, nonce) {
    const secret = privateKey.sharedSecret(publicKey);
    if (!nonce) {
        nonce = session.UInt64.random();
    }
    const key = session.Checksum512.hash(session.Serializer.encode({ object: nonce }).appending(secret.array));
    const cbc = new miniaes.AES_CBC(key.array.slice(0, 32), key.array.slice(32, 48));
    const ciphertext = session.Bytes.from(cbc.encrypt(session.Bytes.from(message, 'utf8').array));
    const checksumView = new DataView(session.Checksum256.hash(key.array).array.buffer);
    const checksum = checksumView.getUint32(0, true);
    return exports.SealedMessage.from({
        from: privateKey.toPublic(),
        nonce,
        ciphertext,
        checksum,
    });
}
function verifyLoginCallbackResponse(callbackResponse, context) {
    return __awaiter(this, void 0, void 0, function* () {
        if (!callbackResponse.sig || callbackResponse.sig.length === 0) {
            throw new Error('Invalid response, must have at least one signature');
        }
        let chain;
        if (!context.chain && context.chains.length > 1) {
            if (!callbackResponse.cid) {
                throw new Error('Multi chain response payload must specify resolved chain id (cid)');
            }
        }
        else {
            chain = context.chain || context.chains[0];
            if (callbackResponse.cid && String(chain.id) !== callbackResponse.cid) {
                throw new Error('Got response for wrong chain id');
            }
        }
    });
}
function extractSignaturesFromCallback(payload) {
    const signatures = [];
    let index = 0;
    let sig = payload.sig;
    while (sig) {
        signatures.push(String(sig));
        sig = payload[`sig${index}`];
        index++;
    }
    // Deduplicate and make signatures
    return [...new Set(signatures)].map((s) => session.Signature.from(s));
}
function isCallback(object) {
    return 'tx' in object;
}

exports.createIdentityRequest = createIdentityRequest;
exports.extractSignaturesFromCallback = extractSignaturesFromCallback;
exports.generateReturnUrl = generateReturnUrl;
exports.getUserAgent = getUserAgent;
exports.isAndroid = isAndroid;
exports.isAndroidWebView = isAndroidWebView;
exports.isAppleHandheld = isAppleHandheld;
exports.isBrave = isBrave;
exports.isCallback = isCallback;
exports.isChromeMobile = isChromeMobile;
exports.isChromeiOS = isChromeiOS;
exports.isEdge = isEdge;
exports.isFirefox = isFirefox;
exports.isFirefoxiOS = isFirefoxiOS;
exports.isKnownMobile = isKnownMobile;
exports.isOpera = isOpera;
exports.isReactNativeApp = isReactNativeApp;
exports.logWarn = logWarn;
exports.prepareCallback = prepareCallback;
exports.sealMessage = sealMessage;
exports.setTransactionCallback = setTransactionCallback;
exports.snakeToCamel = snakeToCamel;
exports.snakeToPascal = snakeToPascal;
exports.uuid = uuid;
exports.verifyLoginCallbackResponse = verifyLoginCallbackResponse;
exports.waitForCallback = waitForCallback;
//# sourceMappingURL=protocol-esr.js.map
