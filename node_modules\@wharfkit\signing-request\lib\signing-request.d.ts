/**
 * EOSIO Signing Request v3.2.0
 * https://github.com/greymass/eosio-signing-request
 *
 * @license
 * Copyright © 2021 Greymass Inc.
 * 
 * Permission is hereby granted, free of charge, to any person
 * obtaining a copy of this software and associated documentation
 * files (the “Software”), to deal in the Software without
 * restriction, including without limitation the rights to use,
 * copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following
 * conditions:
 * 
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 * OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 * WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 * 
 * YOU ACKNOWLEDGE THAT THIS SOFTWARE IS NOT DESIGNED, LICENSED OR
 * INTENDED FOR USE IN THE DESIGN, CONSTRUCTION, OPERATION OR
 * MAINTENANCE OF ANY MILITARY FACILITY.
 */
import { Checksum256Type, Checksum256, UInt8, Variant, Name, Struct, PermissionLevel, Action, Transaction, Bytes, Signature, NameType, TimePointType, PermissionLevelType, SignatureType, TimePointSec, PublicKey, AuthorityType, ABIDef, UInt32Type, UInt16Type, ABISerializable, UInt16, UInt32, VarUInt, TransactionExtension, AnyAction, AnyTransaction, ABI, BytesType, ABISerializableType, ABISerializableConstructor } from '@wharfkit/antelope';

/** Chain ID aliases. */
declare enum ChainName {
    UNKNOWN = 0,
    EOS = 1,
    TELOS = 2,
    JUNGLE = 3,
    KYLIN = 4,
    WORBLI = 5,
    BOS = 6,
    MEETONE = 7,
    INSIGHTS = 8,
    BEOS = 9,
    WAX = 10,
    PROTON = 11,
    FIO = 12
}
declare type ChainIdType = ChainId | ChainName | Checksum256Type;
declare class ChainId extends Checksum256 {
    static from(value: ChainIdType): ChainId;
    get chainVariant(): ChainIdVariant;
    get chainName(): ChainName;
}
declare class ChainAlias extends UInt8 {
}
declare class ChainIdVariant extends Variant {
    value: ChainId | ChainAlias;
    get chainId(): ChainId;
}

/** SigningRequest ABI and typedefs. */

declare class AccountName extends Name {
}
declare class PermissionName extends Name {
}
declare class IdentityV2 extends Struct {
    permission?: PermissionLevel;
}
declare class IdentityV3 extends Struct {
    scope: Name;
    permission?: PermissionLevel;
}
declare class RequestVariantV2 extends Variant {
    value: Action | Action[] | Transaction | IdentityV2;
}
declare class RequestVariantV3 extends Variant {
    value: Action | Action[] | Transaction | IdentityV3;
}
declare class RequestFlags extends UInt8 {
    static broadcast: number;
    static background: number;
    get broadcast(): boolean;
    set broadcast(enabled: boolean);
    get background(): boolean;
    set background(enabled: boolean);
    private setFlag;
}
declare class InfoPair extends Struct {
    key: string;
    value: Bytes;
}
declare class RequestDataV2 extends Struct {
    chain_id: ChainIdVariant;
    req: RequestVariantV2;
    flags: RequestFlags;
    callback: string;
    info: InfoPair[];
}
declare class RequestDataV3 extends Struct {
    chain_id: ChainIdVariant;
    req: RequestVariantV3;
    flags: RequestFlags;
    callback: string;
    info: InfoPair[];
}
declare class RequestSignature extends Struct {
    signer: Name;
    signature: Signature;
}

declare type IdentityProofType = IdentityProof | string | {
    chainId: ChainIdType;
    scope: NameType;
    expiration: TimePointType;
    signer: PermissionLevelType;
    signature: SignatureType;
};
declare class IdentityProof extends Struct {
    chainId: ChainId;
    scope: Name;
    expiration: TimePointSec;
    signer: PermissionLevel;
    signature: Signature;
    static from(value: IdentityProofType): IdentityProof;
    /**
     * Create a new instance from an EOSIO authorization header string.
     * "EOSIO <base64payload>"
     */
    static fromString(string: string): IdentityProof;
    /** Create a new instance from a callback payload. */
    static fromPayload(payload: CallbackPayload, options?: SigningRequestEncodingOptions): IdentityProof;
    /**
     * Transaction this proof resolves to.
     * @internal
     */
    get transaction(): Transaction;
    /**
     * Recover the public key that signed this proof.
     */
    recover(): PublicKey;
    /**
     * Verify that given authority signed this proof.
     * @param auth The accounts signing authority.
     * @param currentTime Time to verify expiry against, if unset will use system time.
     */
    verify(auth: AuthorityType, currentTime?: TimePointType): boolean;
    /**
     * Encode the proof to an `EOSIO` auth header string.
     */
    toString(): string;
}

/**
 * EOSIO Signing Request (ESR).
 */

/** Current supported protocol version, backwards compatible with version 2. */
declare const ProtocolVersion = 3;
/** Interface that should be implemented by abi providers. */
interface AbiProvider {
    /**
     * Return a promise that resolves to an abi object for the given account name,
     * e.g. the result of a rpc call to chain/get_abi.
     */
    getAbi: (account: Name) => Promise<ABIDef>;
}
/** Interface that should be implemented by zlib implementations. */
interface ZlibProvider {
    /** Deflate data w/o adding zlib header. */
    deflateRaw: (data: Uint8Array) => Uint8Array;
    /** Inflate data w/o requiring zlib header. */
    inflateRaw: (data: Uint8Array) => Uint8Array;
}
/** Interface that should be implemented by signature providers. */
interface SignatureProvider {
    /** Sign 32-byte message and return signer name and signature string. */
    sign: (message: Checksum256) => {
        signer: NameType;
        signature: SignatureType;
    };
}
/**
 * The callback payload sent to background callbacks.
 */
interface CallbackPayload {
    /** The first signature. */
    sig: string;
    /** Transaction ID as HEX-encoded string. */
    tx: string;
    /** Block number hint (only present if transaction was broadcast). */
    bn?: string;
    /** Signer authority, aka account name. */
    sa: string;
    /** Signer permission, e.g. "active". */
    sp: string;
    /** Reference block num used when resolving request. */
    rbn: string;
    /** Reference block id used when resolving request. */
    rid: string;
    /** The originating signing request packed as a uri string. */
    req: string;
    /** Expiration time used when resolving request. */
    ex: string;
    /** The resolved chain id.  */
    cid?: string;
    /** All signatures 0-indexed as `sig0`, `sig1`, etc. */
    [sig0: string]: string | undefined;
}
/**
 * Context used to resolve a callback.
 * Compatible with the JSON response from a `push_transaction` call.
 */
interface ResolvedCallback {
    /** The URL to hit. */
    url: string;
    /**
     * Whether to run the request in the background. For a https url this
     * means POST in the background instead of a GET redirect.
     */
    background: boolean;
    /**
     * The callback payload as a object that should be encoded to JSON
     * and POSTed to background callbacks.
     */
    payload: CallbackPayload;
}
/**
 * Context used to resolve a transaction.
 * Compatible with the JSON response from a `get_block` call.
 */
interface TransactionContext {
    /** Timestamp expiration will be derived from. */
    timestamp?: TimePointType;
    /**
     * How many seconds in the future to set expiration when deriving from timestamp.
     * Defaults to 60 seconds if unset.
     */
    expire_seconds?: UInt32Type;
    /** Block number ref_block_num will be derived from. */
    block_num?: UInt32Type;
    /** Reference block number, takes precedence over block_num if both is set. */
    ref_block_num?: UInt16Type;
    /** Reference block prefix. */
    ref_block_prefix?: UInt32Type;
    /** Expiration timestamp, takes precedence over timestamp and expire_seconds if set. */
    expiration?: TimePointType;
    /** Chain ID to resolve for, required for multi-chain requests. */
    chainId?: ChainIdType;
}
/**
 * The placeholder name: `............1` aka `uint64(1)`.
 * If used in action data will be resolved to current signer.
 * If used in as an authorization permission will be resolved to
 * the signers permission level.
 *
 * Example action:
 * ```
 * { account: "eosio.token",
 *   name: "transfer",
 *   authorization: [{actor: "............1", permission: "............1"}],
 *   data: {
 *     from: "............1",
 *     to: "bar",
 *     quantity: "42.0000 EOS",
 *     memo: "Don't panic" }}
 * ```
 * When signed by `foo@active` would resolve to:
 * ```
 * { account: "eosio.token",
 *   name: "transfer",
 *   authorization: [{actor: "foo", permission: "active"}],
 *   data: {
 *     from: "foo",
 *     to: "bar",
 *     quantity: "42.0000 EOS",
 *     memo: "Don't panic" }}
 * ```
 */
declare const PlaceholderName: Name;
/** Placeholder that will resolve to signer permission name. */
declare const PlaceholderPermission: Name;
declare const PlaceholderAuth: PermissionLevel;
interface ResolvedAction {
    /** The account (a.k.a. contract) to run action on. */
    account: Name;
    /** The name of the action. */
    name: Name;
    /** The permissions authorizing the action. */
    authorization: PermissionLevel[];
    /** The decoded action data. */
    data: Record<string, ABISerializable>;
}
interface ResolvedTransaction {
    /** The time at which a transaction expires. */
    expiration: TimePointSec;
    /** *Specifies a block num in the last 2^16 blocks. */
    ref_block_num: UInt16;
    /** Specifies the lower 32 bits of the block id. */
    ref_block_prefix: UInt32;
    /** Upper limit on total network bandwidth (in 8 byte words) billed for this transaction. */
    max_net_usage_words: VarUInt;
    /** Upper limit on the total CPU time billed for this transaction. */
    max_cpu_usage_ms: UInt8;
    /** Number of seconds to delay this transaction for during which it may be canceled. */
    delay_sec: VarUInt;
    /** The context free actions in the transaction. */
    context_free_actions: ResolvedAction[];
    /** The actions in the transaction. */
    actions: ResolvedAction[];
    /** Transaction extensions. */
    transaction_extensions: TransactionExtension[];
}
declare type CallbackType = string | {
    url: string;
    background: boolean;
};
interface SigningRequestCommonArguments {
    /**
     * Chain ID to use, can be set to `null` for a multi-chain request.
     * Defaults to EOS if omitted.
     */
    chainId?: ChainIdType | null;
    /**
     * Chain IDs to constrain a multi-chain request to.
     * Only considered if `chainId` is explicitly set to `null.
     */
    chainIds?: ChainIdType[];
    /** Optional metadata to pass along with the request. */
    info?: {
        [key: string]: Bytes | ABISerializable;
    };
}
interface SigningRequestCreateArguments extends SigningRequestCommonArguments {
    /** Single action to create request with. */
    action?: AnyAction;
    /** Multiple actions to create request with. */
    actions?: AnyAction[];
    /**
     * Full or partial transaction to create request with.
     * If TAPoS info is omitted it will be filled in when resolving the request.
     */
    transaction?: Partial<AnyTransaction>;
    /** Create an identity request. */
    identity?: {
        scope?: NameType;
        permission?: PermissionLevelType;
    };
    /** Whether wallet should broadcast tx, defaults to true. */
    broadcast?: boolean;
    /**
     * Optional callback URL the signer should hit after
     * broadcasting or signing. Passing a string means background = false.
     */
    callback?: CallbackType;
}
interface SigningRequestCreateIdentityArguments extends SigningRequestCommonArguments {
    /**
     * Callback where the identity should be delivered.
     */
    callback: CallbackType;
    /**
     * Requested account name of identity.
     * Defaults to placeholder (any identity) if omitted.
     */
    account?: NameType;
    /**
     * Requested account permission.
     * Defaults to placeholder (any permission) if omitted.
     */
    permission?: NameType;
    /**
     * Scope for the request.
     */
    scope?: NameType;
}
interface SigningRequestEncodingOptions {
    /** Optional zlib, if provided the request will be compressed when encoding. */
    zlib?: ZlibProvider;
    /** Abi provider, required if the arguments contain un-encoded actions. */
    abiProvider?: AbiProvider;
    /** Optional signature provider, will be used to create a request signature if provided. */
    signatureProvider?: SignatureProvider;
}
declare type AbiMap = Map<string, ABI>;
declare class SigningRequest {
    /** Return the identity ABI for given version. */
    private static identityAbi;
    /** Return the ABISerializableType identity type for given version. */
    private static identityType;
    /** Return the ABISerializableType storage type for given version. */
    private static storageType;
    /** Create a new signing request. */
    static create(args: SigningRequestCreateArguments, options?: SigningRequestEncodingOptions): Promise<SigningRequest>;
    /**
     * Synchronously create a new signing request.
     * @throws If an un-encoded action with no abi def is encountered.
     */
    static createSync(args: SigningRequestCreateArguments, options?: SigningRequestEncodingOptions, abis?: Record<string, ABIDef>): SigningRequest;
    /** Creates an identity request. */
    static identity(args: SigningRequestCreateIdentityArguments, options?: SigningRequestEncodingOptions): SigningRequest;
    /**
     * Create a request from a chain id and serialized transaction.
     * @param chainId The chain id where the transaction is valid.
     * @param serializedTransaction The serialized transaction.
     * @param options Creation options.
     */
    static fromTransaction(chainId: ChainIdType, serializedTransaction: BytesType, options?: SigningRequestEncodingOptions): SigningRequest;
    /** Creates a signing request from encoded `esr:` uri string. */
    static from(uri: string, options?: SigningRequestEncodingOptions): SigningRequest;
    static fromData(data: BytesType, options?: SigningRequestEncodingOptions): SigningRequest;
    /** The signing request version. */
    version: number;
    /** The raw signing request data. */
    data: RequestDataV2 | RequestDataV3;
    /** The request signature. */
    signature?: RequestSignature;
    private zlib?;
    private abiProvider?;
    /**
     * Create a new signing request.
     * Normally not used directly, see the `create` and `from` class methods.
     */
    constructor(version: number, data: RequestDataV2 | RequestDataV3, zlib?: ZlibProvider, abiProvider?: AbiProvider, signature?: RequestSignature);
    /**
     * Sign the request, mutating.
     * @param signatureProvider The signature provider that provides a signature for the signer.
     */
    sign(signatureProvider: SignatureProvider): void;
    /**
     * Get the signature digest for this request.
     */
    getSignatureDigest(): Checksum256;
    /**
     * Set the signature data for this request, mutating.
     * @param signer Account name of signer.
     * @param signature The signature string.
     */
    setSignature(signer: string, signature: string): void;
    /**
     * Set the request callback, mutating.
     * @param url Where the callback should be sent.
     * @param background Whether the callback should be sent in the background.
     */
    setCallback(url: string, background: boolean): void;
    /**
     * Set broadcast flag.
     * @param broadcast Whether the transaction should be broadcast by receiver.
     */
    setBroadcast(broadcast: boolean): void;
    /**
     * Encode this request into an `esr:` uri.
     * @argument compress Whether to compress the request data using zlib,
     *                    defaults to true if omitted and zlib is present;
     *                    otherwise false.
     * @argument slashes Whether add slashes after the protocol scheme, i.e. `esr://`.
     *                   Defaults to true.
     * @returns An esr uri string.
     */
    encode(compress?: boolean, slashes?: boolean, scheme?: string): string;
    /** Get the request data without header or signature. */
    getData(): Uint8Array;
    /** Get signature data, returns an empty array if request is not signed. */
    getSignatureData(): Uint8Array;
    /** ABI definitions required to resolve request. */
    getRequiredAbis(): Name[];
    /** Whether TaPoS values are required to resolve request. */
    requiresTapos(): boolean;
    /** Resolve required ABI definitions. */
    fetchAbis(abiProvider?: AbiProvider): Promise<AbiMap>;
    /**
     * Decode raw actions actions to object representations.
     * @param abis ABI defenitions required to decode all actions.
     * @param signer Placeholders in actions will be resolved to signer if set.
     */
    resolveActions(abis: AbiMap, signer?: PermissionLevelType): ResolvedAction[];
    resolveTransaction(abis: AbiMap, signer: PermissionLevelType, ctx?: TransactionContext): ResolvedTransaction;
    resolve(abis: AbiMap, signer: PermissionLevelType, ctx?: TransactionContext): ResolvedSigningRequest;
    /**
     * Get the id of the chain where this request is valid.
     * @returns The 32-byte chain id as hex encoded string.
     */
    getChainId(): ChainId;
    /**
     * Chain IDs this request is valid for, only valid for multi chain requests. Value of `null` when `isMultiChain` is true denotes any chain.
     */
    getChainIds(): ChainId[] | null;
    /**
     * Set chain IDs this request is valid for, only considered for multi chain requests.
     */
    setChainIds(ids: ChainIdType[]): void;
    /**
     * True if chainId is set to chain alias `0` which indicates that the request is valid for any chain.
     */
    isMultiChain(): boolean;
    /** Return the actions in this request with action data encoded. */
    getRawActions(): Action[];
    /** Unresolved transaction. */
    getRawTransaction(): Transaction;
    /** Whether the request is an identity request. */
    isIdentity(): boolean;
    /** Whether the request should be broadcast by signer. */
    shouldBroadcast(): boolean;
    /**
     * Present if the request is an identity request and requests a specific account.
     * @note This returns `nil` unless a specific identity has been requested,
     *       use `isIdentity` to check id requests.
     */
    getIdentity(): Name | null;
    /**
     * Present if the request is an identity request and requests a specific permission.
     * @note This returns `nil` unless a specific permission has been requested,
     *       use `isIdentity` to check id requests.
     */
    getIdentityPermission(): Name | null;
    /**
     * Present if the request is an identity request and requests a specific permission.
     * @note This returns `nil` unless a specific permission has been requested,
     *       use `isIdentity` to check id requests.
     */
    getIdentityScope(): Name | null;
    /** Get raw info dict */
    getRawInfo(): {
        [key: string]: Bytes;
    };
    getRawInfoKey(key: string): Bytes | undefined;
    setRawInfoKey(key: string, value: BytesType): void;
    /** Set a metadata key. */
    setInfoKey(key: string, object: ABISerializable, type?: ABISerializableType): void;
    /** Get a metadata key. */
    getInfoKey(key: string): string;
    getInfoKey<T extends ABISerializableConstructor>(key: string, type: T): InstanceType<T>;
    getInfoKey(key: string, type: ABISerializableType): any;
    /** Return a deep copy of this request. */
    clone(): SigningRequest;
    toString(): string;
    toJSON(): string;
}
declare class ResolvedSigningRequest {
    /** Recreate a resolved request from a callback payload. */
    static fromPayload(payload: CallbackPayload, options?: SigningRequestEncodingOptions): Promise<ResolvedSigningRequest>;
    /** The request that created the transaction. */
    readonly request: SigningRequest;
    /** Expected signer of transaction. */
    readonly signer: PermissionLevel;
    /** Transaction object with action data encoded. */
    readonly transaction: Transaction;
    /** Transaction object with action data decoded. */
    readonly resolvedTransaction: ResolvedTransaction;
    /** Id of chain where the request was resolved. */
    readonly chainId: ChainId;
    constructor(request: SigningRequest, signer: PermissionLevel, transaction: Transaction, resolvedTransaction: ResolvedTransaction, chainId: ChainId);
    get serializedTransaction(): Uint8Array;
    get signingDigest(): Checksum256;
    get signingData(): Bytes;
    getCallback(signatures: SignatureType[], blockNum?: UInt32Type): ResolvedCallback | null;
    getIdentityProof(signature: SignatureType): IdentityProof;
}

/**
 * Base64u - URL-Safe Base64 variant no padding.
 * Based on https://gist.github.com/jonleighton/958841
 */
declare function encode(data: Uint8Array, urlSafe?: boolean): string;
declare function decode(input: string): Uint8Array;

declare const base64u_encode: typeof encode;
declare const base64u_decode: typeof decode;
declare namespace base64u {
  export {
    base64u_encode as encode,
    base64u_decode as decode,
  };
}

export { AbiMap, AbiProvider, AccountName, base64u as Base64u, CallbackPayload, CallbackType, ChainAlias, ChainId, ChainIdType, ChainIdVariant, ChainName, IdentityProof, IdentityProofType, IdentityV2, IdentityV3, InfoPair, PermissionName, PlaceholderAuth, PlaceholderName, PlaceholderPermission, ProtocolVersion, RequestDataV2, RequestDataV3, RequestFlags, RequestSignature, RequestVariantV2, RequestVariantV3, ResolvedAction, ResolvedCallback, ResolvedSigningRequest, ResolvedTransaction, SignatureProvider, SigningRequest, SigningRequestCreateArguments, SigningRequestCreateIdentityArguments, SigningRequestEncodingOptions, TransactionContext, ZlibProvider };
