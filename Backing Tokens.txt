Backing Tokens
AtomicAssets allows anyone to back assets with tokens. The supported tokens for this are defined in the config's supported_tokens field. This will indefinitely link the tokens to the asset, and they can only be freed by burning the asset itself. This also means that non-burnable assets cannot be backed.

By backing an asset, it is given an intrinsic minimum value, because users know that they could always burn their asset to receive the backed core tokens.

How to back an asset
Backing an asset follows the deposit-spend pattern. Therefore you first need to deposit the tokens you want to use to back an asset before actually backing an asset.

To deposit tokens, first the announcedepo action needs to be called to initialize the balance table. After that, the tokens that should be deposited need to be transferred to the AtomicAssets smart contract account with the memo deposit. This will add the token's to the depositors balances table. Any unspent tokens can also be withdrawn again using the withdraw action.

After depositing a sufficient quantity of tokens, assets can be backed using the backasset action. Note that by backing an asset, you also have to pay for the full RAM cost of that asset.

Checking how much an asset is backed with
In the assets table, there is a field called backed_tokens, which can be checked for every asset.

Freeing the backed tokens
The only way to free the backed core tokens is to burn the asset that the core tokens are backed to. This can be done using the burnasset action. The backed core tokens are then transferred to the owner of the asset.