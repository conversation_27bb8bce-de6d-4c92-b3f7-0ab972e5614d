/**
 * @wharfkit/protocol-esr v1.4.0
 * https://github.com/wharfkit/protocol-esr
 *
 * @license
 * Copyright (c) 2023 Greymass Inc. All Rights Reserved.
 * 
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 
 * 1.  Redistribution of source code must retain the above copyright notice, this
 *     list of conditions and the following disclaimer.
 * 
 * 2.  Redistribution in binary form must reproduce the above copyright notice,
 *     this list of conditions and the following disclaimer in the documentation
 *     and/or other materials provided with the distribution.
 * 
 * 3.  Neither the name of the copyright holder nor the names of its contributors
 *     may be used to endorse or promote products derived from this software without
 *     specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
 * OF THE POSSIBILITY OF SUCH DAMAGE.
 * 
 * YOU ACKNOWLEDGE THAT THIS SOFTWARE IS NOT DESIGNED, LICENSED OR INTENDED FOR USE
 * IN THE DESIGN, CONSTRUCTION, OPERATION OR MAINTENANCE OF ANY MILITARY FACILITY.
 */
import { Struct, PublicKey, UInt64, Bytes, UInt32, Name, TimePointSec, CallbackPayload, SigningRequest, PrivateKey, LoginContext, CallbackType, Signature } from '@wharfkit/session';
import { ReceiveOptions } from '@greymass/buoy';
import WebSocket from 'isomorphic-ws';

declare class SealedMessage extends Struct {
    from: PublicKey;
    nonce: UInt64;
    ciphertext: Bytes;
    checksum: UInt32;
}
declare class LinkCreate extends Struct {
    session_name: Name;
    request_key: PublicKey;
    user_agent?: string;
}
declare class LinkInfo extends Struct {
    expiration: TimePointSec;
}

declare class BuoyMessage extends Struct {
    from: PublicKey;
    nonce: UInt64;
    ciphertext: Bytes;
    checksum: UInt32;
}
declare class BuoySession extends Struct {
    session_name: Name;
    request_key: PublicKey;
    user_agent?: string;
}
declare class BuoyInfo extends Struct {
    expiration: TimePointSec;
}

declare function waitForCallback(callbackArgs: any, buoyWs: any, t: any): Promise<CallbackPayload>;

interface WalletPluginOptions {
    buoyUrl?: string;
    buoyWs?: WebSocket;
}
interface IdentityRequestResponse {
    callback: any;
    request: SigningRequest;
    sameDeviceRequest: SigningRequest;
    requestKey: PublicKey;
    privateKey: PrivateKey;
}
/**
 * createIdentityRequest
 *
 * @param context LoginContext
 * @returns
 */
declare function createIdentityRequest(context: LoginContext, buoyUrl: string): Promise<IdentityRequestResponse>;
/**
 * prepareTransactionRequest
 *
 * @param resolved ResolvedSigningRequest
 * @returns
 */
declare function setTransactionCallback(request: SigningRequest, buoyUrl: any): ReceiveOptions;
declare function getUserAgent(): string;
declare function prepareCallback(callbackChannel: ReceiveOptions): CallbackType;
declare function sealMessage(message: string, privateKey: PrivateKey, publicKey: PublicKey, nonce?: UInt64): SealedMessage;
declare function verifyLoginCallbackResponse(callbackResponse: any, context: LoginContext): Promise<void>;
declare function extractSignaturesFromCallback(payload: CallbackPayload): Signature[];
declare function isCallback(object: any): object is CallbackPayload;

/**
 * Return PascalCase version of snake_case string.
 * @internal
 */
declare function snakeToPascal(name: string): string;
/**
 * Return camelCase version of snake_case string.
 * @internal
 */
declare function snakeToCamel(name: string): string;
/**
 * Print a warning message to console.
 * @internal
 **/
declare function logWarn(...args: any[]): void;
/**
 * Generate a UUID.
 *  @internal
 * */
declare function uuid(): string;
/** Generate a return url that Anchor will redirect back to w/o reload. */
declare function generateReturnUrl(): string | undefined;
declare function isAppleHandheld(): boolean;
declare function isChromeiOS(): boolean;
declare function isChromeMobile(): boolean;
declare function isFirefox(): boolean;
declare function isFirefoxiOS(): boolean;
declare function isOpera(): boolean;
declare function isEdge(): boolean;
declare function isBrave(): any;
declare function isAndroid(): boolean;
declare function isReactNativeApp(): boolean;
declare function isAndroidWebView(): boolean;
declare function isKnownMobile(): boolean;

export { BuoyInfo, BuoyMessage, BuoySession, IdentityRequestResponse, LinkCreate, LinkInfo, SealedMessage, WalletPluginOptions, createIdentityRequest, extractSignaturesFromCallback, generateReturnUrl, getUserAgent, isAndroid, isAndroidWebView, isAppleHandheld, isBrave, isCallback, isChromeMobile, isChromeiOS, isEdge, isFirefox, isFirefoxiOS, isKnownMobile, isOpera, isReactNativeApp, logWarn, prepareCallback, sealMessage, setTransactionCallback, snakeToCamel, snakeToPascal, uuid, verifyLoginCallbackResponse, waitForCallback };
