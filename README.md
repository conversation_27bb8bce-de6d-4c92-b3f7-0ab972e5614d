# NFT Access Demo

A web application that demonstrates NFT-gated access using EOS blockchain and Anchor wallet.

## Features

- Login with Anchor wallet
- Check if user owns NFTs from a specific collection
- Grant access based on NFT ownership
- Collection owner gets automatic access

## Current Configuration

- **Collection**: `easterpax23y`
- **Blockchain**: EOS Mainnet
- **Wallet**: Anchor

## CORS Issue and Solutions

### The Problem
When running this application directly in a browser (file:// or localhost), you'll encounter CORS (Cross-Origin Resource Sharing) errors when trying to access EOS API endpoints. This is a browser security feature that prevents web pages from making requests to different domains.

### Current Implementation
The current code includes mock data for development/testing purposes. When you test the application:
- It will simulate that the account `hokentechita` owns NFTs from the `easterpax23y` collection
- Other accounts will be denied access (as expected)

### Production Solutions

#### Option 1: Backend API (Recommended)
Create a backend server that acts as a proxy to EOS APIs.

#### Option 2: CORS Proxy Service
Use a CORS proxy service (not recommended for production).

#### Option 3: Local Web Server
Run a local web server instead of opening the file directly:
```bash
# Python
python -m http.server 8000

# Node.js
npx http-server

# PHP
php -S localhost:8000
```

## Installation

1. Install dependencies:
```bash
npm install
```

2. Open `index.html` in a web browser or serve it through a web server

## Usage

1. Click "Login with Anchor"
2. Connect your Anchor wallet
3. The app will check if your account owns NFTs from the `easterpax23y` collection
4. If you own NFTs (or are the collection owner), you'll get access
5. If not, access will be denied

## Customization

To change the required collection, update the collection name in:
- `main.js` (search for 'easterpax23y')
- `index.html` (description text)

## Troubleshooting

### "Failed to fetch" errors
This indicates CORS issues. Use one of the production solutions above.

### Wallet connection issues
- Make sure Anchor wallet is installed
- Clear stored sessions using the "Clear Stored Sessions" button
- Check browser console for detailed error messages