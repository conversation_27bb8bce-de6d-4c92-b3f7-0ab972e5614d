# NFT Access Demo

A simple demo showing how to create an NFT-gated website using WharfKit and Anchor wallet.

## Prerequisites

- Node.js (v14 or later)
- npm or yarn
- Anchor Wallet browser extension installed
- An EOS account with NFTs from the "yourfreenfts" collection

## Installation

1. Clone this repository
2. Install dependencies:

```bash
npm install
# or
yarn
```

3. Start the development server:

```bash
npm run dev
# or
yarn dev
```

4. Open your browser at http://localhost:3000

## How it works

This demo:

1. Allows users to login with their Anchor wallet
2. Checks if the authenticated user owns an NFT from the "yourfreenfts" collection
3. Only displays the protected content if the user has the required NFT
4. Shows information about the user's account and their NFT

## Customization

- To change the required NFT collection, modify the `collection_name` parameter in the API call in `main.js`
- To use a different blockchain, modify the `chains` configuration in the SessionKit initialization