init
Description:
Initializes the config and tokenconfigs table if it hasn't been initialized before, otherwise does nothing. Has to be called once after deploying the contract.

Required Authorization:
atomicassets account.

admincoledit
Parameters:
Type	Name	Description
vector<FORMAT>	collection_format_extension	Vector of FORMATs to be added to the collection_format that is used to serialize collection data
Description:
Adds one or more lines are added to the collection_format value in the config singleton. These lines are used to serialize the data of collections.

Required Authorization:
atomicassets account.

setversion
Parameters:
Type	Name	Description
string	new_version	New version string to be saved in the tokenconfigs table
Description:
Updates the version in the tokenconfigs table.

Required Authorization:
atomicassets account.

addconftoken
Parameters:
Type	Name	Description
name	token_contract	Contract that the token is stored in
symbol	token_symbol	Symbol of the token
Description:
Adds a new TOKEN to the supported_tokens vector in the config, which then makes it possible to back assets with the specified token.

Required Authorization:
atomicassets account.

createcol
Parameters:
Type	Name	Description
name	author	Account that creates the collections and can later modify it
name	collection_name	Name of the collection
bool	allow_notify	If this is set to false, it won't be possible to add accounts to the notify_accounts vector. This solves the problem of notify_accounts theoretically being able to block transfers by making the transactions throw, at the expense of notifications.
vector	authorized_accounts	Vector of account names that are authorized to create/ edit both templates and assets of this collection
vector	notify_accounts	Vector of accounts that will get notified of any actions related to this collection (More Info)
double	market_fee	3rd party marketplaces are encouraged to take this fee out of every asset sale of this collection and make it available to the collection's author
ATTRIBUTE_MAP	data	Data that the collection is initialized with, to be serialized with the collection_format specified in the config table
Description:
Creates a new collection. Collection names must be unique, so no other collection with the name <collection_name> can exist. Collections also can't have the name of an account that exists on the blockchain, except if the action has the authorization of that account.

Required Authorization:
<author>

setcoldata
Parameters:
Type	Name	Description
name	collection_name	Collection to set the data of
ATTRIBUTE_MAP	data	Data to be serialized with the collection_format specified in the config table
Description:
Updates/ sets the data of a collection.

Required Authorization:
Author of the collection

addcolauth
Parameters:
Type	Name	Description
name	collection_name	Name of the collection
name	account_to_add	Account to add to the authorized_accounts vector
Description:
Adds an account to the authroized_accounts vector of a collection.

Required Authorization:
Author of the collection

remcolauth
Parameters:
Type	Name	Description
name	collection_name	Name of the collection
name	account_to_remove	Account to remove from the authorized_accounts vector
Description:
Removes an account from the authroized_accounts vector of a collection.

Required Authorization:
Author of the collection

addnotifyacc
Parameters:
Type	Name	Description
name	collection_name	Name of the collection
name	account_to_add	Account to add to the notify_accounts vector
Description:
Adds an account to the notify_accounts vector of a collection.

Required Authorization:
Author of the collection

remnotifyacc
Parameters:
Type	Name	Description
name	collection_name	Name of the collection
name	account_to_remove	Account to remove from the notify_accounts vector
Description:
Removes an account from the notify_accounts vector of a collection.

Required Authorization:
Author of the collection

setmarketfee
Parameters:
Type	Name	Description
name	collection_name	Name of the collection
double	market_fee	New market_fee of the collection
Description:
Sets the market_fee of a collection. 3rd party marketplaces are encouraged to take this fee out of every asset sale of this collection and make it available to the collection's author.

Required Authorization:
Author of the collection

forbidnotify
Parameters:
Type	Name	Description
name	collection_name	Name of the collection
Description:
Sets the allow_notify value of a collection to false if it has previously been true. Only possible if the notify_accounts vector of the collection is empty.

Required Authorization:
Author of the collection

createschema
Parameters:
Type	Name	Description
name	authorized_creator	Account createing this schema
name	collection_name	Name of the collection that this schema belongs to
name	schema_name	Name of the schema
vector<FORMAT>	schema_format	Vector of FORMATs that the schema is initialized with. Each FORMAT describes one attribute that can be serialized by this schema.
Description:
Creates a new schema. Schema names must be unique within the parent collection. Schemas also can't have the name of an account that exists on the blockchain, except if the action has the authorization of that account.

Required Authorization:
<authorized_creator>, who needs to be in the authorized_accounts vector of the collection that the schema belongs to

extendschema
Parameters:
Type	Name	Description
name	authorized_editor	Account editing / extending this schema
name	collection_name	Name of the collection that the schema belongs to
name	schema_name	Schema to extend
vector<FORMAT>	schema_format_extension	Vector of FORMATs that the schema is extended by. Each FORMAT describes one attribute that can be serialized by this schema.
Description:
Extends the format of an existing schema.

Required Authorization:
<authorized_editor>, who needs to be in the authorized_accounts vector of the collection that the schema belongs to

createtempl
Parameters:
Type	Name	Description
name	authorized_creator	Account creating the template
name	collection_name	Collection this template and any child assets belong to
name	schema_name	Schema that is used for data serialization for this template and any child assets
bool	transferable	Determines if child assets are transferable
bool	burnable	Determines if child assets are burnable
uint32_t	max_supply	Maximum amount of child assets that this template can mint. 0 if there should not be a maximum
ATTRIBUTE_MAP	immutable_data	Data that can not be changed after creating the template
Description:
Creates a new template.

Required Authorization:
<authorized_creator>, who needs to be in the authorized_accounts vector of the specified collection

locktemplate
Parameters:
Type	Name	Description
name	authorized_editor	Account locking the template
name	collection_name	Name of the collection that the template belongs to
int32_t	template_id	ID of the template
Description:
Locks a template, by setting its max_supply to the current issued_supply, which means that no further assets of this template can be minted

Required Authorization:
<authorized_editor>, who needs to be in the authorized_accounts vector of the collection that the template belongs to

mintasset
Parameters:
Type	Name	Description
name	authorized_minter	Account minting the asset
name	collection_name	Collection that the asset belongs to
name	schema_name	Schema that the asset belongs to
int32_t	template_id	ID of the template referenced, or -1 if no template is referenced
name	new_asset_owner	Account that will own the minted asset
ATTRIBUTE_MAP	immutable_data	Data that can not be changed after creating the asset
ATTRIBUTE_MAP	mutable_data	Data that can be changed after creating the asset
vector<asset>	tokens_to_back	Vector of tokens that should be backed to the asset. This functionality is equivalent to minting the asset and then calling the backasset action for each of the tokens to back.
Description:
Mints (creates) a new asset. The RAM cost is paid by <authorized_minter>.

Required Authorization:
<authorized_minter>, who needs to be in the authorized_accounts vector of the collection that the parent template belongs to

setassetdata
Parameters:
Type	Name	Description
name	authorized_editor	Account editing the asset
name	asset_owner	Account owning the asset
uint64_t	asset_id	ID of the asset
ATTRIBUTE_MAP	new_mutable_data	Data to set the mutable data to
Description:
Updates/ sets the mutable data of an asset.

Required Authorization:
<authorized_editor>, who needs to be in the authorized_accounts vector of the collection that the asset's parent template belongs to

announcedepo
Parameters:
Type	Name	Description
name	owner	Account owning the asset
symbol	symbol_to_announce	Symbol that is added to the quantities vector in the owner's balances table row
Description:
Adds a zero value asset with the specified symbol to the quantities vector in the owner's balances table row. If no balances table row exists for the owner, a new one is created.

Because doing this costs RAM, this can't be done directly in the receipt of a transfer action. Therefore, using this action, a zero value asset is first added to owner's balance (taking up more RAM than before) so that then in the transfer action meant to deposit tokens, this zero value asset can be modified instead of having to add a new one.

Note: To pass a symbol to eosio as a string, use the following format: <precision>,<symbol_code>

So for example: "8,WAX"

Required Authorization:
<owner>

withdraw
Parameters:
Type	Name	Description
name	owner	Account owning the asset
asset	token_to_withdraw	Token to withdraw from the owner's balance
Description:
Withdraws tokens that have previously been deposited. Ideally, this shouldn't have to be used if you only deposit tokens as required when backing assets.

Required Authorization:
<owner>

backasset
Parameters:
Type	Name	Description
name	payer	Account paying for the tokens that are backed
asset	asset_owner	Owner of the asset that gets backed
asset	asset_id	ID of the asset that gets backed
asset	token_to_back	Token that the asset is backed with
Description:
Backs an asset with a fungible token. The back_quantity is deducted from payer's balance.

After the asset is backed, payer also pays for the full RAM cost of the asset.

More Info on backing tokens

Required Authorization:
<payer>

burnasset
Parameters:
Type	Name	Description
name	asset_owner	Account owning the asset
uint64_t	asset_id	ID of the asset
Description:
Burns (deletes) an asset. If there previously were core tokens backed for this asset, these core tokens are transferred to <owner>.

Note: Burning an asset does not decrement the issued_supply of the parent template.

Required Authorization:
<owner>

transfer
Parameters:
Type	Name	Description
name	from	Account to transfer assets from
name	to	Account to transfer assets to
vector<uint64_t>	asset_ids	Vector of asset ids that are to be transferred
string	memo	Memo attached to the transaction
Description:
Transfers one or more assets from one account to another. If the recipient did not previously own any assets, the sender pays the 112 bytes of RAM to create the recipient's scope in the assets table.

Note: Both <from> and <to> are notified using require_recipient().

Required Authorization:
<from>

createoffer
Parameters:
Type	Name	Description
name	sender	Account creating/ sending the offer
name	recipient	Account receiving the offer
vector<uint64_t>	sender_asset_ids	Vector of asset ids that the sender will give to the recipient
vector<uint64_t>	recipient_asset_ids	Vector of asset ids that the recipient will give to the sender
string	memo	Memo attached to the trade offer
Description:
Creates a two sided trade offer, that <recipient> can accept or decline. If the offer is accepted, the assets will automatically be transferred to the respective sides.

Note: Both <sender> and <recipient> are notified using require_recipient().

Note: Only a single asset needs to be included in a trade offer. That means that offers with one side empty are possible.

Note: An offer's validity is checked when they are created, but they are not automatically deleted when they become invalid (because one of the sides loses ownership of at least one of the items included in the offer).

Required Authorization:
<sender>

canceloffer
Parameters:
Type	Name	Description
uint64_t	offer_id	ID of the offer
Description:
Cancels (deletes) an offer.

Note: Both the offer sender and recipient are notified using require_recipient().

Required Authorization:
Sender of the offer

acceptoffer
Parameters:
Type	Name	Description
uint64_t	offer_id	ID of the offer
Description:
Accepts an offer. The assets included in the offer are automatically transferred to the respective sides.

Note: Both the offer sender and recipient are notified using require_recipient().

Required Authorization:
Recipient of the offer

declineoffer
Parameters:
Type	Name	Description
uint64_t	offer_id	ID of the offer
Description:
Declines (deletes) an offer.

Note: Both the offer sender and recipient are notified using require_recipient().

Required Authorization:
Recipient of the offer

payofferram
Parameters:
Type	Name	Description
name	payer	account who will pay for the RAM
uint64_t	offer_id	ID of the offer
Description:
Pays for the RAM cost of an offer. The intended purpose for this is to allow dapps to pay for the RAM of offers of their users.

Required Authorization:
<payer>