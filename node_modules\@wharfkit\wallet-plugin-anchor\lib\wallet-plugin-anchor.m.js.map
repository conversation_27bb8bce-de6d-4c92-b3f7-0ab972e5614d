{"version": 3, "file": "wallet-plugin-anchor.m.js", "sources": ["../src/translations/index.ts", "../src/index.ts"], "sourcesContent": [null, null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,0BAAe;IACX,EAAE;IACF,EAAE;AACF,IAAA,SAAS,EAAE,OAAO;AAClB,IAAA,SAAS,EAAE,OAAO;CACrB;;ACiCK,MAAO,kBAAmB,SAAQ,oBAAoB,CAAA;AAqBxD,IAAA,WAAA,CAAY,OAA6B,EAAA;AACrC,QAAA,KAAK,EAAE,CAAA;AAXX;;AAEG;QACH,IAAE,CAAA,EAAA,GAAG,QAAQ,CAAA;AAEb;;AAEG;QACH,IAAY,CAAA,YAAA,GAAG,mBAAmB,CAAA;AASlC;;AAEG;AACM,QAAA,IAAA,CAAA,MAAM,GAAuB;;AAElC,YAAA,mBAAmB,EAAE,KAAK;;AAE1B,YAAA,wBAAwB,EAAE,KAAK;SAClC,CAAA;AACD;;AAEG;AACM,QAAA,IAAA,CAAA,QAAQ,GAAyB,oBAAoB,CAAC,IAAI,CAAC;AAChE,YAAA,IAAI,EAAE,QAAQ;AACd,YAAA,WAAW,EAAE,EAAE;AACf,YAAA,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC;AACZ,gBAAA,IAAI,EAAE,w8CAAw8C;AAC98C,gBAAA,KAAK,EAAE,ggCAAggC;aAC1gC,CAAC;AACF,YAAA,QAAQ,EAAE,6BAA6B;AACvC,YAAA,QAAQ,EAAE,sCAAsC;AACnD,SAAA,CAAC,CAAA;QAzBE,IAAI,CAAC,OAAO,GAAG,OAAO,EAAE,OAAO,IAAI,wBAAwB,CAAA;AAC3D,QAAA,IAAI,CAAC,MAAM,GAAG,OAAO,EAAE,MAAM,CAAA;KAChC;AAwBD;;;;;AAKG;AACH,IAAA,KAAK,CAAC,OAAqB,EAAA;QACvB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACnC,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACpB,iBAAA,IAAI,CAAC,CAAC,QAAQ,KAAI;gBACf,OAAO,CAAC,QAAQ,CAAC,CAAA;AACrB,aAAC,CAAC;AACD,iBAAA,KAAK,CAAC,CAAC,KAAK,KAAI;gBACb,MAAM,CAAC,KAAK,CAAC,CAAA;AACjB,aAAC,CAAC,CAAA;AACV,SAAC,CAAC,CAAA;KACL;IAED,MAAM,WAAW,CAAC,OAAqB,EAAA;AACnC,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;AACb,YAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;AACrC,SAAA;;AAGD,QAAA,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;;QAG1C,MAAM,EAAC,QAAQ,EAAE,OAAO,EAAE,iBAAiB,EAAE,UAAU,EAAE,UAAU,EAAC,GAChE,MAAM,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;;AAGtD,QAAA,MAAM,QAAQ,GAAoB;AAC9B,YAAA;AACI,gBAAA,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,CAAC,CAAC,YAAY,EAAE,EAAC,OAAO,EAAE,eAAe,EAAC,CAAC;AAClD,gBAAA,IAAI,EAAE;oBACF,IAAI,EAAE,iBAAiB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC;oBACnD,KAAK,EAAE,CAAC,CAAC,YAAY,EAAE,EAAC,OAAO,EAAE,eAAe,EAAC,CAAC;AAClD,oBAAA,OAAO,EAAE,SAAS;AACrB,iBAAA;AACJ,aAAA;SACJ,CAAA;;QAGD,IAAI,CAAC,aAAa,EAAE,EAAE;YAClB,QAAQ,CAAC,OAAO,CAAC;AACb,gBAAA,IAAI,EAAE,IAAI;gBACV,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC;AAC5C,aAAA,CAAC,CAAA;AACL,SAAA;;AAGD,QAAA,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;;AAGpE,QAAA,MAAM,cAAc,GAAG,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC;YACtC,KAAK,EAAE,CAAC,CAAC,aAAa,EAAE,EAAC,OAAO,EAAE,qBAAqB,EAAC,CAAC;AACzD,YAAA,IAAI,EAAE,CAAC,CAAC,YAAY,EAAE;AAClB,gBAAA,OAAO,EACH,0FAA0F;aACjG,CAAC;YACF,QAAQ;AACX,SAAA,CAAC,CAAA;AAEF,QAAA,cAAc,CAAC,KAAK,CAAC,MAAK;;AAEtB,YAAA,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;AAChC,SAAC,CAAC,CAAA;;AAGF,QAAA,MAAM,gBAAgB,GAAoB,MAAM,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;AACzF,QAAA,2BAA2B,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAA;AAEtD,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE;AACvE,YAAA,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;AAC/C,SAAA;QAED,IAAI,gBAAgB,CAAC,OAAO,IAAI,gBAAgB,CAAC,QAAQ,IAAI,gBAAgB,CAAC,SAAS,EAAE;AACrF,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;AACjC,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;YACjC,IAAI,CAAC,IAAI,CAAC,SAAS;gBACf,gBAAgB,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAA;YAC1E,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAA;YAC/C,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,gBAAgB,CAAC,SAAS,CAAA;YAElD,IAAI;gBACA,IAAI,gBAAgB,CAAC,SAAS,EAAE;oBAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;oBACvD,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAA;oBAC1C,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAA;oBACxC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAA;AAC7C,iBAAA;AACJ,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;;AAEX,aAAA;AACJ,SAAA;AAED,QAAA,MAAM,gBAAgB,GAAG,MAAM,sBAAsB,CAAC,WAAW,CAC7D,gBAAgB,EAChB,OAAO,CAAC,UAAU,CACrB,CAAA;QAED,MAAM,aAAa,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA;QAE7E,OAAO;YACH,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;AAC7C,YAAA,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC;gBAClC,KAAK,EAAE,gBAAgB,CAAC,EAAE;gBAC1B,UAAU,EAAE,gBAAgB,CAAC,EAAE;aAClC,CAAC;YACF,aAAa;SAChB,CAAA;KACJ;AAED;;;;;;AAMG;;IAEH,IAAI,CACA,QAAgC,EAChC,OAAwB,EAAA;QAExB,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;KACtD;AAEO,IAAA,MAAM,oBAAoB,CAC9B,QAAgC,EAChC,OAAwB,EAAA;AAExB,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;AACb,YAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;AACrC,SAAA;;AAGD,QAAA,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;;QAG1C,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,CAAA;AAC3D,QAAA,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAA;AACtB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,CAAA;;AAGlE,QAAA,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,aAAa,CAAC,EAAC,WAAW,EAAE,QAAQ,CAAC,WAAW,EAAC,CAAC,CAAA;;QAGxF,eAAe,CAAC,UAAU,CACtB,MAAM,EACN,QAAQ,CAAC,IAAI,CAAC;YACV,UAAU;AACb,SAAA,CAAC,CACL,CAAA;;QAGD,MAAM,QAAQ,GAAG,sBAAsB,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QAEtE,MAAM,OAAO,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;;QAGnD,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,KAAK,CAAA;;AAGnD,QAAA,MAAM,iBAAiB,GAAG,eAAe,CAAC,KAAK,EAAE,CAAA;AACjD,QAAA,MAAM,SAAS,GAAG,iBAAiB,EAAE,CAAA;AACrC,QAAA,iBAAiB,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;AACjD,QAAA,IAAI,SAAS,EAAE;AACX,YAAA,iBAAiB,CAAC,UAAU,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;AACzD,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACtB,YAAA,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACrB,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;AAC7C,aAAA;iBAAM,IAAI,eAAe,EAAE,EAAE;AAC1B,gBAAA,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,eAAe,CAAA;AACzC,aAAA;AACJ,SAAA;QAED,MAAM,YAAY,GAAG,MAAK;AACtB,YAAA,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC;gBACf,KAAK,EAAE,CAAC,CAAC,8BAA8B,EAAE,EAAC,OAAO,EAAE,eAAe,EAAC,CAAC;AACpE,gBAAA,IAAI,EAAE,CAAC,CAAC,6BAA6B,EAAE;AACnC,oBAAA,OAAO,EACH,mFAAmF;iBAC1F,CAAC;AACF,gBAAA,QAAQ,EAAE;AACN,oBAAA;AACI,wBAAA,IAAI,EAAE,IAAI;AACV,wBAAA,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC;AACxB,qBAAA;AACD,oBAAA;AACI,wBAAA,IAAI,EAAE,MAAM;wBACZ,KAAK,EAAE,CAAC,CAAC,mCAAmC,EAAE,EAAC,OAAO,EAAE,aAAa,EAAC,CAAC;AACvE,wBAAA,IAAI,EAAE;AACF,4BAAA,IAAI,EAAE,MAAM,CAAC,iBAAiB,CAAC;4BAC/B,KAAK,EAAE,CAAC,CAAC,mCAAmC,EAAE,EAAC,OAAO,EAAE,aAAa,EAAC,CAAC;AAC1E,yBAAA;AACJ,qBAAA;AACJ,iBAAA;AACJ,aAAA,CAAC,CAAA;AACN,SAAC,CAAA;;AAGD,QAAA,MAAM,aAAa,GAA+B,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC;YAChE,KAAK,EAAE,CAAC,CAAC,gBAAgB,EAAE,EAAC,OAAO,EAAE,uBAAuB,EAAC,CAAC;AAC9D,YAAA,IAAI,EAAE,CAAC,CAAC,eAAe,EAAE;AACrB,gBAAA,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW;AAClC,gBAAA,OAAO,EAAE,CAAsC,mCAAA,EAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAA2C,yCAAA,CAAA;aAClH,CAAC;AACF,YAAA,QAAQ,EAAE;AACN,gBAAA;AACI,oBAAA,IAAI,EAAE,WAAW;AACjB,oBAAA,IAAI,EAAE;wBACF,KAAK,EAAE,CAAC,CAAC,gBAAgB,EAAE,EAAC,OAAO,EAAE,kCAAkC,EAAC,CAAC;AACzE,wBAAA,GAAG,EAAE,UAAU,CAAC,WAAW,EAAE;AAChC,qBAAA;AACJ,iBAAA;AACD,gBAAA;AACI,oBAAA,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,CAAC,CAAC,gBAAgB,EAAE,EAAC,OAAO,EAAE,sCAAsC,EAAC,CAAC;AAC7E,oBAAA,IAAI,EAAE;AACF,wBAAA,OAAO,EAAE,YAAY;AACjB,8BAAE,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC;AAC3D,8BAAE,YAAY;AAClB,wBAAA,KAAK,EAAE,CAAC,CAAC,gBAAgB,EAAE;AACvB,4BAAA,OAAO,EAAE,sCAAsC;yBAClD,CAAC;AACL,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA,CAAC,CAAA;;AAGF,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,MAAK;AAC1B,YAAA,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;AACb,gBAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;AACrC,aAAA;AACD,YAAA,aAAa,CAAC,MAAM,CAChB,CAAC,CAAC,eAAe,EAAE,EAAC,OAAO,EAAE,wCAAwC,EAAC,CAAC,CAC1E,CAAA;SACJ,EAAE,SAAS,CAAC,CAAA;;QAGb,aAAa,CAAC,KAAK,CAAC,MAAM,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;;AAG9C,QAAA,MAAM,eAAe,GAAG,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;;AAGjE,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACtB,YAAA,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAA;AACpD,YAAA,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;YACnE,MAAM,aAAa,GAAG,WAAW,CAC7B,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,iBAAiB,GAAG,eAAe,EAAE,MAAM,CAC/D,IAAI,EACJ,KAAK,EACL,MAAM,CACT,EACD,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EACrC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CACtC,CAAA;AAED,YAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAC,MAAM,EAAE,aAAa,EAAC,CAAC,CAAC,KAAK,EAAE;gBACnD,OAAO;gBACP,OAAO;AACV,aAAA,CAAC,CAAA;AACL,SAAA;AAAM,aAAA;;YAEH,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAA;AACpD,SAAA;;AAGD,QAAA,MAAM,gBAAgB,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC,CAAC,OAAO,CACjF,MAAK;;YAED,YAAY,CAAC,KAAK,CAAC,CAAA;AACvB,SAAC,CACJ,CAAA;AAED,QAAA,MAAM,aAAa,GACf,UAAU,CAAC,gBAAgB,CAAC;AAC5B,YAAA,6BAA6B,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAA;AAE9D,QAAA,IAAI,aAAa,EAAE;;AAEf,YAAA,MAAM,eAAe,GAAG,MAAM,sBAAsB,CAAC,WAAW,CAC5D,gBAAgB,EAChB,OAAO,CAAC,UAAU,CACrB,CAAA;;YAGD,OAAO;AACH,gBAAA,UAAU,EAAE,6BAA6B,CAAC,gBAAgB,CAAC;AAC3D,gBAAA,QAAQ,EAAE,eAAe;aAC5B,CAAA;AACJ,SAAA;AAED,QAAA,MAAM,WAAW,GAAG,CAAC,CAAC,qBAAqB,EAAE,EAAC,OAAO,EAAE,gCAAgC,EAAC,CAAC,CAAA;AAEzF,QAAA,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;;AAGjC,QAAA,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAA;KAC/B;AACJ;;;;"}