Structure
AtomicAssets has Collections, Schemas, Templates and Assets, which all serve different purposes and often reference each other. It is important to understand how these work to properly understand the AtomicAssets standard.

Collections
Collections = Dapps (e.g. Pokemon GO)
Collections group assets, schemas and templates together and manage the permissions for those. They also have a data field that follow a unified schema meant specifically for collection infos.

In order to create schemas and then templates and assets, it is necessary to create a collection and to add one or more accounts to the authorized_accounts list, as only the accounts in this list are authorized to create assets and templates of this collection.

Next to handling the permissions, collections are used to provide additional information to end-users. End-users are meant to see which collection an asset belongs to and the serialized collection data is meant to contain information like a name, description and a website URL. Applications, especially exchanges, will likely also offer functionality to filter assets by their collection.

Schemas
Schemas = Asset Types, (e.g. Pokemon / Pokeballs / Berries)
Schemas are used to define a data structure. They have nothing more a unique name that is used to reference them and a vector of FORMATs, each is a tuple of a unique name and a type, describing an attribute that the data structure has.

Schemas belong to a collection, and the collection's author can later extend the schema by adding more FORMATs, but it is impossible to remove existing FORMATs.

When later creating assets that belong to this schema, it will not be necessary to use all of the attributes defined in the schema's format. However, attributes that are not set will be displayed as null values by the official AtomicAssets API.

It makes sense for 3rd party sites and their users to group assets by the schemas they use, because all assets that use the same schema are thought the be the same "kind" of asset.

Templates
Templates = The same kind of assets (e.g. all Pikachus)
Templates are optional.

Templates' main purpose is to save RAM costs by storing data that is duplicate in a lot of similar assets only once. It is however also possible to define a max_supply within a template, in which case it could be used to group together assets with provable scarcity.

Just like assets, templates also reference a schema, which will be used to serialize the template's data.

Templates also have a transferable and a burnable field that determines whether it will be possible to transfer/ burn its child assets.

Assets
Assets = The actual asset (e.g. my Pikachu that is level 23 and learned Thunderbolt)
Assets are the core of the AtomicAssets standard. They reference a schema that is used to serialize the asset's data as well as a collection that they belong to.

They can also optionally reference a template, in which case the serialized data of the template will be treated as if it also was part of the asset's data. To find out how they inherit a template's data, check out Data Precendence.