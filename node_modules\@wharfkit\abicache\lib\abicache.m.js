import { ABI } from '@wharfkit/antelope';

class ABICache {
    constructor(client) {
        this.client = client;
        this.cache = new Map();
        this.pending = new Map();
    }
    async getAbi(account) {
        const key = String(account);
        let record = this.cache.get(key);
        if (!record) {
            let getAbi = this.pending.get(key);
            if (!getAbi) {
                getAbi = this.client.v1.chain.get_raw_abi(account);
                this.pending.set(key, getAbi);
            }
            const response = await getAbi;
            this.pending.delete(key);
            if (response.abi) {
                record = ABI.from(response.abi);
                this.cache.set(key, record);
            }
            else {
                throw new Error(`ABI for ${key} could not be loaded.`);
            }
        }
        return record;
    }
    setAbi(account, abiDef, merge = false) {
        const key = String(account);
        const abi = ABI.from(abiDef);
        const existing = this.cache.get(key);
        if (merge && existing) {
            this.cache.set(key, ABI.from({
                action_results: mergeAndDeduplicate(existing.action_results, abi.action_results),
                types: mergeAndDeduplicate(existing.types, abi.types, 'new_type_name'),
                structs: mergeAndDeduplicate(existing.structs, abi.structs),
                actions: mergeAndDeduplicate(existing.actions, abi.actions),
                tables: mergeAndDeduplicate(existing.tables, abi.tables),
                ricardian_clauses: mergeAndDeduplicate(existing.ricardian_clauses, abi.ricardian_clauses, 'id'),
                variants: mergeAndDeduplicate(existing.variants, abi.variants),
                version: abi.version,
            }));
        }
        else {
            this.cache.set(key, abi);
        }
    }
}
function mergeAndDeduplicate(array1, array2, byField = 'name') {
    return array2.reduce((acc, current) => {
        if (!acc.some((obj) => String(obj[byField]) === String(current[byField]))) {
            acc.push(current);
        }
        return acc;
    }, array1.slice());
}

export { ABICache, ABICache as default };
//# sourceMappingURL=abicache.m.js.map
