{"version": 3, "sources": ["../../eventemitter3/index.js", "../../@greymass/buoy/src/errors.ts", "../../@greymass/buoy/src/listener.ts", "../../@greymass/buoy/src/receive.ts", "../../@greymass/buoy/src/send.ts", "../../@greymass/miniaes/src/aes.asm.js", "../../@greymass/miniaes/src/utils.ts", "../../@greymass/miniaes/src/aes.ts", "../../@greymass/miniaes/src/cbc.ts", "../../@wharfkit/protocol-esr/node_modules/tslib/tslib.es6.js", "../../@wharfkit/protocol-esr/src/anchor-types.ts", "../../@wharfkit/protocol-esr/src/buoy-types.ts", "../../@wharfkit/protocol-esr/src/callback.ts", "../../@wharfkit/protocol-esr/src/utils.ts", "../../@wharfkit/protocol-esr/src/esr.ts", "../../@wharfkit/wallet-plugin-anchor/src/translations/index.ts", "../../@wharfkit/wallet-plugin-anchor/src/index.ts"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "/** Emitted when a network error occurs, can safely be ignored. */\nexport class SocketError extends Error {\n    code = 'E_NETWORK'\n    constructor(readonly event: Event) {\n        super('Socket error')\n    }\n}\n\n/** Emitted when a message fails to parse or read, non-recoverable. */\nexport class MessageError extends Error {\n    code = 'E_MESSAGE'\n    constructor(readonly reason: string, readonly underlyingError?: Error) {\n        super(reason)\n    }\n}\n", "import EventEmitter from 'eventemitter3'\n\nimport type {Options} from './options'\nimport {MessageError, SocketError} from './errors'\n\nconst globalBuoy = globalThis || window\n\nexport enum ListenerEncoding {\n    binary = 'binary',\n    text = 'text',\n    json = 'json',\n}\n\nexport interface ListenerOptions extends Options {\n    /** Auto-connect when instantiated, defaults to true. */\n    autoConnect?: boolean\n    /** Attempt to parse incoming messages as JSON. */\n    json?: boolean\n    /** Receive encoding for incoming messages, defaults to text. */\n    encoding?: ListenerEncoding\n    /** WebSocket class to use, if unset will try to use global WebSocket. */\n    WebSocket?: any\n}\n\nexport class Listener extends EventEmitter {\n    readonly url: string\n\n    private active = false\n    private socket?: WebSocket\n    private timer?: any\n    private reconnectTimer?: any\n    private encoding: ListenerEncoding\n    private WebSocket: typeof WebSocket\n\n    constructor(options: ListenerOptions) {\n        super()\n        if (!options.service) {\n            throw new Error('Options must include a service url')\n        }\n        if (!options.channel) {\n            throw new Error('Options must include a channel name')\n        }\n        const baseUrl = options.service.replace(/^http/, 'ws').replace(/\\/$/, '')\n        this.url = `${baseUrl}/${options.channel}?v=2`\n        this.encoding = options.encoding || ListenerEncoding.text\n        this.WebSocket = options.WebSocket || globalBuoy.WebSocket\n        if (options.autoConnect !== false) {\n            this.connect()\n        }\n    }\n\n    connect() {\n        if (this.active) return\n        this.active = true\n        let retries = 0\n        let pingTimer: any\n\n        const connect = () => {\n            const socket = new this.WebSocket(this.url)\n            socket.onmessage = (event) => {\n                if (typeof Blob !== 'undefined' && event.data instanceof Blob) {\n                    const reader = new FileReader()\n                    reader.onload = () => {\n                        this.handleMessage(new Uint8Array(reader.result as ArrayBuffer))\n                    }\n                    reader.onerror = () => {\n                        this.emit('error', new MessageError('Could not read message'))\n                    }\n                    reader.readAsArrayBuffer(event.data)\n                } else if (typeof event.data === 'string') {\n                    this.handleMessage(new TextEncoder().encode(event.data))\n                } else if (\n                    typeof globalBuoy.Buffer !== 'undefined' &&\n                    (event.data instanceof globalBuoy.Buffer || Array.isArray(event.data))\n                ) {\n                    let buffer = event.data\n                    if (!globalBuoy.Buffer.isBuffer(buffer)) {\n                        buffer = globalBuoy.Buffer.concat(buffer)\n                    }\n                    this.handleMessage(\n                        new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.byteLength)\n                    )\n                } else if (event.data instanceof Uint8Array) {\n                    this.handleMessage(event.data)\n                } else if (event.data instanceof ArrayBuffer) {\n                    this.handleMessage(new Uint8Array(event.data))\n                } else {\n                    this.emit('error', new MessageError('Unhandled event data type'))\n                }\n            }\n            socket.onerror = (event) => {\n                if (this.socket === socket && this.active) {\n                    this.emit('error', new SocketError(event))\n                }\n            }\n            socket.onopen = () => {\n                retries = 0\n                this.emit('connect')\n            }\n            socket.onclose = () => {\n                if (this.active) {\n                    clearTimeout(this.timer)\n                    this.timer = setTimeout(connect, backoff(retries++))\n                }\n                this.socket = undefined\n                clearTimeout(pingTimer)\n                if (this.reconnectTimer) {\n                    clearInterval(this.reconnectTimer)\n                }\n                this.emit('disconnect')\n            }\n\n            // Reconnect every 10 mins to keep the connection alive\n            this.setupReconnectionTimer()\n            // fix problem where node.js does not react to the socket going down\n            // this terminates the connection if we don't get a heartbeat in 15s (buoy-nodejs sends every 10s)\n            const nodeSocket = socket as any\n            if (typeof nodeSocket.on === 'function' && typeof nodeSocket.terminate === 'function') {\n                nodeSocket.on('ping', () => {\n                    clearTimeout(pingTimer)\n                    pingTimer = setTimeout(() => {\n                        nodeSocket.terminate()\n                    }, 15 * 1000)\n                })\n            }\n            this.socket = socket\n        }\n        connect()\n    }\n\n    disconnect() {\n        this.active = false\n        if (\n            this.socket &&\n            (this.socket.readyState === this.WebSocket.OPEN ||\n                this.socket.readyState === this.WebSocket.CONNECTING)\n        ) {\n            this.socket.close(1000)\n        }\n    }\n\n    get isConnected(): boolean {\n        return this.active && this.socket?.readyState == this.WebSocket.OPEN\n    }\n\n    private handleMessage(bytes: Uint8Array) {\n        if (bytes[0] === 0x42 && bytes[1] === 0x42 && bytes[2] === 0x01) {\n            this.socket?.send(new Uint8Array([0x42, 0x42, 0x02, bytes[3]]))\n            bytes = bytes.subarray(4)\n        }\n        let message: any\n        switch (this.encoding) {\n            case ListenerEncoding.binary:\n                message = bytes\n                break\n            case ListenerEncoding.text:\n                message = new TextDecoder().decode(bytes)\n                break\n            case ListenerEncoding.json: {\n                try {\n                    message = JSON.parse(new TextDecoder().decode(bytes))\n                } catch (error) {\n                    this.emit('error', new MessageError('Unable to decode JSON', error))\n                    return\n                }\n            }\n        }\n        this.emit('message', message)\n    }\n\n    private setupReconnectionTimer() {\n        this.reconnectTimer = setInterval(() => {\n            this.socket?.close(1000)\n        }, 10 * 60 * 1000)\n    }\n}\n\n/**\n * Exponential backoff function that caps off at 5s after 10 tries.\n * @internal\n */\nfunction backoff(tries: number): number {\n    return Math.min(Math.pow(tries * 7, 2), 5 * 1000)\n}\n", "import {MessageError, SocketError} from './errors'\nimport {Listener, ListenerOptions} from './listener'\n\nexport interface ReceiveContext {\n    /** Can be called by sender to cancel the receive. */\n    cancel?: () => void\n}\n\nexport interface ReceiveOptions extends ListenerOptions {\n    /** How many milliseconds to wait before giving up. */\n    timeout?: number\n}\n\n/**\n * Receive a single message from a buoy channel.\n * @note Instantiate a [[Listener]] if you want to receive multiple messages over the same channel.\n */\nexport function receive(options: ReceiveOptions, ctx?: ReceiveContext): Promise<any> {\n    return new Promise<any>((resolve, reject) => {\n        const listener = new Listener({...options, autoConnect: true})\n        let timer: any\n        let lastError: Error | undefined\n        const done = (error?: Error, message?: any) => {\n            clearTimeout(timer)\n            if (error) {\n                reject(error)\n            } else {\n                resolve(message)\n            }\n            listener.disconnect()\n        }\n        if (ctx) {\n            ctx.cancel = () => {\n                done(new MessageError('Cancelled', lastError))\n            }\n        }\n        if (options.timeout) {\n            timer = setTimeout(() => {\n                done(new MessageError('Timed out', lastError))\n            }, options.timeout)\n        }\n        listener.on('error', (error) => {\n            if (!(error instanceof SocketError)) {\n                done(error)\n            } else {\n                lastError = error\n            }\n        })\n        listener.once('message', (message) => {\n            done(undefined, message)\n        })\n    })\n}\n", "import type {Options} from './options'\n\nconst globalBuoy = globalThis || window\n\n/** Options for the [[send]] method. */\ninterface SendOptions extends Options {\n    /**\n     * How many milliseconds to wait for delivery.\n     * If used in conjunction with requireDelivery the promise will reject\n     * if the message is not delivered within the given timeout.\n     */\n    timeout?: number\n    /** Whether to only return on a guaranteed delivery. Can only be used if timeout is set. */\n    requireDelivery?: boolean\n    /** Fetch function to use, if unset will attempt to use global fetch. */\n    fetch?: typeof fetch\n}\n\n/** Result of a [[send]] call. */\nexport enum SendResult {\n    /** Message was sent but not yet delivered. */\n    buffered = 'buffered',\n    /** Message was delivered to at least 1 listener on the channel. */\n    delivered = 'delivered',\n}\n\n/** A JSON-encodable value. */\ntype JSONValue = string | number | boolean | null | JSONValue[] | {[key: string]: JSONValue}\n\n/** Data to send, either a string, uint8array or an object that can be JSON encoded. */\nexport type SendData = string | Uint8Array | JSONValue\n\n/**\n * Sends a message to the channel.\n * @returns a promise that resolves to a [[SendResult]].\n * @throws if the message can't be delivered if [[SendOptions.requireDelivery]] is set.\n */\nexport async function send(message: SendData, options: SendOptions): Promise<SendResult> {\n    const fetch = options.fetch || globalBuoy.fetch\n    const baseUrl = options.service.replace(/^ws/, 'http').replace(/\\/$/, '')\n    const url = `${baseUrl}/${options.channel}`\n\n    const headers: Record<string, string> = {}\n    if (options.requireDelivery) {\n        if (!options.timeout) {\n            throw new Error('requireDelivery can only be used with timeout')\n        }\n        headers['X-Buoy-Wait'] = `${Math.ceil(options.timeout / 1000)}`\n    } else if (options.timeout) {\n        headers['X-Buoy-Soft-Wait'] = `${Math.ceil(options.timeout / 1000)}`\n    }\n\n    let body: string | Uint8Array\n    if (typeof message === 'string' || message instanceof Uint8Array) {\n        body = message\n    } else {\n        body = JSON.stringify(message)\n    }\n    const response = await fetch(url, {method: 'POST', body, headers})\n\n    if (Math.floor(response.status / 100) !== 2) {\n        if (response.status === 408) {\n            throw new Error('Unable to deliver message')\n        } else if (response.status === 410) {\n            throw new Error('Request cancelled')\n        } else {\n            throw new Error(`Unexpected status code ${response.status}`)\n        }\n    }\n\n    return (response.headers.get('X-Buoy-Delivery') || SendResult.buffered) as SendResult\n}\n", "/* eslint-disable */\n/**\n * @file {@link http://asmjs.org Asm.js} implementation of the {@link https://en.wikipedia.org/wiki/Advanced_Encryption_Standard Advanced Encryption Standard}.\n * <AUTHOR> <PERSON> <v<PERSON><PERSON><PERSON>@gmail.com>\n * @license MIT\n */\n export var AES_asm = function () {\n  \"use strict\";\n\n  /**\n   * Galois Field stuff init flag\n   */\n  var ginit_done = false;\n\n  /**\n   * Galois Field exponentiation and logarithm tables for 3 (the generator)\n   */\n  var gexp3, glog3;\n\n  /**\n   * Init Galois Field tables\n   */\n  function ginit() {\n    gexp3 = [],\n      glog3 = [];\n\n    var a = 1, c, d;\n    for (c = 0; c < 255; c++) {\n      gexp3[c] = a;\n\n      // Multiply by three\n      d = a & 0x80, a <<= 1, a &= 255;\n      if (d === 0x80) a ^= 0x1b;\n      a ^= gexp3[c];\n\n      // Set the log table value\n      glog3[gexp3[c]] = c;\n    }\n    gexp3[255] = gexp3[0];\n    glog3[0] = 0;\n\n    ginit_done = true;\n  }\n\n  /**\n   * Galois Field multiplication\n   * @param {number} a\n   * @param {number} b\n   * @return {number}\n   */\n  function gmul(a, b) {\n    var c = gexp3[(glog3[a] + glog3[b]) % 255];\n    if (a === 0 || b === 0) c = 0;\n    return c;\n  }\n\n  /**\n   * Galois Field reciprocal\n   * @param {number} a\n   * @return {number}\n   */\n  function ginv(a) {\n    var i = gexp3[255 - glog3[a]];\n    if (a === 0) i = 0;\n    return i;\n  }\n\n  /**\n   * AES stuff init flag\n   */\n  var aes_init_done = false;\n\n  /**\n   * Encryption, Decryption, S-Box and KeyTransform tables\n   *\n   * @type {number[]}\n   */\n  var aes_sbox;\n\n  /**\n   * @type {number[]}\n   */\n  var aes_sinv;\n\n  /**\n   * @type {number[][]}\n   */\n  var aes_enc;\n\n  /**\n   * @type {number[][]}\n   */\n  var aes_dec;\n\n  /**\n   * Init AES tables\n   */\n  function aes_init() {\n    if (!ginit_done) ginit();\n\n    // Calculates AES S-Box value\n    function _s(a) {\n      var c, s, x;\n      s = x = ginv(a);\n      for (c = 0; c < 4; c++) {\n        s = ((s << 1) | (s >>> 7)) & 255;\n        x ^= s;\n      }\n      x ^= 99;\n      return x;\n    }\n\n    // Tables\n    aes_sbox = [],\n      aes_sinv = [],\n      aes_enc = [[], [], [], []],\n      aes_dec = [[], [], [], []];\n\n    for (var i = 0; i < 256; i++) {\n      var s = _s(i);\n\n      // S-Box and its inverse\n      aes_sbox[i] = s;\n      aes_sinv[s] = i;\n\n      // Ecryption and Decryption tables\n      aes_enc[0][i] = (gmul(2, s) << 24) | (s << 16) | (s << 8) | gmul(3, s);\n      aes_dec[0][s] = (gmul(14, i) << 24) | (gmul(9, i) << 16) | (gmul(13, i) << 8) | gmul(11, i);\n      // Rotate tables\n      for (var t = 1; t < 4; t++) {\n        aes_enc[t][i] = (aes_enc[t - 1][i] >>> 8) | (aes_enc[t - 1][i] << 24);\n        aes_dec[t][s] = (aes_dec[t - 1][s] >>> 8) | (aes_dec[t - 1][s] << 24);\n      }\n    }\n\n    aes_init_done = true;\n  }\n\n  /**\n   * Asm.js module constructor.\n   *\n   * <p>\n   * Heap buffer layout by offset:\n   * <pre>\n   * 0x0000   encryption key schedule\n   * 0x0400   decryption key schedule\n   * 0x0800   sbox\n   * 0x0c00   inv sbox\n   * 0x1000   encryption tables\n   * 0x2000   decryption tables\n   * 0x3000   reserved (future GCM multiplication lookup table)\n   * 0x4000   data\n   * </pre>\n   * Don't touch anything before <code>0x400</code>.\n   * </p>\n   *\n   * @alias AES_asm\n   * @class\n   * @param foreign - <i>ignored</i>\n   * @param buffer - heap buffer to link with\n   * @type any\n   */\n  var wrapper = function (foreign, buffer) {\n    // Init AES stuff for the first time\n    if (!aes_init_done) aes_init();\n\n    // Fill up AES tables\n    var heap = new Uint32Array(buffer);\n    heap.set(aes_sbox, 0x0800 >> 2);\n    heap.set(aes_sinv, 0x0c00 >> 2);\n    for (var i = 0; i < 4; i++) {\n      heap.set(aes_enc[i], (0x1000 + 0x400 * i) >> 2);\n      heap.set(aes_dec[i], (0x2000 + 0x400 * i) >> 2);\n    }\n\n    /**\n     * Calculate AES key schedules.\n     * @instance\n     * @memberof AES_asm\n     * @param {number} ks - key size, 4/6/8 (for 128/192/256-bit key correspondingly)\n     * @param {number} k0 - key vector components\n     * @param {number} k1 - key vector components\n     * @param {number} k2 - key vector components\n     * @param {number} k3 - key vector components\n     * @param {number} k4 - key vector components\n     * @param {number} k5 - key vector components\n     * @param {number} k6 - key vector components\n     * @param {number} k7 - key vector components\n     */\n    function set_key(ks, k0, k1, k2, k3, k4, k5, k6, k7) {\n      var ekeys = heap.subarray(0x000, 60),\n        dkeys = heap.subarray(0x100, 0x100 + 60);\n\n      // Encryption key schedule\n      ekeys.set([k0, k1, k2, k3, k4, k5, k6, k7]);\n      for (var i = ks, rcon = 1; i < 4 * ks + 28; i++) {\n        var k = ekeys[i - 1];\n        if ((i % ks === 0) || (ks === 8 && i % ks === 4)) {\n          k = aes_sbox[k >>> 24] << 24 ^ aes_sbox[k >>> 16 & 255] << 16 ^ aes_sbox[k >>> 8 & 255] << 8 ^ aes_sbox[k & 255];\n        }\n        if (i % ks === 0) {\n          k = (k << 8) ^ (k >>> 24) ^ (rcon << 24);\n          rcon = (rcon << 1) ^ ((rcon & 0x80) ? 0x1b : 0);\n        }\n        ekeys[i] = ekeys[i - ks] ^ k;\n      }\n\n      // Decryption key schedule\n      for (var j = 0; j < i; j += 4) {\n        for (var jj = 0; jj < 4; jj++) {\n          var k = ekeys[i - (4 + j) + (4 - jj) % 4];\n          if (j < 4 || j >= i - 4) {\n            dkeys[j + jj] = k;\n          } else {\n            dkeys[j + jj] = aes_dec[0][aes_sbox[k >>> 24]]\n              ^ aes_dec[1][aes_sbox[k >>> 16 & 255]]\n              ^ aes_dec[2][aes_sbox[k >>> 8 & 255]]\n              ^ aes_dec[3][aes_sbox[k & 255]];\n          }\n        }\n      }\n\n      // Set rounds number\n      asm.set_rounds(ks + 5);\n    }\n\n    // create library object with necessary properties\n    var stdlib = {Uint8Array: Uint8Array, Uint32Array: Uint32Array};\n\n    var asm = function (stdlib, foreign, buffer) {\n      \"use asm\";\n\n      var S0 = 0, S1 = 0, S2 = 0, S3 = 0,\n        I0 = 0, I1 = 0, I2 = 0, I3 = 0,\n        N0 = 0, N1 = 0, N2 = 0, N3 = 0,\n        M0 = 0, M1 = 0, M2 = 0, M3 = 0,\n        H0 = 0, H1 = 0, H2 = 0, H3 = 0,\n        R = 0;\n\n      var HEAP = new stdlib.Uint32Array(buffer),\n        DATA = new stdlib.Uint8Array(buffer);\n\n      /**\n       * AES core\n       * @param {number} k - precomputed key schedule offset\n       * @param {number} s - precomputed sbox table offset\n       * @param {number} t - precomputed round table offset\n       * @param {number} r - number of inner rounds to perform\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      function _core(k, s, t, r, x0, x1, x2, x3) {\n        k = k | 0;\n        s = s | 0;\n        t = t | 0;\n        r = r | 0;\n        x0 = x0 | 0;\n        x1 = x1 | 0;\n        x2 = x2 | 0;\n        x3 = x3 | 0;\n\n        var t1 = 0, t2 = 0, t3 = 0,\n          y0 = 0, y1 = 0, y2 = 0, y3 = 0,\n          i = 0;\n\n        t1 = t | 0x400, t2 = t | 0x800, t3 = t | 0xc00;\n\n        // round 0\n        x0 = x0 ^ HEAP[(k | 0) >> 2],\n          x1 = x1 ^ HEAP[(k | 4) >> 2],\n          x2 = x2 ^ HEAP[(k | 8) >> 2],\n          x3 = x3 ^ HEAP[(k | 12) >> 2];\n\n        // round 1..r\n        for (i = 16; (i | 0) <= (r << 4); i = (i + 16) | 0) {\n          y0 = HEAP[(t | x0 >> 22 & 1020) >> 2] ^ HEAP[(t1 | x1 >> 14 & 1020) >> 2] ^ HEAP[(t2 | x2 >> 6 & 1020) >> 2] ^ HEAP[(t3 | x3 << 2 & 1020) >> 2] ^ HEAP[(k | i | 0) >> 2],\n            y1 = HEAP[(t | x1 >> 22 & 1020) >> 2] ^ HEAP[(t1 | x2 >> 14 & 1020) >> 2] ^ HEAP[(t2 | x3 >> 6 & 1020) >> 2] ^ HEAP[(t3 | x0 << 2 & 1020) >> 2] ^ HEAP[(k | i | 4) >> 2],\n            y2 = HEAP[(t | x2 >> 22 & 1020) >> 2] ^ HEAP[(t1 | x3 >> 14 & 1020) >> 2] ^ HEAP[(t2 | x0 >> 6 & 1020) >> 2] ^ HEAP[(t3 | x1 << 2 & 1020) >> 2] ^ HEAP[(k | i | 8) >> 2],\n            y3 = HEAP[(t | x3 >> 22 & 1020) >> 2] ^ HEAP[(t1 | x0 >> 14 & 1020) >> 2] ^ HEAP[(t2 | x1 >> 6 & 1020) >> 2] ^ HEAP[(t3 | x2 << 2 & 1020) >> 2] ^ HEAP[(k | i | 12) >> 2];\n          x0 = y0, x1 = y1, x2 = y2, x3 = y3;\n        }\n\n        // final round\n        S0 = HEAP[(s | x0 >> 22 & 1020) >> 2] << 24 ^ HEAP[(s | x1 >> 14 & 1020) >> 2] << 16 ^ HEAP[(s | x2 >> 6 & 1020) >> 2] << 8 ^ HEAP[(s | x3 << 2 & 1020) >> 2] ^ HEAP[(k | i | 0) >> 2],\n          S1 = HEAP[(s | x1 >> 22 & 1020) >> 2] << 24 ^ HEAP[(s | x2 >> 14 & 1020) >> 2] << 16 ^ HEAP[(s | x3 >> 6 & 1020) >> 2] << 8 ^ HEAP[(s | x0 << 2 & 1020) >> 2] ^ HEAP[(k | i | 4) >> 2],\n          S2 = HEAP[(s | x2 >> 22 & 1020) >> 2] << 24 ^ HEAP[(s | x3 >> 14 & 1020) >> 2] << 16 ^ HEAP[(s | x0 >> 6 & 1020) >> 2] << 8 ^ HEAP[(s | x1 << 2 & 1020) >> 2] ^ HEAP[(k | i | 8) >> 2],\n          S3 = HEAP[(s | x3 >> 22 & 1020) >> 2] << 24 ^ HEAP[(s | x0 >> 14 & 1020) >> 2] << 16 ^ HEAP[(s | x1 >> 6 & 1020) >> 2] << 8 ^ HEAP[(s | x2 << 2 & 1020) >> 2] ^ HEAP[(k | i | 12) >> 2];\n      }\n\n      /**\n       * ECB mode encryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      // function _ecb_enc(x0, x1, x2, x3) {\n      //   x0 = x0 | 0;\n      //   x1 = x1 | 0;\n      //   x2 = x2 | 0;\n      //   x3 = x3 | 0;\n\n      //   _core(\n      //     0x0000, 0x0800, 0x1000,\n      //     R,\n      //     x0,\n      //     x1,\n      //     x2,\n      //     x3\n      //   );\n      // }\n\n      /**\n       * ECB mode decryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      // function _ecb_dec(x0, x1, x2, x3) {\n      //   x0 = x0 | 0;\n      //   x1 = x1 | 0;\n      //   x2 = x2 | 0;\n      //   x3 = x3 | 0;\n\n      //   var t = 0;\n\n      //   _core(\n      //     0x0400, 0x0c00, 0x2000,\n      //     R,\n      //     x0,\n      //     x3,\n      //     x2,\n      //     x1\n      //   );\n\n      //   t = S1, S1 = S3, S3 = t;\n      // }\n\n\n      /**\n       * CBC mode encryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      function _cbc_enc(x0, x1, x2, x3) {\n        x0 = x0 | 0;\n        x1 = x1 | 0;\n        x2 = x2 | 0;\n        x3 = x3 | 0;\n\n        _core(\n          0x0000, 0x0800, 0x1000,\n          R,\n          I0 ^ x0,\n          I1 ^ x1,\n          I2 ^ x2,\n          I3 ^ x3\n        );\n\n        I0 = S0,\n          I1 = S1,\n          I2 = S2,\n          I3 = S3;\n      }\n\n      /**\n       * CBC mode decryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      function _cbc_dec(x0, x1, x2, x3) {\n        x0 = x0 | 0;\n        x1 = x1 | 0;\n        x2 = x2 | 0;\n        x3 = x3 | 0;\n\n        var t = 0;\n\n        _core(\n          0x0400, 0x0c00, 0x2000,\n          R,\n          x0,\n          x3,\n          x2,\n          x1\n        );\n\n        t = S1, S1 = S3, S3 = t;\n\n        S0 = S0 ^ I0,\n          S1 = S1 ^ I1,\n          S2 = S2 ^ I2,\n          S3 = S3 ^ I3;\n\n        I0 = x0,\n          I1 = x1,\n          I2 = x2,\n          I3 = x3;\n      }\n\n      /**\n       * CFB mode encryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      // function _cfb_enc(x0, x1, x2, x3) {\n      //   x0 = x0 | 0;\n      //   x1 = x1 | 0;\n      //   x2 = x2 | 0;\n      //   x3 = x3 | 0;\n\n      //   _core(\n      //     0x0000, 0x0800, 0x1000,\n      //     R,\n      //     I0,\n      //     I1,\n      //     I2,\n      //     I3\n      //   );\n\n      //   I0 = S0 = S0 ^ x0,\n      //     I1 = S1 = S1 ^ x1,\n      //     I2 = S2 = S2 ^ x2,\n      //     I3 = S3 = S3 ^ x3;\n      // }\n\n\n      /**\n       * CFB mode decryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      // function _cfb_dec(x0, x1, x2, x3) {\n      //   x0 = x0 | 0;\n      //   x1 = x1 | 0;\n      //   x2 = x2 | 0;\n      //   x3 = x3 | 0;\n\n      //   _core(\n      //     0x0000, 0x0800, 0x1000,\n      //     R,\n      //     I0,\n      //     I1,\n      //     I2,\n      //     I3\n      //   );\n\n      //   S0 = S0 ^ x0,\n      //     S1 = S1 ^ x1,\n      //     S2 = S2 ^ x2,\n      //     S3 = S3 ^ x3;\n\n      //   I0 = x0,\n      //     I1 = x1,\n      //     I2 = x2,\n      //     I3 = x3;\n      // }\n\n      /**\n       * OFB mode encryption / decryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      // function _ofb(x0, x1, x2, x3) {\n      //   x0 = x0 | 0;\n      //   x1 = x1 | 0;\n      //   x2 = x2 | 0;\n      //   x3 = x3 | 0;\n\n      //   _core(\n      //     0x0000, 0x0800, 0x1000,\n      //     R,\n      //     I0,\n      //     I1,\n      //     I2,\n      //     I3\n      //   );\n\n      //   I0 = S0,\n      //     I1 = S1,\n      //     I2 = S2,\n      //     I3 = S3;\n\n      //   S0 = S0 ^ x0,\n      //     S1 = S1 ^ x1,\n      //     S2 = S2 ^ x2,\n      //     S3 = S3 ^ x3;\n      // }\n\n      /**\n       * CTR mode encryption / decryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      // function _ctr(x0, x1, x2, x3) {\n      //   x0 = x0 | 0;\n      //   x1 = x1 | 0;\n      //   x2 = x2 | 0;\n      //   x3 = x3 | 0;\n\n      //   _core(\n      //     0x0000, 0x0800, 0x1000,\n      //     R,\n      //     N0,\n      //     N1,\n      //     N2,\n      //     N3\n      //   );\n\n      //   N3 = (~M3 & N3) | M3 & (N3 + 1);\n      //     N2 = (~M2 & N2) | M2 & (N2 + ((N3 | 0) == 0));\n      //     N1 = (~M1 & N1) | M1 & (N1 + ((N2 | 0) == 0));\n      //     N0 = (~M0 & N0) | M0 & (N0 + ((N1 | 0) == 0));\n\n      //   S0 = S0 ^ x0;\n      //     S1 = S1 ^ x1;\n      //     S2 = S2 ^ x2;\n      //     S3 = S3 ^ x3;\n      // }\n\n      /**\n       * GCM mode MAC calculation\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      function _gcm_mac(x0, x1, x2, x3) {\n        x0 = x0 | 0;\n        x1 = x1 | 0;\n        x2 = x2 | 0;\n        x3 = x3 | 0;\n\n        var y0 = 0, y1 = 0, y2 = 0, y3 = 0,\n          z0 = 0, z1 = 0, z2 = 0, z3 = 0,\n          i = 0, c = 0;\n\n        x0 = x0 ^ I0,\n          x1 = x1 ^ I1,\n          x2 = x2 ^ I2,\n          x3 = x3 ^ I3;\n\n        y0 = H0 | 0,\n          y1 = H1 | 0,\n          y2 = H2 | 0,\n          y3 = H3 | 0;\n\n        for (; (i | 0) < 128; i = (i + 1) | 0) {\n          if (y0 >>> 31) {\n            z0 = z0 ^ x0,\n              z1 = z1 ^ x1,\n              z2 = z2 ^ x2,\n              z3 = z3 ^ x3;\n          }\n\n          y0 = (y0 << 1) | (y1 >>> 31),\n            y1 = (y1 << 1) | (y2 >>> 31),\n            y2 = (y2 << 1) | (y3 >>> 31),\n            y3 = (y3 << 1);\n\n          c = x3 & 1;\n\n          x3 = (x3 >>> 1) | (x2 << 31),\n            x2 = (x2 >>> 1) | (x1 << 31),\n            x1 = (x1 >>> 1) | (x0 << 31),\n            x0 = (x0 >>> 1);\n\n          if (c) x0 = x0 ^ 0xe1000000;\n        }\n\n        I0 = z0,\n          I1 = z1,\n          I2 = z2,\n          I3 = z3;\n      }\n\n      /**\n       * Set the internal rounds number.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} r - number if inner AES rounds\n       */\n      function set_rounds(r) {\n        r = r | 0;\n        R = r;\n      }\n\n      /**\n       * Populate the internal state of the module.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} s0 - state vector\n       * @param {number} s1 - state vector\n       * @param {number} s2 - state vector\n       * @param {number} s3 - state vector\n       */\n      function set_state(s0, s1, s2, s3) {\n        s0 = s0 | 0;\n        s1 = s1 | 0;\n        s2 = s2 | 0;\n        s3 = s3 | 0;\n\n        S0 = s0,\n          S1 = s1,\n          S2 = s2,\n          S3 = s3;\n      }\n\n      /**\n       * Populate the internal iv of the module.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} i0 - iv vector\n       * @param {number} i1 - iv vector\n       * @param {number} i2 - iv vector\n       * @param {number} i3 - iv vector\n       */\n      function set_iv(i0, i1, i2, i3) {\n        i0 = i0 | 0;\n        i1 = i1 | 0;\n        i2 = i2 | 0;\n        i3 = i3 | 0;\n\n        I0 = i0,\n          I1 = i1,\n          I2 = i2,\n          I3 = i3;\n      }\n\n      /**\n       * Set nonce for CTR-family modes.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} n0 - nonce vector\n       * @param {number} n1 - nonce vector\n       * @param {number} n2 - nonce vector\n       * @param {number} n3 - nonce vector\n       */\n      function set_nonce(n0, n1, n2, n3) {\n        n0 = n0 | 0;\n        n1 = n1 | 0;\n        n2 = n2 | 0;\n        n3 = n3 | 0;\n\n        N0 = n0,\n          N1 = n1,\n          N2 = n2,\n          N3 = n3;\n      }\n\n      /**\n       * Set counter mask for CTR-family modes.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} m0 - counter mask vector\n       * @param {number} m1 - counter mask vector\n       * @param {number} m2 - counter mask vector\n       * @param {number} m3 - counter mask vector\n       */\n      function set_mask(m0, m1, m2, m3) {\n        m0 = m0 | 0;\n        m1 = m1 | 0;\n        m2 = m2 | 0;\n        m3 = m3 | 0;\n\n        M0 = m0,\n          M1 = m1,\n          M2 = m2,\n          M3 = m3;\n      }\n\n      /**\n       * Set counter for CTR-family modes.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} c0 - counter vector\n       * @param {number} c1 - counter vector\n       * @param {number} c2 - counter vector\n       * @param {number} c3 - counter vector\n       */\n      function set_counter(c0, c1, c2, c3) {\n        c0 = c0 | 0;\n        c1 = c1 | 0;\n        c2 = c2 | 0;\n        c3 = c3 | 0;\n\n        N3 = (~M3 & N3) | M3 & c3,\n          N2 = (~M2 & N2) | M2 & c2,\n          N1 = (~M1 & N1) | M1 & c1,\n          N0 = (~M0 & N0) | M0 & c0;\n      }\n\n      /**\n       * Store the internal state vector into the heap.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} pos - offset where to put the data\n       * @return {number} The number of bytes have been written into the heap, always 16.\n       */\n      function get_state(pos) {\n        pos = pos | 0;\n\n        if (pos & 15) return -1;\n\n        DATA[pos | 0] = S0 >>> 24,\n          DATA[pos | 1] = S0 >>> 16 & 255,\n          DATA[pos | 2] = S0 >>> 8 & 255,\n          DATA[pos | 3] = S0 & 255,\n          DATA[pos | 4] = S1 >>> 24,\n          DATA[pos | 5] = S1 >>> 16 & 255,\n          DATA[pos | 6] = S1 >>> 8 & 255,\n          DATA[pos | 7] = S1 & 255,\n          DATA[pos | 8] = S2 >>> 24,\n          DATA[pos | 9] = S2 >>> 16 & 255,\n          DATA[pos | 10] = S2 >>> 8 & 255,\n          DATA[pos | 11] = S2 & 255,\n          DATA[pos | 12] = S3 >>> 24,\n          DATA[pos | 13] = S3 >>> 16 & 255,\n          DATA[pos | 14] = S3 >>> 8 & 255,\n          DATA[pos | 15] = S3 & 255;\n\n        return 16;\n      }\n\n      /**\n       * Store the internal iv vector into the heap.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} pos - offset where to put the data\n       * @return {number} The number of bytes have been written into the heap, always 16.\n       */\n      function get_iv(pos) {\n        pos = pos | 0;\n\n        if (pos & 15) return -1;\n\n        DATA[pos | 0] = I0 >>> 24,\n          DATA[pos | 1] = I0 >>> 16 & 255,\n          DATA[pos | 2] = I0 >>> 8 & 255,\n          DATA[pos | 3] = I0 & 255,\n          DATA[pos | 4] = I1 >>> 24,\n          DATA[pos | 5] = I1 >>> 16 & 255,\n          DATA[pos | 6] = I1 >>> 8 & 255,\n          DATA[pos | 7] = I1 & 255,\n          DATA[pos | 8] = I2 >>> 24,\n          DATA[pos | 9] = I2 >>> 16 & 255,\n          DATA[pos | 10] = I2 >>> 8 & 255,\n          DATA[pos | 11] = I2 & 255,\n          DATA[pos | 12] = I3 >>> 24,\n          DATA[pos | 13] = I3 >>> 16 & 255,\n          DATA[pos | 14] = I3 >>> 8 & 255,\n          DATA[pos | 15] = I3 & 255;\n\n        return 16;\n      }\n\n      /**\n       * GCM initialization.\n       * @instance\n       * @memberof AES_asm\n       */\n      // function gcm_init() {\n      //   _ecb_enc(0, 0, 0, 0);\n      //   H0 = S0,\n      //     H1 = S1,\n      //     H2 = S2,\n      //     H3 = S3;\n      // }\n\n      /**\n       * Perform ciphering operation on the supplied data.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} mode - block cipher mode (see {@link AES_asm} mode constants)\n       * @param {number} pos - offset of the data being processed\n       * @param {number} len - length of the data being processed\n       * @return {number} Actual amount of data have been processed.\n       */\n      function cipher(mode, pos, len) {\n        mode = mode | 0;\n        pos = pos | 0;\n        len = len | 0;\n\n        var ret = 0;\n\n        if (pos & 15) return -1;\n\n        while ((len | 0) >= 16) {\n          _cipher_modes[mode & 7](\n            DATA[pos | 0] << 24 | DATA[pos | 1] << 16 | DATA[pos | 2] << 8 | DATA[pos | 3],\n            DATA[pos | 4] << 24 | DATA[pos | 5] << 16 | DATA[pos | 6] << 8 | DATA[pos | 7],\n            DATA[pos | 8] << 24 | DATA[pos | 9] << 16 | DATA[pos | 10] << 8 | DATA[pos | 11],\n            DATA[pos | 12] << 24 | DATA[pos | 13] << 16 | DATA[pos | 14] << 8 | DATA[pos | 15]\n          );\n\n          DATA[pos | 0] = S0 >>> 24,\n            DATA[pos | 1] = S0 >>> 16 & 255,\n            DATA[pos | 2] = S0 >>> 8 & 255,\n            DATA[pos | 3] = S0 & 255,\n            DATA[pos | 4] = S1 >>> 24,\n            DATA[pos | 5] = S1 >>> 16 & 255,\n            DATA[pos | 6] = S1 >>> 8 & 255,\n            DATA[pos | 7] = S1 & 255,\n            DATA[pos | 8] = S2 >>> 24,\n            DATA[pos | 9] = S2 >>> 16 & 255,\n            DATA[pos | 10] = S2 >>> 8 & 255,\n            DATA[pos | 11] = S2 & 255,\n            DATA[pos | 12] = S3 >>> 24,\n            DATA[pos | 13] = S3 >>> 16 & 255,\n            DATA[pos | 14] = S3 >>> 8 & 255,\n            DATA[pos | 15] = S3 & 255;\n\n          ret = (ret + 16) | 0,\n            pos = (pos + 16) | 0,\n            len = (len - 16) | 0;\n        }\n\n        return ret | 0;\n      }\n\n      /**\n       * Calculates MAC of the supplied data.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} mode - block cipher mode (see {@link AES_asm} mode constants)\n       * @param {number} pos - offset of the data being processed\n       * @param {number} len - length of the data being processed\n       * @return {number} Actual amount of data have been processed.\n       */\n      function mac(mode, pos, len) {\n        mode = mode | 0;\n        pos = pos | 0;\n        len = len | 0;\n\n        var ret = 0;\n\n        if (pos & 15) return -1;\n\n        while ((len | 0) >= 16) {\n          _mac_modes[mode & 1](\n            DATA[pos | 0] << 24 | DATA[pos | 1] << 16 | DATA[pos | 2] << 8 | DATA[pos | 3],\n            DATA[pos | 4] << 24 | DATA[pos | 5] << 16 | DATA[pos | 6] << 8 | DATA[pos | 7],\n            DATA[pos | 8] << 24 | DATA[pos | 9] << 16 | DATA[pos | 10] << 8 | DATA[pos | 11],\n            DATA[pos | 12] << 24 | DATA[pos | 13] << 16 | DATA[pos | 14] << 8 | DATA[pos | 15]\n          );\n\n          ret = (ret + 16) | 0,\n            pos = (pos + 16) | 0,\n            len = (len - 16) | 0;\n        }\n\n        return ret | 0;\n      }\n\n      /**\n       * AES cipher modes table (virual methods)\n       */\n      var _cipher_modes = [_cbc_enc, _cbc_enc, _cbc_enc, _cbc_dec, _cbc_dec, _cbc_dec, _cbc_dec, _cbc_dec];\n\n      /**\n       * AES MAC modes table (virual methods)\n       */\n      var _mac_modes = [_cbc_enc, _cbc_enc];\n\n      /**\n       * Asm.js module exports\n       */\n      return {\n        set_rounds: set_rounds,\n        set_state: set_state,\n        set_iv: set_iv,\n        set_nonce: set_nonce,\n        set_mask: set_mask,\n        set_counter: set_counter,\n        get_state: get_state,\n        get_iv: get_iv,\n        // gcm_init: gcm_init,\n        cipher: cipher,\n        mac: mac,\n      };\n    }(stdlib, foreign, buffer);\n\n    asm.set_key = set_key;\n\n    return asm;\n  };\n\n  /**\n   * AES enciphering mode constants\n   * @enum {number}\n   * @const\n   */\n  wrapper.ENC = {\n    //ECB: 0,\n    CBC: 2,\n    //CFB: 4,\n    //OFB: 6,\n    // CTR: 7,\n  },\n\n    /**\n     * AES deciphering mode constants\n     * @enum {number}\n     * @const\n     */\n    wrapper.DEC = {\n      //ECB: 1,\n      CBC: 3,\n      //CFB: 5,\n      //OFB: 6,\n      // CTR: 7,\n    },\n\n    /**\n     * AES MAC mode constants\n     * @enum {number}\n     * @const\n     */\n    wrapper.MAC = {\n      CBC: 0,\n      //GCM: 1,\n    };\n\n  /**\n   * Heap data offset\n   * @type {number}\n   * @const\n   */\n  wrapper.HEAP_DATA = 0x4000;\n\n  return wrapper;\n}();\n", "export function _heap_init(heap?: Uint8Array, heapSize?: number): Uint8Array {\n    const size = heap ? heap.byteLength : heapSize || 65536\n\n    if (size & 0xfff || size <= 0)\n        throw new Error('heap size must be a positive integer and a multiple of 4096')\n\n    heap = heap || new Uint8Array(new ArrayBuffer(size))\n\n    return heap\n}\n\nexport function _heap_write(\n    heap: Uint8Array,\n    hpos: number,\n    data: Uint8Array,\n    dpos: number,\n    dlen: number\n): number {\n    const hlen = heap.length - hpos\n    const wlen = hlen < dlen ? hlen : dlen\n\n    heap.set(data.subarray(dpos, dpos + wlen), hpos)\n\n    return wlen\n}\n\nexport function is_buffer(a: ArrayBuffer): boolean {\n    return a instanceof ArrayBuffer\n}\n\nexport function is_bytes(a: Uint8Array): boolean {\n    return a instanceof Uint8Array\n}\n\nexport function joinBytes(...arg: Uint8Array[]): Uint8Array {\n    const totalLength = arg.reduce((sum, curr) => sum + curr.length, 0)\n    const ret = new Uint8Array(totalLength)\n\n    let cursor = 0\n    for (let i = 0; i < arg.length; i++) {\n        ret.set(arg[i], cursor)\n        cursor += arg[i].length\n    }\n    return ret\n}\n", "import {AES_asm} from './aes.asm'\nimport {_heap_init, _heap_write, is_bytes} from './utils'\n\nexport class AES {\n    public readonly heap: Uint8Array\n    public readonly asm: any\n    public readonly mode: string\n    public readonly padding: boolean\n    public pos = 0\n    public len = 0\n\n    constructor(key: Uint8Array, iv: Uint8Array | undefined, padding = true, mode: 'CBC') {\n        this.mode = mode\n\n        // The AES \"worker\"\n        this.heap = _heap_init().subarray(AES_asm.HEAP_DATA)\n        this.asm = new AES_asm(null, this.heap.buffer)\n\n        // The AES object state\n        this.pos = 0\n        this.len = 0\n\n        // Key\n        const keylen = key.length\n        if (keylen !== 16 && keylen !== 24 && keylen !== 32) throw new TypeError('illegal key size')\n\n        const keyview = new DataView(key.buffer, key.byteOffset, key.byteLength)\n        this.asm.set_key(\n            keylen >> 2,\n            keyview.getUint32(0),\n            keyview.getUint32(4),\n            keyview.getUint32(8),\n            keyview.getUint32(12),\n            keylen > 16 ? keyview.getUint32(16) : 0,\n            keylen > 16 ? keyview.getUint32(20) : 0,\n            keylen > 24 ? keyview.getUint32(24) : 0,\n            keylen > 24 ? keyview.getUint32(28) : 0\n        )\n\n        // IV\n        if (iv !== undefined) {\n            if (iv.length !== 16) throw new TypeError('illegal iv size')\n\n            const ivview = new DataView(iv.buffer, iv.byteOffset, iv.byteLength)\n\n            this.asm.set_iv(\n                ivview.getUint32(0),\n                ivview.getUint32(4),\n                ivview.getUint32(8),\n                ivview.getUint32(12)\n            )\n        } else {\n            this.asm.set_iv(0, 0, 0, 0)\n        }\n\n        this.padding = padding\n    }\n\n    AES_Encrypt_process(data: Uint8Array): Uint8Array {\n        if (!is_bytes(data)) throw new TypeError(\"data isn't of expected type\")\n\n        const asm = this.asm\n        const heap = this.heap\n        const amode = AES_asm.ENC[this.mode]\n        const hpos = AES_asm.HEAP_DATA\n        let pos = this.pos\n        let len = this.len\n        let dpos = 0\n        let dlen = data.length || 0\n        let rpos = 0\n        const rlen = (len + dlen) & -16\n        let wlen = 0\n\n        const result = new Uint8Array(rlen)\n\n        while (dlen > 0) {\n            wlen = _heap_write(heap, pos + len, data, dpos, dlen)\n            len += wlen\n            dpos += wlen\n            dlen -= wlen\n\n            wlen = asm.cipher(amode, hpos + pos, len)\n\n            if (wlen) result.set(heap.subarray(pos, pos + wlen), rpos)\n            rpos += wlen\n\n            if (wlen < len) {\n                pos += wlen\n                len -= wlen\n            } else {\n                pos = 0\n                len = 0\n            }\n        }\n\n        this.pos = pos\n        this.len = len\n\n        return result\n    }\n\n    AES_Encrypt_finish(): Uint8Array {\n        const asm = this.asm\n        const heap = this.heap\n        const amode = AES_asm.ENC[this.mode]\n        const hpos = AES_asm.HEAP_DATA\n        const pos = this.pos\n        let len = this.len\n        const plen = 16 - (len % 16)\n        let rlen = len\n\n        // if (this.hasOwnProperty('padding')) {\n        if (this.padding) {\n            for (let p = 0; p < plen; ++p) {\n                heap[pos + len + p] = plen\n            }\n            len += plen\n            rlen = len\n        } else if (len % 16) {\n            throw new TypeError('data length must be a multiple of the block size')\n        }\n        // } else {\n        //     len += plen\n        // }\n\n        const result = new Uint8Array(rlen)\n\n        if (len) asm.cipher(amode, hpos + pos, len)\n\n        if (rlen) result.set(heap.subarray(pos, pos + rlen))\n\n        this.pos = 0\n        this.len = 0\n\n        return result\n    }\n\n    AES_Decrypt_process(data: Uint8Array): Uint8Array {\n        if (!is_bytes(data)) throw new TypeError(\"data isn't of expected type\")\n\n        const asm = this.asm\n        const heap = this.heap\n        const amode = AES_asm.DEC[this.mode]\n        const hpos = AES_asm.HEAP_DATA\n        let pos = this.pos\n        let len = this.len\n        let dpos = 0\n        let dlen = data.length || 0\n        let rpos = 0\n        let rlen = (len + dlen) & -16\n        let plen = 0\n        let wlen = 0\n\n        if (this.padding) {\n            plen = len + dlen - rlen || 16\n            rlen -= plen\n        }\n\n        const result = new Uint8Array(rlen)\n\n        while (dlen > 0) {\n            wlen = _heap_write(heap, pos + len, data, dpos, dlen)\n            len += wlen\n            dpos += wlen\n            dlen -= wlen\n\n            wlen = asm.cipher(amode, hpos + pos, len - (!dlen ? plen : 0))\n\n            if (wlen) result.set(heap.subarray(pos, pos + wlen), rpos)\n            rpos += wlen\n\n            if (wlen < len) {\n                pos += wlen\n                len -= wlen\n            } else {\n                pos = 0\n                len = 0\n            }\n        }\n\n        this.pos = pos\n        this.len = len\n\n        return result\n    }\n\n    AES_Decrypt_finish(): Uint8Array {\n        const asm = this.asm\n        const heap = this.heap\n        const amode = AES_asm.DEC[this.mode]\n        const hpos = AES_asm.HEAP_DATA\n        const pos = this.pos\n        const len = this.len\n        let rlen = len\n\n        if (len > 0) {\n            if (len % 16) {\n                // if (this.hasOwnProperty('padding')) {\n                throw new Error('data length must be a multiple of the block size')\n                // } else {\n                // len += 16 - (len % 16)\n                // }\n            }\n\n            asm.cipher(amode, hpos + pos, len)\n\n            if (/*this.hasOwnProperty('padding')  &&*/ this.padding) {\n                const pad = heap[pos + rlen - 1]\n                if (pad < 1 || pad > 16 || pad > rlen) throw new Error('bad padding')\n\n                let pcheck = 0\n                for (let i = pad; i > 1; i--) pcheck |= pad ^ heap[pos + rlen - i]\n                if (pcheck) throw new Error('bad padding')\n\n                rlen -= pad\n            }\n        }\n\n        const result = new Uint8Array(rlen)\n\n        if (rlen > 0) {\n            result.set(heap.subarray(pos, pos + rlen))\n        }\n\n        this.pos = 0\n        this.len = 0\n\n        return result\n    }\n}\n", "import {AES} from './aes'\nimport {joinBytes} from './utils'\n\nexport class AES_CBC {\n    private aes: AES\n\n    static encrypt(data: Uint8Array, key: Uint8Array, padding = true, iv?: Uint8Array): Uint8Array {\n        return new AES_CBC(key, iv, padding).encrypt(data)\n    }\n\n    static decrypt(data: Uint8Array, key: Uint8Array, padding = true, iv?: Uint8Array): Uint8Array {\n        return new AES_CBC(key, iv, padding).decrypt(data)\n    }\n\n    constructor(key: Uint8Array, iv?: Uint8Array, padding = true, aes?: AES) {\n        this.aes = aes ? aes : new AES(key, iv, padding, 'CBC')\n    }\n\n    encrypt(data: Uint8Array): Uint8Array {\n        const r1 = this.aes.AES_Encrypt_process(data)\n        const r2 = this.aes.AES_Encrypt_finish()\n\n        return joinBytes(r1, r2)\n    }\n\n    decrypt(data: Uint8Array): Uint8Array {\n        const r1 = this.aes.AES_Decrypt_process(data)\n        const r2 = this.aes.AES_Decrypt_finish()\n\n        return joinBytes(r1, r2)\n    }\n}\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "import {Bytes, Name, <PERSON><PERSON><PERSON>, <PERSON>ruct, TimePointSec, UInt32, UInt64} from '@wharfkit/session'\n\*************('sealed_message')\nexport class SealedMessage extends Struct {\n    @Struct.field('public_key') from!: <PERSON><PERSON>ey\n    @Struct.field('uint64') nonce!: UInt64\n    @Struct.field('bytes') ciphertext!: Bytes\n    @Struct.field('uint32') checksum!: UInt32\n}\n\*************('link_create')\nexport class LinkCreate extends Struct {\n    @Struct.field('name') session_name!: Name\n    @Struct.field('public_key') request_key!: Public<PERSON>ey\n    @Struct.field('string', {extension: true}) user_agent?: string\n}\n\*************('link_info')\nexport class LinkInfo extends Struct {\n    @Struct.field('time_point_sec') expiration!: TimePointSec\n}\n", "import {Bytes, Name, <PERSON>Key, Struct, TimePointSec, UInt32, UInt64} from '@wharfkit/session'\n\*************('buoy_message')\nexport class BuoyMessage extends Struct {\n    @Struct.field('public_key') from!: <PERSON><PERSON>ey\n    @Struct.field('uint64') nonce!: UInt64\n    @Struct.field('bytes') ciphertext!: Bytes\n    @Struct.field('uint32') checksum!: UInt32\n}\n\*************('buoy_session')\nexport class BuoySession extends Struct {\n    @Struct.field('name') session_name!: Name\n    @Struct.field('public_key') request_key!: PublicKey\n    @Struct.field('string', {extension: true}) user_agent?: string\n}\n\*************('buoy_info')\nexport class BuoyInfo extends Struct {\n    @Struct.field('time_point_sec') expiration!: TimePointSec\n}\n", "import {receive} from '@greymass/buoy'\nimport {CallbackPayload} from '@wharfkit/session'\n\nexport async function waitForCallback(callbackArgs, buoyWs, t): Promise<CallbackPayload> {\n    // Use the buoy-client to create a promise and wait for a response to the identity request\n    const callbackResponse = await receive({...callbackArgs, WebSocket: buoyWs || WebSocket})\n\n    if (!callbackResponse) {\n        // If the promise was rejected, throw an error\n        throw new Error(callbackResponse.rejected)\n    }\n\n    // If the promise was rejected, throw an error\n    if (typeof callbackResponse.rejected === 'string') {\n        throw new Error(callbackResponse.rejected)\n    }\n\n    // Process the identity request callback payload\n    const payload = JSON.parse(callbackResponse) as CallbackPayload\n\n    if (payload.sa === undefined || payload.sp === undefined || payload.cid === undefined) {\n        throw new Error(t('error.cancelled', {default: 'The request was cancelled from Anchor.'}))\n    }\n\n    return payload\n}\n", "/**\n * Return PascalCase version of snake_case string.\n * @internal\n */\nexport function snakeToPascal(name: string): string {\n    return name\n        .split('_')\n        .map((v) => (v[0] ? v[0].toUpperCase() : '') + v.slice(1))\n        .join('')\n}\n\n/**\n * Return camelCase version of snake_case string.\n * @internal\n */\nexport function snakeToCamel(name: string): string {\n    const pascal = snakeToPascal(name)\n\n    return (pascal[0] ? pascal[0].toLowerCase() : '') + pascal.slice(1)\n}\n\n/**\n * Print a warning message to console.\n * @internal\n **/\nexport function logWarn(...args: any[]) {\n    // eslint-disable-next-line no-console\n    console.warn('[anchor-link]', ...args)\n}\n\n/**\n * Generate a UUID.\n *  @internal\n * */\n\nexport function uuid(): string {\n    let uuid = '',\n        ii\n    const chars = '0123456789abcdef'\n    for (ii = 0; ii < 36; ii += 1) {\n        switch (ii) {\n            case 8:\n            case 13:\n            case 18:\n            case 23:\n                uuid += '-'\n                break\n            case 14:\n                uuid += '4'\n                break\n            case 19:\n                uuid += chars[(Math.random() * 4) | (0 + 8)]\n                break\n            default:\n                uuid += chars[(Math.random() * 16) | 0]\n        }\n    }\n    return uuid\n}\n\n/** Generate a return url that Anchor will redirect back to w/o reload. */\nexport function generateReturnUrl(): string | undefined {\n    // Return undefined for iOS React Native apps to prevent redirect to Safari\n    if (isAppleHandheld() && isReactNativeApp()) {\n        return undefined\n    }\n\n    if (isChromeiOS()) {\n        // google chrome on iOS will always open new tab so we just ask it to open again as a workaround\n        return 'googlechrome://'\n    }\n    if (isFirefoxiOS()) {\n        // same for firefox\n        return 'firefox:://'\n    }\n    if (isAppleHandheld() && isBrave()) {\n        // and brave ios\n        return 'brave://'\n    }\n    if (isAppleHandheld()) {\n        // return url with unique fragment required for iOS safari to trigger the return url\n        const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'\n        let rv = window.location.href.split('#')[0] + '#'\n        for (let i = 0; i < 8; i++) {\n            rv += alphabet.charAt(Math.floor(Math.random() * alphabet.length))\n        }\n        return rv\n    }\n\n    if (isAndroid() && isFirefox()) {\n        return 'android-app://org.mozilla.firefox'\n    }\n\n    if (isAndroid() && isEdge()) {\n        return 'android-app://com.microsoft.emmx'\n    }\n\n    if (isAndroid() && isOpera()) {\n        return 'android-app://com.opera.browser'\n    }\n\n    if (isAndroid() && isBrave()) {\n        return 'android-app://com.brave.browser'\n    }\n\n    if (isAndroid() && isAndroidWebView()) {\n        return 'android-app://webview'\n    }\n\n    if (isAndroid() && isChromeMobile()) {\n        return 'android-app://com.android.chrome'\n    }\n\n    return window.location.href\n}\n\nexport function isAppleHandheld() {\n    return /iP(ad|od|hone)/i.test(navigator.userAgent)\n}\n\nexport function isChromeiOS() {\n    return /CriOS/.test(navigator.userAgent)\n}\n\nexport function isChromeMobile() {\n    return /Chrome\\/[.0-9]* Mobile/i.test(navigator.userAgent)\n}\n\nexport function isFirefox() {\n    return /Firefox/i.test(navigator.userAgent)\n}\n\nexport function isFirefoxiOS() {\n    return /FxiOS/.test(navigator.userAgent)\n}\n\nexport function isOpera() {\n    return /OPR/.test(navigator.userAgent) || /Opera/.test(navigator.userAgent)\n}\n\nexport function isEdge() {\n    return /Edg/.test(navigator.userAgent)\n}\n\nexport function isBrave() {\n    return navigator['brave'] && typeof navigator['brave'].isBrave === 'function'\n}\n\nexport function isAndroid() {\n    return /Android/.test(navigator.userAgent)\n}\n\nexport function isReactNativeApp() {\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    return !!window.ReactNativeWebView\n}\n\nexport function isAndroidWebView() {\n    return (\n        /wv/.test(navigator.userAgent) ||\n        (/Android/.test(navigator.userAgent) && isReactNativeApp())\n    )\n}\n\nexport function isKnownMobile() {\n    return (\n        isAppleHandheld() ||\n        isChromeiOS() ||\n        isChromeMobile() ||\n        isFirefoxiOS() ||\n        isAndroid() ||\n        isAndroidWebView()\n    )\n}\n", "import {ReceiveOptions} from '@greymass/buoy'\nimport {AES_CBC} from '@greymass/miniaes'\nimport {\n    Bytes,\n    CallbackPayload,\n    CallbackType,\n    ChainDefinition,\n    ChainId,\n    Checksum256,\n    Checksum512,\n    <PERSON>ginContext,\n    PrivateKey,\n    Public<PERSON>ey,\n    Serializer,\n    Signature,\n    SigningRequest,\n    UInt64,\n} from '@wharfkit/session'\n\nimport {generateReturnUrl, uuid} from './utils'\n\nimport {BuoySession} from './buoy-types'\n\nimport {SealedMessage} from './anchor-types'\n\nimport WebSocket from 'isomorphic-ws'\n\nexport interface WalletPluginOptions {\n    buoyUrl?: string\n    buoyWs?: WebSocket\n}\n\nexport interface IdentityRequestResponse {\n    callback\n    request: SigningRequest // Request for multi-device login\n    sameDeviceRequest: SigningRequest // Request for same-device login\n    requestKey: PublicKey\n    privateKey: PrivateKey\n}\n\n/**\n * createIdentityRequest\n *\n * @param context LoginContext\n * @returns\n */\nexport async function createIdentityRequest(\n    context: LoginContext,\n    buoyUrl: string\n): Promise<IdentityRequestResponse> {\n    // Create a new private key and public key to act as the request key\n    const privateKey = PrivateKey.generate('K1')\n    const requestKey = privateKey.toPublic()\n\n    // Create a new BuoySession struct to be used as the info field\n    const createInfo = BuoySession.from({\n        session_name: context.appName,\n        request_key: requestKey,\n        user_agent: getUserAgent(),\n    })\n\n    // Determine based on the options whether this is a multichain request\n    const isMultiChain = !(context.chain || context.chains.length === 1)\n\n    // Create the callback\n    const callbackChannel = prepareCallbackChannel(buoyUrl)\n\n    // Determine the chain id(s) to use\n    const chainId: ChainId | null = isMultiChain\n        ? null\n        : context.chain\n        ? ChainId.from(context.chain.id.array)\n        : null\n\n    const chainIds: ChainId[] = isMultiChain\n        ? context.chains.map((c) => ChainId.from(c.id.array))\n        : []\n\n    // Create the request\n    const request = SigningRequest.identity(\n        {\n            callback: prepareCallback(callbackChannel),\n            scope: String(context.appName),\n            chainId,\n            chainIds,\n            info: {\n                link: createInfo,\n                scope: String(context.appName),\n            },\n        },\n        context.esrOptions\n    )\n\n    const sameDeviceRequest = request.clone()\n    if (typeof window !== 'undefined') {\n        const returnUrl = generateReturnUrl()\n        sameDeviceRequest.setInfoKey('same_device', true)\n        if (returnUrl !== undefined) {\n            sameDeviceRequest.setInfoKey('return_path', returnUrl)\n        }\n    }\n\n    // Return the request and the callback data\n    return {\n        callback: callbackChannel,\n        request,\n        sameDeviceRequest,\n        requestKey,\n        privateKey,\n    }\n}\n\n/**\n * prepareTransactionRequest\n *\n * @param resolved ResolvedSigningRequest\n * @returns\n */\n\nexport function setTransactionCallback(request: SigningRequest, buoyUrl) {\n    const callback = prepareCallbackChannel(buoyUrl)\n\n    request.setCallback(`${callback.service}/${callback.channel}`, true)\n\n    return callback\n}\n\nexport function getUserAgent(): string {\n    const version = '__ver'\n    let agent = `@wharfkit/protocol-esr ${version}`\n    if (typeof navigator !== 'undefined') {\n        agent += ' ' + navigator.userAgent\n    }\n    return agent\n}\n\nexport function prepareCallback(callbackChannel: ReceiveOptions): CallbackType {\n    const {service, channel} = callbackChannel\n    return {\n        url: `${service}/${channel}`,\n        background: true,\n    }\n}\n\nfunction prepareCallbackChannel(buoyUrl): ReceiveOptions {\n    return {\n        service: buoyUrl,\n        channel: uuid(),\n    }\n}\n\nexport function sealMessage(\n    message: string,\n    privateKey: PrivateKey,\n    publicKey: PublicKey,\n    nonce?: UInt64\n): SealedMessage {\n    const secret = privateKey.sharedSecret(publicKey)\n    if (!nonce) {\n        nonce = UInt64.random()\n    }\n    const key = Checksum512.hash(Serializer.encode({object: nonce}).appending(secret.array))\n    const cbc = new AES_CBC(key.array.slice(0, 32), key.array.slice(32, 48))\n    const ciphertext = Bytes.from(cbc.encrypt(Bytes.from(message, 'utf8').array))\n    const checksumView = new DataView(Checksum256.hash(key.array).array.buffer)\n    const checksum = checksumView.getUint32(0, true)\n    return SealedMessage.from({\n        from: privateKey.toPublic(),\n        nonce,\n        ciphertext,\n        checksum,\n    })\n}\n\nexport async function verifyLoginCallbackResponse(callbackResponse, context: LoginContext) {\n    if (!callbackResponse.sig || callbackResponse.sig.length === 0) {\n        throw new Error('Invalid response, must have at least one signature')\n    }\n\n    let chain: ChainDefinition\n    if (!context.chain && context.chains.length > 1) {\n        if (!callbackResponse.cid) {\n            throw new Error('Multi chain response payload must specify resolved chain id (cid)')\n        }\n    } else {\n        chain = context.chain || context.chains[0]\n\n        if (callbackResponse.cid && String(chain.id) !== callbackResponse.cid) {\n            throw new Error('Got response for wrong chain id')\n        }\n    }\n}\n\nexport function extractSignaturesFromCallback(payload: CallbackPayload): Signature[] {\n    const signatures: string[] = []\n\n    let index = 0\n    let sig: string | undefined = payload.sig\n\n    while (sig) {\n        signatures.push(String(sig))\n\n        sig = payload[`sig${index}`]\n\n        index++\n    }\n\n    // Deduplicate and make signatures\n    return [...new Set(signatures)].map((s) => Signature.from(s))\n}\n\nexport function isCallback(object: any): object is CallbackPayload {\n    return 'tx' in object\n}\n", "import en from './en.json'\nimport ko from './ko.json'\nimport zh_hans from './zh-hans.json'\nimport zh_hant from './zh-hant.json'\n\nexport default {\n    en,\n    ko,\n    'zh-Hans': zh_hans,\n    'zh-Hant': zh_hant,\n}\n", "import {send} from '@greymass/buoy'\nimport {\n    AbstractWalletPlugin,\n    CallbackPayload,\n    Cancelable,\n    Checksum256,\n    LoginContext,\n    Logo,\n    PermissionLevel,\n    PrivateKey,\n    PromptElement,\n    PromptResponse,\n    PublicKey,\n    ResolvedSigningRequest,\n    Serializer,\n    TransactContext,\n    WalletPluginConfig,\n    WalletPluginLoginResponse,\n    WalletPluginMetadata,\n    WalletPluginSignResponse,\n} from '@wharfkit/session'\nimport {\n    createIdentityRequest,\n    extractSignaturesFromCallback,\n    generateReturnUrl,\n    isAppleHandheld,\n    isCallback,\n    isKnownMobile,\n    LinkInfo,\n    sealMessage,\n    setTransactionCallback,\n    verifyLoginCallbackResponse,\n    waitForCallback,\n} from '@wharfkit/protocol-esr'\n\nimport WebSocket from 'isomorphic-ws'\n\nimport defaultTranslations from './translations'\n\ninterface WalletPluginOptions {\n    buoyUrl?: string\n    buoyWs?: WebSocket\n}\nexport class WalletPluginAnchor extends AbstractWalletPlugin {\n    chain: Checksum256 | undefined\n    auth: PermissionLevel | undefined\n    requestKey: PublicKey | undefined\n    privateKey: PrivateKey | undefined\n    signerKey: PublicKey | undefined\n    channelUrl: string | undefined\n    channelName: string | undefined\n    buoyUrl: string\n    buoyWs: WebSocket | undefined\n\n    /**\n     * The unique identifier for the wallet plugin.\n     */\n    id = 'anchor'\n\n    /**\n     * The translations for this plugin\n     */\n    translations = defaultTranslations\n\n    constructor(options?: WalletPluginOptions) {\n        super()\n\n        this.buoyUrl = options?.buoyUrl || 'https://cb.anchor.link'\n        this.buoyWs = options?.buoyWs\n    }\n\n    /**\n     * The logic configuration for the wallet plugin.\n     */\n    readonly config: WalletPluginConfig = {\n        // Should the user interface display a chain selector?\n        requiresChainSelect: false,\n        // Should the user interface display a permission selector?\n        requiresPermissionSelect: false,\n    }\n    /**\n     * The metadata for the wallet plugin to be displayed in the user interface.\n     */\n    readonly metadata: WalletPluginMetadata = WalletPluginMetadata.from({\n        name: 'Anchor',\n        description: '',\n        logo: Logo.from({\n            dark: 'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyB3aWR0aD0iMTYwIiBoZWlnaHQ9IjE2MCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGcgdHJhbnNmb3JtPSJtYXRyaXgoMS40NCwgMCwgMCwgMS40NCwgLTguNTAxOTI1LCAtNTcuMDc0NTcpIiBzdHlsZT0iIj4KICAgIDx0aXRsZT5XaGl0ZTwvdGl0bGU+CiAgICA8Y2lyY2xlIGN4PSI5NC43OTMiIGN5PSIxMjguNTI0IiByPSI4MCIgZmlsbD0iI0ZCRkRGRiIvPgogICAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0gOTQuNzk5IDc4LjUyNCBDIDk3LjA5OCA3OC41MjQgOTkuMTk1IDc5LjgzNyAxMDAuMTk4IDgxLjkwNiBMIDEyNC4yMDQgMTMxLjQwNiBMIDEyNC43NDYgMTMyLjUyNCBMIDExMS40MDkgMTMyLjUyNCBMIDEwNy41MyAxMjQuNTI0IEwgODIuMDY5IDEyNC41MjQgTCA3OC4xODkgMTMyLjUyNCBMIDY0Ljg1MyAxMzIuNTI0IEwgNjUuMzk1IDEzMS40MDYgTCA4OS40MDEgODEuOTA2IEMgOTAuNDA0IDc5LjgzNyA5Mi41MDEgNzguNTI0IDk0Ljc5OSA3OC41MjQgWiBNIDg2LjkxOSAxMTQuNTI0IEwgMTAyLjY4IDExNC41MjQgTCA5NC43OTkgOTguMjc0IEwgODYuOTE5IDExNC41MjQgWiBNIDExMi43OTMgMTQ5LjUyNCBMIDEyNC43OTggMTQ5LjUyNCBDIDEyNC40MzcgMTY1LjY3NiAxMTEuMDY3IDE3OC41MjQgOTQuNzk5IDE3OC41MjQgQyA3OC41MzIgMTc4LjUyNCA2NS4xNjIgMTY1LjY3NiA2NC44MDEgMTQ5LjUyNCBMIDc2LjgwNiAxNDkuNTI0IEMgNzcuMDg3IDE1Ni44NzggODEuOTc0IDE2My4xNTUgODguNzkzIDE2NS41MiBMIDg4Ljc5MyAxNDEuNTI0IEMgODguNzkzIDEzOC4yMSA5MS40OCAxMzUuNTI0IDk0Ljc5MyAxMzUuNTI0IEMgOTguMTA3IDEzNS41MjQgMTAwLjc5MyAxMzguMjEgMTAwLjc5MyAxNDEuNTI0IEwgMTAwLjc5MyAxNjUuNTI0IEMgMTA3LjYyIDE2My4xNjIgMTEyLjUxMSAxNTYuODgzIDExMi43OTMgMTQ5LjUyNCBaIiBmaWxsPSIjMzY1MEEyIi8+CiAgPC9nPgo8L3N2Zz4=',\n            light: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYwIiBoZWlnaHQ9IjE2MCIgdmlld0JveD0iMCAwIDE2MCAxNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjgwIiBjeT0iODAiIHI9IjgwIiBmaWxsPSIjMzY1MEEyIi8+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNODAuMDA2MyAzMEM4Mi4zMDUxIDMwIDg0LjQwMTkgMzEuMzEzNCA4NS40MDUgMzMuMzgxOEwxMDkuNDExIDgyLjg4MjJMMTA5Ljk1MyA4NEg5Ni42MTYzTDkyLjczNjYgNzZINjcuMjc1OUw2My4zOTYxIDg0SDUwLjA1OTRMNTAuNjAxNyA4Mi44ODE4TDc0LjYwNzcgMzMuMzgxOEM3NS42MTA4IDMxLjMxMzQgNzcuNzA3NSAzMCA4MC4wMDYzIDMwWk03Mi4xMjU2IDY2SDg3Ljg4N0w4MC4wMDYzIDQ5Ljc1MDFMNzIuMTI1NiA2NlpNOTcuOTk5NSAxMDFIMTEwLjAwNUMxMDkuNjQ0IDExNy4xNTIgOTYuMjczOCAxMzAgODAuMDA2MyAxMzBDNjMuNzM4OCAxMzAgNTAuMzY4NiAxMTcuMTUyIDUwLjAwNzggMTAxSDYyLjAxMzFDNjIuMjk0MSAxMDguMzU0IDY3LjE4MDQgMTE0LjYzMSA3NC4wMDAzIDExNi45OTZWOTNDNzQuMDAwMyA4OS42ODYzIDc2LjY4NjYgODcgODAuMDAwMyA4N0M4My4zMTQgODcgODYuMDAwMyA4OS42ODYzIDg2LjAwMDMgOTNWMTE3QzkyLjgyNjUgMTE0LjYzOCA5Ny43MTgzIDEwOC4zNTkgOTcuOTk5NSAxMDFaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K',\n        }),\n        homepage: 'https://greymass.com/anchor',\n        download: 'https://greymass.com/anchor/download',\n    })\n    /**\n     * Performs the wallet logic required to login and return the chain and permission level to use.\n     *\n     * @param options WalletPluginLoginOptions\n     * @returns Promise<WalletPluginLoginResponse>\n     */\n    login(context: LoginContext): Promise<WalletPluginLoginResponse> {\n        return new Promise((resolve, reject) => {\n            this.handleLogin(context)\n                .then((response) => {\n                    resolve(response)\n                })\n                .catch((error) => {\n                    reject(error)\n                })\n        })\n    }\n\n    async handleLogin(context: LoginContext): Promise<WalletPluginLoginResponse> {\n        if (!context.ui) {\n            throw new Error('No UI available')\n        }\n\n        // Retrieve translation helper from the UI, passing the app ID\n        const t = context.ui.getTranslate(this.id)\n\n        // Create the identity request to be presented to the user\n        const {callback, request, sameDeviceRequest, requestKey, privateKey} =\n            await createIdentityRequest(context, this.buoyUrl)\n\n        // Elements for the login prompt\n        const elements: PromptElement[] = [\n            {\n                type: 'link',\n                label: t('login.link', {default: 'Launch Anchor'}),\n                data: {\n                    href: sameDeviceRequest.encode(true, false, 'esr:'),\n                    label: t('login.link', {default: 'Launch Anchor'}),\n                    variant: 'primary',\n                },\n            },\n        ]\n\n        // If we know this is NOT a mobile device, show the QR code\n        if (!isKnownMobile()) {\n            elements.unshift({\n                type: 'qr',\n                data: request.encode(true, false, 'esr:'),\n            })\n        }\n\n        // Automatically try to open the link\n        window.location.href = sameDeviceRequest.encode(true, false, 'esr:')\n\n        // Tell Wharf we need to prompt the user with a QR code and a button\n        const promptResponse = context.ui?.prompt({\n            title: t('login.title', {default: 'Connect with Anchor'}),\n            body: t('login.body', {\n                default:\n                    'Scan with Anchor on your mobile device or click the button below to open on this device.',\n            }),\n            elements,\n        })\n\n        promptResponse.catch(() => {\n            // eslint-disable-next-line no-console\n            console.info('Modal closed')\n        })\n\n        // Await a promise race to wait for either the wallet response or the cancel\n        const callbackResponse: CallbackPayload = await waitForCallback(callback, this.buoyWs, t)\n        verifyLoginCallbackResponse(callbackResponse, context)\n\n        if (!callbackResponse.cid || !callbackResponse.sa || !callbackResponse.sp) {\n            throw new Error('Invalid callback response')\n        }\n\n        if (callbackResponse.link_ch && callbackResponse.link_key && callbackResponse.link_name) {\n            this.data.requestKey = requestKey\n            this.data.privateKey = privateKey\n            this.data.signerKey =\n                callbackResponse.link_key && PublicKey.from(callbackResponse.link_key)\n            this.data.channelUrl = callbackResponse.link_ch\n            this.data.channelName = callbackResponse.link_name\n\n            try {\n                if (callbackResponse.link_meta) {\n                    const metadata = JSON.parse(callbackResponse.link_meta)\n                    this.data.sameDevice = metadata.sameDevice\n                    this.data.launchUrl = metadata.launchUrl\n                    this.data.triggerUrl = metadata.triggerUrl\n                }\n            } catch (e) {\n                // console.log('Error processing link_meta', e)\n            }\n        }\n\n        const resolvedResponse = await ResolvedSigningRequest.fromPayload(\n            callbackResponse,\n            context.esrOptions\n        )\n\n        const identityProof = resolvedResponse.getIdentityProof(callbackResponse.sig)\n\n        return {\n            chain: Checksum256.from(callbackResponse.cid),\n            permissionLevel: PermissionLevel.from({\n                actor: callbackResponse.sa,\n                permission: callbackResponse.sp,\n            }),\n            identityProof,\n        }\n    }\n\n    /**\n     * Performs the wallet logic required to sign a transaction and return the signature.\n     *\n     * @param chain ChainDefinition\n     * @param resolved ResolvedSigningRequest\n     * @returns Promise<Signature>\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    sign(\n        resolved: ResolvedSigningRequest,\n        context: TransactContext\n    ): Promise<WalletPluginSignResponse> {\n        return this.handleSigningRequest(resolved, context)\n    }\n\n    private async handleSigningRequest(\n        resolved: ResolvedSigningRequest,\n        context: TransactContext\n    ): Promise<WalletPluginSignResponse> {\n        if (!context.ui) {\n            throw new Error('No UI available')\n        }\n\n        // Retrieve translation helper from the UI, passing the app ID\n        const t = context.ui.getTranslate(this.id)\n\n        // Set expiration time frames for the request\n        const expiration = resolved.transaction.expiration.toDate()\n        const now = new Date()\n        const expiresIn = Math.floor(expiration.getTime() - now.getTime())\n\n        // Create a new signing request based on the existing resolved request\n        const modifiedRequest = await context.createRequest({transaction: resolved.transaction})\n\n        // Set the expiration on the request LinkInfo\n        modifiedRequest.setInfoKey(\n            'link',\n            LinkInfo.from({\n                expiration,\n            })\n        )\n\n        // Add the callback to the request\n        const callback = setTransactionCallback(modifiedRequest, this.buoyUrl)\n\n        const request = modifiedRequest.encode(true, false)\n\n        // Mobile will return true or false, desktop will return undefined\n        const isSameDevice = this.data.sameDevice !== false\n\n        // Same device request\n        const sameDeviceRequest = modifiedRequest.clone()\n        const returnUrl = generateReturnUrl()\n        sameDeviceRequest.setInfoKey('same_device', true)\n        if (returnUrl) {\n            sameDeviceRequest.setInfoKey('return_path', returnUrl)\n        }\n\n        if (this.data.sameDevice) {\n            if (this.data.launchUrl) {\n                window.location.href = this.data.launchUrl\n            } else if (isAppleHandheld()) {\n                window.location.href = 'anchor://link'\n            }\n        }\n\n        const signManually = () => {\n            context.ui?.prompt({\n                title: t('transact.sign_manually.title', {default: 'Sign manually'}),\n                body: t('transact.sign_manually.body', {\n                    default:\n                        'Scan the QR-code with Anchor on another device or use the button to open it here.',\n                }),\n                elements: [\n                    {\n                        type: 'qr',\n                        data: String(request),\n                    },\n                    {\n                        type: 'link',\n                        label: t('transact.sign_manually.link.title', {default: 'Open Anchor'}),\n                        data: {\n                            href: String(sameDeviceRequest),\n                            label: t('transact.sign_manually.link.title', {default: 'Open Anchor'}),\n                        },\n                    },\n                ],\n            })\n        }\n\n        // Tell Wharf we need to prompt the user with a QR code and a button\n        const promptPromise: Cancelable<PromptResponse> = context.ui.prompt({\n            title: t('transact.title', {default: 'Complete using Anchor'}),\n            body: t('transact.body', {\n                channelName: this.data.channelName,\n                default: `Please open your Anchor Wallet on \"${this.data.channelName}\" to review and approve this transaction.`,\n            }),\n            elements: [\n                {\n                    type: 'countdown',\n                    data: {\n                        label: t('transact.await', {default: 'Waiting for response from Anchor'}),\n                        end: expiration.toISOString(),\n                    },\n                },\n                {\n                    type: 'button',\n                    label: t('transact.label', {default: 'Sign manually or with another device'}),\n                    data: {\n                        onClick: isSameDevice\n                            ? () => (window.location.href = sameDeviceRequest.encode())\n                            : signManually,\n                        label: t('transact.label', {\n                            default: 'Sign manually or with another device',\n                        }),\n                    },\n                },\n            ],\n        })\n\n        // Create a timer to test the external cancelation of the prompt, if defined\n        const timer = setTimeout(() => {\n            if (!context.ui) {\n                throw new Error('No UI available')\n            }\n            promptPromise.cancel(\n                t('error.expired', {default: 'The request expired, please try again.'})\n            )\n        }, expiresIn)\n\n        // Clear the timeout if the UI throws (which generally means it closed)\n        promptPromise.catch(() => clearTimeout(timer))\n\n        // Wait for the callback from the wallet\n        const callbackPromise = waitForCallback(callback, this.buoyWs, t)\n\n        // Assemble and send the payload to the wallet\n        if (this.data.channelUrl) {\n            const service = new URL(this.data.channelUrl).origin\n            const channel = new URL(this.data.channelUrl).pathname.substring(1)\n            const sealedMessage = sealMessage(\n                (this.data.sameDevice ? sameDeviceRequest : modifiedRequest).encode(\n                    true,\n                    false,\n                    'esr:'\n                ),\n                PrivateKey.from(this.data.privateKey),\n                PublicKey.from(this.data.signerKey)\n            )\n\n            send(Serializer.encode({object: sealedMessage}).array, {\n                service,\n                channel,\n            })\n        } else {\n            // If no channel is defined, fallback to the same device request and trigger immediately\n            window.location.href = sameDeviceRequest.encode()\n        }\n\n        // Wait for either the callback or the prompt to resolve\n        const callbackResponse = await Promise.race([callbackPromise, promptPromise]).finally(\n            () => {\n                // Clear the automatic timeout once the race resolves\n                clearTimeout(timer)\n            }\n        )\n\n        const wasSuccessful =\n            isCallback(callbackResponse) &&\n            extractSignaturesFromCallback(callbackResponse).length > 0\n\n        if (wasSuccessful) {\n            // If the callback was resolved, create a new request from the response\n            const resolvedRequest = await ResolvedSigningRequest.fromPayload(\n                callbackResponse,\n                context.esrOptions\n            )\n\n            // Return the new request and the signatures from the wallet\n            return {\n                signatures: extractSignaturesFromCallback(callbackResponse),\n                resolved: resolvedRequest,\n            }\n        }\n\n        const errorString = t('error.not_completed', {default: 'The request was not completed.'})\n\n        promptPromise.cancel(errorString)\n\n        // This shouldn't ever trigger, but just in case\n        throw new Error(errorString)\n    }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,MAAM,OAAO,UAAU;AAA3B,QACI,SAAS;AASb,aAAS,SAAS;AAAA,IAAC;AASnB,QAAI,OAAO,QAAQ;AACjB,aAAO,YAAY,uBAAO,OAAO,IAAI;AAMrC,UAAI,CAAC,IAAI,OAAO,EAAE;AAAW,iBAAS;AAAA,IACxC;AAWA,aAAS,GAAG,IAAI,SAAS,MAAM;AAC7B,WAAK,KAAK;AACV,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ;AAAA,IACtB;AAaA,aAAS,YAAY,SAAS,OAAO,IAAI,SAAS,MAAM;AACtD,UAAI,OAAO,OAAO,YAAY;AAC5B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,WAAW,IAAI,GAAG,IAAI,WAAW,SAAS,IAAI,GAC9C,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,QAAQ,QAAQ,GAAG;AAAG,gBAAQ,QAAQ,GAAG,IAAI,UAAU,QAAQ;AAAA,eAC3D,CAAC,QAAQ,QAAQ,GAAG,EAAE;AAAI,gBAAQ,QAAQ,GAAG,EAAE,KAAK,QAAQ;AAAA;AAChE,gBAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG,QAAQ;AAE3D,aAAO;AAAA,IACT;AASA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAI,EAAE,QAAQ,iBAAiB;AAAG,gBAAQ,UAAU,IAAI,OAAO;AAAA;AAC1D,eAAO,QAAQ,QAAQ,GAAG;AAAA,IACjC;AASA,aAASA,gBAAe;AACtB,WAAK,UAAU,IAAI,OAAO;AAC1B,WAAK,eAAe;AAAA,IACtB;AASA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,UAAI,QAAQ,CAAC,GACT,QACA;AAEJ,UAAI,KAAK,iBAAiB;AAAG,eAAO;AAEpC,WAAK,QAAS,SAAS,KAAK,SAAU;AACpC,YAAI,IAAI,KAAK,QAAQ,IAAI;AAAG,gBAAM,KAAK,SAAS,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACtE;AAEA,UAAI,OAAO,uBAAuB;AAChC,eAAO,MAAM,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAC1D;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,OAAO;AAC3D,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,WAAW,KAAK,QAAQ,GAAG;AAE/B,UAAI,CAAC;AAAU,eAAO,CAAC;AACvB,UAAI,SAAS;AAAI,eAAO,CAAC,SAAS,EAAE;AAEpC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK;AAClE,WAAG,CAAC,IAAI,SAAS,CAAC,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,gBAAgB,SAAS,cAAc,OAAO;AACnE,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,CAAC;AAAW,eAAO;AACvB,UAAI,UAAU;AAAI,eAAO;AACzB,aAAO,UAAU;AAAA,IACnB;AASA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AACrE,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAE/B,UAAI,YAAY,KAAK,QAAQ,GAAG,GAC5B,MAAM,UAAU,QAChB,MACA;AAEJ,UAAI,UAAU,IAAI;AAChB,YAAI,UAAU;AAAM,eAAK,eAAe,OAAO,UAAU,IAAI,QAAW,IAAI;AAE5E,gBAAQ,KAAK;AAAA,UACX,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,OAAO,GAAG;AAAA,UACrD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,EAAE,GAAG;AAAA,UACzD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,EAAE,GAAG;AAAA,UAC7D,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,EAAE,GAAG;AAAA,UACjE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,UACrE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,QAC3E;AAEA,aAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAClD,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,QAC3B;AAEA,kBAAU,GAAG,MAAM,UAAU,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,YAAI,SAAS,UAAU,QACnB;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,cAAI,UAAU,CAAC,EAAE;AAAM,iBAAK,eAAe,OAAO,UAAU,CAAC,EAAE,IAAI,QAAW,IAAI;AAElF,kBAAQ,KAAK;AAAA,YACX,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,OAAO;AAAG;AAAA,YACpD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,EAAE;AAAG;AAAA,YACxD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,EAAE;AAAG;AAAA,YAC5D,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,IAAI,EAAE;AAAG;AAAA,YAChE;AACE,kBAAI,CAAC;AAAM,qBAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAC7D,uBAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,gBAC3B;AAEA,wBAAU,CAAC,EAAE,GAAG,MAAM,UAAU,CAAC,EAAE,SAAS,IAAI;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,IAAAA,cAAa,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,SAAS;AAC1D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,KAAK;AAAA,IACpD;AAWA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,SAAS;AAC9D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,IACnD;AAYA,IAAAA,cAAa,UAAU,iBAAiB,SAAS,eAAe,OAAO,IAAI,SAAS,MAAM;AACxF,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAC/B,UAAI,CAAC,IAAI;AACP,mBAAW,MAAM,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,UAAU,IAAI;AAChB,YACE,UAAU,OAAO,OAChB,CAAC,QAAQ,UAAU,UACnB,CAAC,WAAW,UAAU,YAAY,UACnC;AACA,qBAAW,MAAM,GAAG;AAAA,QACtB;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,SAAS,CAAC,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AACvE,cACE,UAAU,CAAC,EAAE,OAAO,MACnB,QAAQ,CAAC,UAAU,CAAC,EAAE,QACtB,WAAW,UAAU,CAAC,EAAE,YAAY,SACrC;AACA,mBAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAKA,YAAI,OAAO;AAAQ,eAAK,QAAQ,GAAG,IAAI,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA;AACpE,qBAAW,MAAM,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,qBAAqB,SAAS,mBAAmB,OAAO;AAC7E,UAAI;AAEJ,UAAI,OAAO;AACT,cAAM,SAAS,SAAS,QAAQ;AAChC,YAAI,KAAK,QAAQ,GAAG;AAAG,qBAAW,MAAM,GAAG;AAAA,MAC7C,OAAO;AACL,aAAK,UAAU,IAAI,OAAO;AAC1B,aAAK,eAAe;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAKA,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AACpD,IAAAA,cAAa,UAAU,cAAcA,cAAa,UAAU;AAK5D,IAAAA,cAAa,WAAW;AAKxB,IAAAA,cAAa,eAAeA;AAK5B,QAAI,gBAAgB,OAAO,QAAQ;AACjC,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;;IC9Ua,4BAAoB,MAAK;EAElC,YAAqB,OAAY;AAC7B,UAAM,cAAc;AADH;AADrB,gCAAO;AACc,SAAA,QAAA;;;IAMZ,6BAAqB,MAAK;EAEnC,YAAqB,QAAyB,iBAAuB;AACjE,UAAM,MAAM;AADK;AAAyB;AAD9C,gCAAO;AACc,SAAA,SAAA;AAAyB,SAAA,kBAAA;;;ACNlD,IAAMC,eAAa,cAAc;IAErB;CAAZ,SAAYC,mBAAgB;AACxB,EAAAA,kBAAA,QAAA,IAAA;AACA,EAAAA,kBAAA,MAAA,IAAA;AACA,EAAAA,kBAAA,MAAA,IAAA;AACJ,GAJY,qBAAA,mBAAgB,CAAA,EAAA;IAiBf,yBAAiB,qBAAAC,QAAY;EAUtC,YAAY,SAAwB;AAChC,UAAK;AAVA;AAED,kCAAS;AACT;AACA;AACA;AACA;AACA;AAIJ,QAAI,CAAC,QAAQ,SAAS;AAClB,YAAM,IAAI,MAAM,oCAAoC;;AAExD,QAAI,CAAC,QAAQ,SAAS;AAClB,YAAM,IAAI,MAAM,qCAAqC;;AAEzD,UAAM,UAAU,QAAQ,QAAQ,QAAQ,SAAS,IAAI,EAAE,QAAQ,OAAO,EAAE;AACxE,SAAK,MAAM,GAAG,OAAO,IAAI,QAAQ,OAAO;AACxC,SAAK,WAAW,QAAQ,YAAY,iBAAiB;AACrD,SAAK,YAAY,QAAQ,aAAaF,aAAW;AACjD,QAAI,QAAQ,gBAAgB,OAAO;AAC/B,WAAK,QAAO;;;EAIpB,UAAO;AACH,QAAI,KAAK;AAAQ;AACjB,SAAK,SAAS;AACd,QAAI,UAAU;AACd,QAAI;AAEJ,UAAM,UAAU,MAAA;AACZ,YAAM,SAAS,IAAI,KAAK,UAAU,KAAK,GAAG;AAC1C,aAAO,YAAY,CAAC,UAAK;AACrB,YAAI,OAAO,SAAS,eAAe,MAAM,gBAAgB,MAAM;AAC3D,gBAAM,SAAS,IAAI,WAAU;AAC7B,iBAAO,SAAS,MAAA;AACZ,iBAAK,cAAc,IAAI,WAAW,OAAO,MAAqB,CAAC;;AAEnE,iBAAO,UAAU,MAAA;AACb,iBAAK,KAAK,SAAS,IAAI,aAAa,wBAAwB,CAAC;;AAEjE,iBAAO,kBAAkB,MAAM,IAAI;mBAC5B,OAAO,MAAM,SAAS,UAAU;AACvC,eAAK,cAAc,IAAI,YAAW,EAAG,OAAO,MAAM,IAAI,CAAC;mBAEvD,OAAOA,aAAW,WAAW,gBAC5B,MAAM,gBAAgBA,aAAW,UAAU,MAAM,QAAQ,MAAM,IAAI,IACtE;AACE,cAAI,SAAS,MAAM;AACnB,cAAI,CAACA,aAAW,OAAO,SAAS,MAAM,GAAG;AACrC,qBAASA,aAAW,OAAO,OAAO,MAAM;;AAE5C,eAAK,cACD,IAAI,WAAW,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU,CAAC;mBAEhE,MAAM,gBAAgB,YAAY;AACzC,eAAK,cAAc,MAAM,IAAI;mBACtB,MAAM,gBAAgB,aAAa;AAC1C,eAAK,cAAc,IAAI,WAAW,MAAM,IAAI,CAAC;eAC1C;AACH,eAAK,KAAK,SAAS,IAAI,aAAa,2BAA2B,CAAC;;;AAGxE,aAAO,UAAU,CAAC,UAAK;AACnB,YAAI,KAAK,WAAW,UAAU,KAAK,QAAQ;AACvC,eAAK,KAAK,SAAS,IAAI,YAAY,KAAK,CAAC;;;AAGjD,aAAO,SAAS,MAAA;AACZ,kBAAU;AACV,aAAK,KAAK,SAAS;;AAEvB,aAAO,UAAU,MAAA;AACb,YAAI,KAAK,QAAQ;AACb,uBAAa,KAAK,KAAK;AACvB,eAAK,QAAQ,WAAW,SAAS,QAAQ,SAAS,CAAC;;AAEvD,aAAK,SAAS;AACd,qBAAa,SAAS;AACtB,YAAI,KAAK,gBAAgB;AACrB,wBAAc,KAAK,cAAc;;AAErC,aAAK,KAAK,YAAY;;AAI1B,WAAK,uBAAsB;AAG3B,YAAM,aAAa;AACnB,UAAI,OAAO,WAAW,OAAO,cAAc,OAAO,WAAW,cAAc,YAAY;AACnF,mBAAW,GAAG,QAAQ,MAAA;AAClB,uBAAa,SAAS;AACtB,sBAAY,WAAW,MAAA;AACnB,uBAAW,UAAS;aACrB,KAAK,GAAI;SACf;;AAEL,WAAK,SAAS;;AAElB,YAAO;;EAGX,aAAU;AACN,SAAK,SAAS;AACd,QACI,KAAK,WACJ,KAAK,OAAO,eAAe,KAAK,UAAU,QACvC,KAAK,OAAO,eAAe,KAAK,UAAU,aAChD;AACE,WAAK,OAAO,MAAM,GAAI;;;EAI9B,IAAI,cAAW;;AACX,WAAO,KAAK,YAAU,UAAK,WAAL,mBAAa,eAAc,KAAK,UAAU;;EAG5D,cAAc,OAAiB;;AACnC,QAAI,MAAM,CAAC,MAAM,MAAQ,MAAM,CAAC,MAAM,MAAQ,MAAM,CAAC,MAAM,GAAM;AAC7D,iBAAK,WAAL,mBAAa,KAAK,IAAI,WAAW,CAAC,IAAM,IAAM,GAAM,MAAM,CAAC,CAAC,CAAC;AAC7D,cAAQ,MAAM,SAAS,CAAC;;AAE5B,QAAI;AACJ,YAAQ,KAAK,UAAQ;MACjB,KAAK,iBAAiB;AAClB,kBAAU;AACV;MACJ,KAAK,iBAAiB;AAClB,kBAAU,IAAI,YAAW,EAAG,OAAO,KAAK;AACxC;MACJ,KAAK,iBAAiB,MAAM;AACxB,YAAI;AACA,oBAAU,KAAK,MAAM,IAAI,YAAW,EAAG,OAAO,KAAK,CAAC;iBAC/CG,QAAO;AACZ,eAAK,KAAK,SAAS,IAAI,aAAa,yBAAyBA,MAAK,CAAC;AACnE;;;;AAIZ,SAAK,KAAK,WAAW,OAAO;;EAGxB,yBAAsB;AAC1B,SAAK,iBAAiB,YAAY,MAAA;;AAC9B,iBAAK,WAAL,mBAAa,MAAM;OACpB,KAAK,KAAK,GAAI;;;AAQzB,SAAS,QAAQ,OAAa;AAC1B,SAAO,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAI;AACpD;SCtKgB,QAAQ,SAAyB,KAAoB;AACjE,SAAO,IAAI,QAAa,CAAC,SAAS,WAAM;AACpC,UAAM,WAAW,IAAI,SAAS,EAAC,GAAG,SAAS,aAAa,KAAI,CAAC;AAC7D,QAAI;AACJ,QAAI;AACJ,UAAM,OAAO,CAACA,QAAe,YAAa;AACtC,mBAAa,KAAK;AAClB,UAAIA,QAAO;AACP,eAAOA,MAAK;aACT;AACH,gBAAQ,OAAO;;AAEnB,eAAS,WAAU;;AAEvB,QAAI,KAAK;AACL,UAAI,SAAS,MAAA;AACT,aAAK,IAAI,aAAa,aAAa,SAAS,CAAC;;;AAGrD,QAAI,QAAQ,SAAS;AACjB,cAAQ,WAAW,MAAA;AACf,aAAK,IAAI,aAAa,aAAa,SAAS,CAAC;SAC9C,QAAQ,OAAO;;AAEtB,aAAS,GAAG,SAAS,CAACA,WAAK;AACvB,UAAI,EAAEA,kBAAiB,cAAc;AACjC,aAAKA,MAAK;aACP;AACH,oBAAYA;;KAEnB;AACD,aAAS,KAAK,WAAW,CAAC,YAAO;AAC7B,WAAK,QAAW,OAAO;KAC1B;GACJ;AACL;AClDA,IAAM,aAAa,cAAc;IAiBrB;CAAZ,SAAYC,aAAU;AAElB,EAAAA,YAAA,UAAA,IAAA;AAEA,EAAAA,YAAA,WAAA,IAAA;AACJ,GALY,eAAA,aAAU,CAAA,EAAA;AAkBf,eAAe,KAAK,SAAmB,SAAoB;AAC9D,QAAM,QAAQ,QAAQ,SAAS,WAAW;AAC1C,QAAM,UAAU,QAAQ,QAAQ,QAAQ,OAAO,MAAM,EAAE,QAAQ,OAAO,EAAE;AACxE,QAAM,MAAM,GAAG,OAAO,IAAI,QAAQ,OAAO;AAEzC,QAAM,UAAkC,CAAA;AACxC,MAAI,QAAQ,iBAAiB;AACzB,QAAI,CAAC,QAAQ,SAAS;AAClB,YAAM,IAAI,MAAM,+CAA+C;;AAEnE,YAAQ,aAAa,IAAI,GAAG,KAAK,KAAK,QAAQ,UAAU,GAAI,CAAC;aACtD,QAAQ,SAAS;AACxB,YAAQ,kBAAkB,IAAI,GAAG,KAAK,KAAK,QAAQ,UAAU,GAAI,CAAC;;AAGtE,MAAI;AACJ,MAAI,OAAO,YAAY,YAAY,mBAAmB,YAAY;AAC9D,WAAO;SACJ;AACH,WAAO,KAAK,UAAU,OAAO;;AAEjC,QAAM,WAAW,MAAM,MAAM,KAAK,EAAC,QAAQ,QAAQ,MAAM,QAAO,CAAC;AAEjE,MAAI,KAAK,MAAM,SAAS,SAAS,GAAG,MAAM,GAAG;AACzC,QAAI,SAAS,WAAW,KAAK;AACzB,YAAM,IAAI,MAAM,2BAA2B;eACpC,SAAS,WAAW,KAAK;AAChC,YAAM,IAAI,MAAM,mBAAmB;WAChC;AACH,YAAM,IAAI,MAAM,0BAA0B,SAAS,MAAM,EAAE;;;AAInE,SAAQ,SAAS,QAAQ,IAAI,iBAAiB,KAAK,WAAW;AAClE;;;ACjEQ,IAAI,UAAU,WAAY;AAMhC,MAAI,aAAa;AAKjB,MAAI,OAAO;AAKX,WAAS,QAAQ;AACf,YAAQ,CAAA,GACN,QAAQ,CAAA;AAEV,QAAI,IAAI,GAAG,GAAG;AACd,SAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,YAAM,CAAC,IAAI;AAGX,UAAI,IAAI,KAAM,MAAM,GAAG,KAAK;AAC5B,UAAI,MAAM;AAAM,aAAK;AACrB,WAAK,MAAM,CAAC;AAGZ,YAAM,MAAM,CAAC,CAAC,IAAI;IACxB;AACI,UAAM,GAAG,IAAI,MAAM,CAAC;AACpB,UAAM,CAAC,IAAI;AAEX,iBAAa;EACjB;AAQE,WAAS,KAAK,GAAG,GAAG;AAClB,QAAI,IAAI,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG;AACzC,QAAI,MAAM,KAAK,MAAM;AAAG,UAAI;AAC5B,WAAO;EACX;AAOE,WAAS,KAAK,GAAG;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,CAAC,CAAC;AAC5B,QAAI,MAAM;AAAG,UAAI;AACjB,WAAO;EACX;AAKE,MAAI,gBAAgB;AAOpB,MAAI;AAKJ,MAAI;AAKJ,MAAI;AAKJ,MAAI;AAKJ,WAAS,WAAW;AAClB,QAAI,CAAC;AAAY,YAAK;AAGtB,aAAS,GAAG,GAAG;AACb,UAAI,GAAGC,IAAG;AACV,MAAAA,KAAI,IAAI,KAAK,CAAC;AACd,WAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,QAAAA,MAAMA,MAAK,IAAMA,OAAM,KAAM;AAC7B,aAAKA;MACb;AACM,WAAK;AACL,aAAO;IACb;AAGI,eAAW,CAAA,GACT,WAAW,CAAA,GACX,UAAU,CAAC,CAAA,GAAI,CAAA,GAAI,CAAA,GAAI,CAAA,CAAE,GACzB,UAAU,CAAC,CAAA,GAAI,CAAA,GAAI,CAAA,GAAI,CAAA,CAAE;AAE3B,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAI,IAAI,GAAG,CAAC;AAGZ,eAAS,CAAC,IAAI;AACd,eAAS,CAAC,IAAI;AAGd,cAAQ,CAAC,EAAE,CAAC,IAAK,KAAK,GAAG,CAAC,KAAK,KAAO,KAAK,KAAO,KAAK,IAAK,KAAK,GAAG,CAAC;AACrE,cAAQ,CAAC,EAAE,CAAC,IAAK,KAAK,IAAI,CAAC,KAAK,KAAO,KAAK,GAAG,CAAC,KAAK,KAAO,KAAK,IAAI,CAAC,KAAK,IAAK,KAAK,IAAI,CAAC;AAE1F,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAQ,CAAC,EAAE,CAAC,IAAK,QAAQ,IAAI,CAAC,EAAE,CAAC,MAAM,IAAM,QAAQ,IAAI,CAAC,EAAE,CAAC,KAAK;AAClE,gBAAQ,CAAC,EAAE,CAAC,IAAK,QAAQ,IAAI,CAAC,EAAE,CAAC,MAAM,IAAM,QAAQ,IAAI,CAAC,EAAE,CAAC,KAAK;MAC1E;IACA;AAEI,oBAAgB;EACpB;AA0BE,MAAI,UAAU,SAAU,SAAS,QAAQ;AAEvC,QAAI,CAAC;AAAe,eAAQ;AAG5B,QAAI,OAAO,IAAI,YAAY,MAAM;AACjC,SAAK,IAAI,UAAU,QAAU,CAAC;AAC9B,SAAK,IAAI,UAAU,QAAU,CAAC;AAC9B,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,WAAK,IAAI,QAAQ,CAAC,GAAI,OAAS,OAAQ,KAAM,CAAC;AAC9C,WAAK,IAAI,QAAQ,CAAC,GAAI,OAAS,OAAQ,KAAM,CAAC;IACpD;AAgBI,aAAS,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACnD,UAAI,QAAQ,KAAK,SAAS,GAAO,EAAE,GACjC,QAAQ,KAAK,SAAS,KAAO,MAAQ,EAAE;AAGzC,YAAM,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AAC1C,eAASC,KAAI,IAAI,OAAO,GAAGA,KAAI,IAAI,KAAK,IAAIA,MAAK;AAC/C,YAAI,IAAI,MAAMA,KAAI,CAAC;AACnB,YAAKA,KAAI,OAAO,KAAO,OAAO,KAAKA,KAAI,OAAO,GAAI;AAChD,cAAI,SAAS,MAAM,EAAE,KAAK,KAAK,SAAS,MAAM,KAAK,GAAG,KAAK,KAAK,SAAS,MAAM,IAAI,GAAG,KAAK,IAAI,SAAS,IAAI,GAAG;QACzH;AACQ,YAAIA,KAAI,OAAO,GAAG;AAChB,cAAK,KAAK,IAAM,MAAM,KAAO,QAAQ;AACrC,iBAAQ,QAAQ,KAAO,OAAO,MAAQ,KAAO;QACvD;AACQ,cAAMA,EAAC,IAAI,MAAMA,KAAI,EAAE,IAAI;MACnC;AAGM,eAAS,IAAI,GAAG,IAAIA,IAAG,KAAK,GAAG;AAC7B,iBAAS,KAAK,GAAG,KAAK,GAAG,MAAM;AAC7B,cAAI,IAAI,MAAMA,MAAK,IAAI,MAAM,IAAI,MAAM,CAAC;AACxC,cAAI,IAAI,KAAK,KAAKA,KAAI,GAAG;AACvB,kBAAM,IAAI,EAAE,IAAI;UAC5B,OAAiB;AACL,kBAAM,IAAI,EAAE,IAAI,QAAQ,CAAC,EAAE,SAAS,MAAM,EAAE,CAAC,IACzC,QAAQ,CAAC,EAAE,SAAS,MAAM,KAAK,GAAG,CAAC,IACnC,QAAQ,CAAC,EAAE,SAAS,MAAM,IAAI,GAAG,CAAC,IAClC,QAAQ,CAAC,EAAE,SAAS,IAAI,GAAG,CAAC;UAC5C;QACA;MACA;AAGM,UAAI,WAAW,KAAK,CAAC;IAC3B;AAGI,QAAI,SAAS,EAAC,YAAwB,YAAwB;AAE9D,QAAI,MAAM,SAAUC,SAAQC,UAASC,SAAQ;AAC3C;AAEA,UAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAC/B,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAC7B,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAC7B,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAC7B,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAC7B,IAAI;AAEN,UAAI,OAAO,IAAIF,QAAO,YAAYE,OAAM,GACtC,OAAO,IAAIF,QAAO,WAAWE,OAAM;AAarC,eAAS,MAAM,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI;AACzC,YAAI,IAAI;AACR,YAAI,IAAI;AACR,YAAI,IAAI;AACR,YAAI,IAAI;AACR,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AAEV,YAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GACvB,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAC7BH,KAAI;AAEN,aAAK,IAAI,MAAO,KAAK,IAAI,MAAO,KAAK,IAAI;AAGzC,aAAK,KAAK,MAAM,IAAI,MAAM,CAAC,GACzB,KAAK,KAAK,MAAM,IAAI,MAAM,CAAC,GAC3B,KAAK,KAAK,MAAM,IAAI,MAAM,CAAC,GAC3B,KAAK,KAAK,MAAM,IAAI,OAAO,CAAC;AAG9B,aAAKA,KAAI,KAAKA,KAAI,MAAO,KAAK,GAAIA,KAAKA,KAAI,KAAM,GAAG;AAClD,eAAK,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC,IAAI,MAAM,KAAK,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,KAAK,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,IAAIA,KAAI,MAAM,CAAC,GACrK,KAAK,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC,IAAI,MAAM,KAAK,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,KAAK,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,IAAIA,KAAI,MAAM,CAAC,GACvK,KAAK,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC,IAAI,MAAM,KAAK,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,KAAK,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,IAAIA,KAAI,MAAM,CAAC,GACvK,KAAK,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC,IAAI,MAAM,KAAK,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,KAAK,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,IAAIA,KAAI,OAAO,CAAC;AAC1K,eAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;QAC1C;AAGQ,aAAK,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,MAAM,IAAI,SAAS,CAAC,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,IAAIA,KAAI,MAAM,CAAC,GACnL,KAAK,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,MAAM,IAAI,SAAS,CAAC,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,IAAIA,KAAI,MAAM,CAAC,GACrL,KAAK,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,MAAM,IAAI,SAAS,CAAC,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,IAAIA,KAAI,MAAM,CAAC,GACrL,KAAK,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,MAAM,IAAI,SAAS,CAAC,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,IAAIA,KAAI,OAAO,CAAC;MAChM;AA4DM,eAAS,SAAS,IAAI,IAAI,IAAI,IAAI;AAChC,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AAEV;UACE;UAAQ;UAAQ;UAChB;UACA,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;QACf;AAEQ,aAAK,IACH,KAAK,IACL,KAAK,IACL,KAAK;MACf;AASM,eAAS,SAAS,IAAI,IAAI,IAAI,IAAI;AAChC,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AAEV,YAAI,IAAI;AAER;UACE;UAAQ;UAAQ;UAChB;UACA;UACA;UACA;UACA;QACV;AAEQ,YAAI,IAAI,KAAK,IAAI,KAAK;AAEtB,aAAK,KAAK,IACR,KAAK,KAAK,IACV,KAAK,KAAK,IACV,KAAK,KAAK;AAEZ,aAAK,IACH,KAAK,IACL,KAAK,IACL,KAAK;MACf;AAyIM,eAAS,SAAS,IAAI,IAAI,IAAI,IAAI;AAChC,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AAEV,YAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAC/B,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAC7BA,KAAI,GAAG,IAAI;AAEb,aAAK,KAAK,IACR,KAAK,KAAK,IACV,KAAK,KAAK,IACV,KAAK,KAAK;AAEZ,aAAK,KAAK,GACR,KAAK,KAAK,GACV,KAAK,KAAK,GACV,KAAK,KAAK;AAEZ,gBAAQA,KAAI,KAAK,KAAKA,KAAKA,KAAI,IAAK,GAAG;AACrC,cAAI,OAAO,IAAI;AACb,iBAAK,KAAK,IACR,KAAK,KAAK,IACV,KAAK,KAAK,IACV,KAAK,KAAK;UACxB;AAEU,eAAM,MAAM,IAAM,OAAO,IACvB,KAAM,MAAM,IAAM,OAAO,IACzB,KAAM,MAAM,IAAM,OAAO,IACzB,KAAM,MAAM;AAEd,cAAI,KAAK;AAET,eAAM,OAAO,IAAM,MAAM,IACvB,KAAM,OAAO,IAAM,MAAM,IACzB,KAAM,OAAO,IAAM,MAAM,IACzB,KAAM,OAAO;AAEf,cAAI;AAAG,iBAAK,KAAK;QAC3B;AAEQ,aAAK,IACH,KAAK,IACL,KAAK,IACL,KAAK;MACf;AAQM,eAAS,WAAW,GAAG;AACrB,YAAI,IAAI;AACR,YAAI;MACZ;AAWM,eAAS,UAAU,IAAI,IAAI,IAAI,IAAI;AACjC,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AAEV,aAAK,IACH,KAAK,IACL,KAAK,IACL,KAAK;MACf;AAWM,eAAS,OAAO,IAAI,IAAI,IAAI,IAAI;AAC9B,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AAEV,aAAK,IACH,KAAK,IACL,KAAK,IACL,KAAK;MACf;AAWM,eAAS,UAAU,IAAI,IAAI,IAAI,IAAI;AACjC,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AAEV,aAAK,IACH,KAAK,IACL,KAAK,IACL,KAAK;MACf;AAWM,eAAS,SAAS,IAAI,IAAI,IAAI,IAAI;AAChC,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AAEV,aAAK,IACH,KAAK,IACL,KAAK,IACL,KAAK;MACf;AAWM,eAAS,YAAY,IAAI,IAAI,IAAI,IAAI;AACnC,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AACV,aAAK,KAAK;AAEV,aAAM,CAAC,KAAK,KAAM,KAAK,IACrB,KAAM,CAAC,KAAK,KAAM,KAAK,IACvB,KAAM,CAAC,KAAK,KAAM,KAAK,IACvB,KAAM,CAAC,KAAK,KAAM,KAAK;MACjC;AASM,eAAS,UAAU,KAAK;AACtB,cAAM,MAAM;AAEZ,YAAI,MAAM;AAAI,iBAAO;AAErB,aAAK,MAAM,CAAC,IAAI,OAAO,IACrB,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,KAC5B,KAAK,MAAM,CAAC,IAAI,OAAO,IAAI,KAC3B,KAAK,MAAM,CAAC,IAAI,KAAK,KACrB,KAAK,MAAM,CAAC,IAAI,OAAO,IACvB,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,KAC5B,KAAK,MAAM,CAAC,IAAI,OAAO,IAAI,KAC3B,KAAK,MAAM,CAAC,IAAI,KAAK,KACrB,KAAK,MAAM,CAAC,IAAI,OAAO,IACvB,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,KAC5B,KAAK,MAAM,EAAE,IAAI,OAAO,IAAI,KAC5B,KAAK,MAAM,EAAE,IAAI,KAAK,KACtB,KAAK,MAAM,EAAE,IAAI,OAAO,IACxB,KAAK,MAAM,EAAE,IAAI,OAAO,KAAK,KAC7B,KAAK,MAAM,EAAE,IAAI,OAAO,IAAI,KAC5B,KAAK,MAAM,EAAE,IAAI,KAAK;AAExB,eAAO;MACf;AASM,eAAS,OAAO,KAAK;AACnB,cAAM,MAAM;AAEZ,YAAI,MAAM;AAAI,iBAAO;AAErB,aAAK,MAAM,CAAC,IAAI,OAAO,IACrB,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,KAC5B,KAAK,MAAM,CAAC,IAAI,OAAO,IAAI,KAC3B,KAAK,MAAM,CAAC,IAAI,KAAK,KACrB,KAAK,MAAM,CAAC,IAAI,OAAO,IACvB,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,KAC5B,KAAK,MAAM,CAAC,IAAI,OAAO,IAAI,KAC3B,KAAK,MAAM,CAAC,IAAI,KAAK,KACrB,KAAK,MAAM,CAAC,IAAI,OAAO,IACvB,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,KAC5B,KAAK,MAAM,EAAE,IAAI,OAAO,IAAI,KAC5B,KAAK,MAAM,EAAE,IAAI,KAAK,KACtB,KAAK,MAAM,EAAE,IAAI,OAAO,IACxB,KAAK,MAAM,EAAE,IAAI,OAAO,KAAK,KAC7B,KAAK,MAAM,EAAE,IAAI,OAAO,IAAI,KAC5B,KAAK,MAAM,EAAE,IAAI,KAAK;AAExB,eAAO;MACf;AAwBM,eAAS,OAAO,MAAM,KAAK,KAAK;AAC9B,eAAO,OAAO;AACd,cAAM,MAAM;AACZ,cAAM,MAAM;AAEZ,YAAI,MAAM;AAEV,YAAI,MAAM;AAAI,iBAAO;AAErB,gBAAQ,MAAM,MAAM,IAAI;AACtB,wBAAc,OAAO,CAAC;YACpB,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK,IAAI,KAAK,MAAM,CAAC;YAC7E,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK,IAAI,KAAK,MAAM,CAAC;YAC7E,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,KAAK,MAAM,EAAE;YAC/E,KAAK,MAAM,EAAE,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,KAAK,MAAM,EAAE;UAC7F;AAEU,eAAK,MAAM,CAAC,IAAI,OAAO,IACrB,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,KAC5B,KAAK,MAAM,CAAC,IAAI,OAAO,IAAI,KAC3B,KAAK,MAAM,CAAC,IAAI,KAAK,KACrB,KAAK,MAAM,CAAC,IAAI,OAAO,IACvB,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,KAC5B,KAAK,MAAM,CAAC,IAAI,OAAO,IAAI,KAC3B,KAAK,MAAM,CAAC,IAAI,KAAK,KACrB,KAAK,MAAM,CAAC,IAAI,OAAO,IACvB,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,KAC5B,KAAK,MAAM,EAAE,IAAI,OAAO,IAAI,KAC5B,KAAK,MAAM,EAAE,IAAI,KAAK,KACtB,KAAK,MAAM,EAAE,IAAI,OAAO,IACxB,KAAK,MAAM,EAAE,IAAI,OAAO,KAAK,KAC7B,KAAK,MAAM,EAAE,IAAI,OAAO,IAAI,KAC5B,KAAK,MAAM,EAAE,IAAI,KAAK;AAExB,gBAAO,MAAM,KAAM,GACjB,MAAO,MAAM,KAAM,GACnB,MAAO,MAAM,KAAM;QAC/B;AAEQ,eAAO,MAAM;MACrB;AAWM,eAAS,IAAI,MAAM,KAAK,KAAK;AAC3B,eAAO,OAAO;AACd,cAAM,MAAM;AACZ,cAAM,MAAM;AAEZ,YAAI,MAAM;AAEV,YAAI,MAAM;AAAI,iBAAO;AAErB,gBAAQ,MAAM,MAAM,IAAI;AACtB,qBAAW,OAAO,CAAC;YACjB,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK,IAAI,KAAK,MAAM,CAAC;YAC7E,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK,IAAI,KAAK,MAAM,CAAC;YAC7E,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,KAAK,MAAM,EAAE;YAC/E,KAAK,MAAM,EAAE,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,KAAK,MAAM,EAAE;UAC7F;AAEU,gBAAO,MAAM,KAAM,GACjB,MAAO,MAAM,KAAM,GACnB,MAAO,MAAM,KAAM;QAC/B;AAEQ,eAAO,MAAM;MACrB;AAKM,UAAI,gBAAgB,CAAC,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,QAAQ;AAKnG,UAAI,aAAa,CAAC,UAAU,QAAQ;AAKpC,aAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;MACR;IACA,EAAM,QAAQ,SAAS,MAAM;AAEzB,QAAI,UAAU;AAEd,WAAO;EACX;AAOE,UAAQ,MAAM;;IAEZ,KAAK;;;;EAIT;;;;;EAOI,QAAQ,MAAM;;IAEZ,KAAK;;;;EAIX;;;;;EAOI,QAAQ,MAAM;IACZ,KAAK;;EAEX;AAOE,UAAQ,YAAY;AAEpB,SAAO;AACT,EAAC;ACl7Be,SAAA,WAAW,MAAmB,UAAiB;AAC3D,QAAM,OAAO,OAAO,KAAK,aAAa,YAAY;AAElD,MAAI,OAAO,QAAS,QAAQ;AACxB,UAAM,IAAI,MAAM,6DAA6D;AAEjF,SAAO,QAAQ,IAAI,WAAW,IAAI,YAAY,IAAI,CAAC;AAEnD,SAAO;AACX;AAEM,SAAU,YACZ,MACA,MACA,MACA,MACA,MAAY;AAEZ,QAAM,OAAO,KAAK,SAAS;AAC3B,QAAM,OAAO,OAAO,OAAO,OAAO;AAElC,OAAK,IAAI,KAAK,SAAS,MAAM,OAAO,IAAI,GAAG,IAAI;AAE/C,SAAO;AACX;AAMM,SAAU,SAAS,GAAa;AAClC,SAAO,aAAa;AACxB;AAEgB,SAAA,aAAa,KAAiB;AAC1C,QAAM,cAAc,IAAI,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,QAAQ,CAAC;AAClE,QAAM,MAAM,IAAI,WAAW,WAAW;AAEtC,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,QAAI,IAAI,IAAI,CAAC,GAAG,MAAM;AACtB,cAAU,IAAI,CAAC,EAAE;EACpB;AACD,SAAO;AACX;ICzCa,YAAG;EAQZ,YAAY,KAAiB,IAA4B,UAAU,MAAM,MAAW;AAH7E,SAAG,MAAG;AACN,SAAG,MAAG;AAGT,SAAK,OAAO;AAGZ,SAAK,OAAO,WAAU,EAAG,SAAS,QAAQ,SAAS;AACnD,SAAK,MAAM,IAAI,QAAQ,MAAM,KAAK,KAAK,MAAM;AAG7C,SAAK,MAAM;AACX,SAAK,MAAM;AAGX,UAAM,SAAS,IAAI;AACnB,QAAI,WAAW,MAAM,WAAW,MAAM,WAAW;AAAI,YAAM,IAAI,UAAU,kBAAkB;AAE3F,UAAM,UAAU,IAAI,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AACvE,SAAK,IAAI,QACL,UAAU,GACV,QAAQ,UAAU,CAAC,GACnB,QAAQ,UAAU,CAAC,GACnB,QAAQ,UAAU,CAAC,GACnB,QAAQ,UAAU,EAAE,GACpB,SAAS,KAAK,QAAQ,UAAU,EAAE,IAAI,GACtC,SAAS,KAAK,QAAQ,UAAU,EAAE,IAAI,GACtC,SAAS,KAAK,QAAQ,UAAU,EAAE,IAAI,GACtC,SAAS,KAAK,QAAQ,UAAU,EAAE,IAAI,CAAC;AAI3C,QAAI,OAAO,QAAW;AAClB,UAAI,GAAG,WAAW;AAAI,cAAM,IAAI,UAAU,iBAAiB;AAE3D,YAAM,SAAS,IAAI,SAAS,GAAG,QAAQ,GAAG,YAAY,GAAG,UAAU;AAEnE,WAAK,IAAI,OACL,OAAO,UAAU,CAAC,GAClB,OAAO,UAAU,CAAC,GAClB,OAAO,UAAU,CAAC,GAClB,OAAO,UAAU,EAAE,CAAC;IAE3B,OAAM;AACH,WAAK,IAAI,OAAO,GAAG,GAAG,GAAG,CAAC;IAC7B;AAED,SAAK,UAAU;;EAGnB,oBAAoB,MAAgB;AAChC,QAAI,CAAC,SAAS,IAAI;AAAG,YAAM,IAAI,UAAU,6BAA6B;AAEtE,UAAM,MAAM,KAAK;AACjB,UAAM,OAAO,KAAK;AAClB,UAAM,QAAQ,QAAQ,IAAI,KAAK,IAAI;AACnC,UAAM,OAAO,QAAQ;AACrB,QAAI,MAAM,KAAK;AACf,QAAI,MAAM,KAAK;AACf,QAAI,OAAO;AACX,QAAI,OAAO,KAAK,UAAU;AAC1B,QAAI,OAAO;AACX,UAAM,OAAQ,MAAM,OAAQ;AAC5B,QAAI,OAAO;AAEX,UAAM,SAAS,IAAI,WAAW,IAAI;AAElC,WAAO,OAAO,GAAG;AACb,aAAO,YAAY,MAAM,MAAM,KAAK,MAAM,MAAM,IAAI;AACpD,aAAO;AACP,cAAQ;AACR,cAAQ;AAER,aAAO,IAAI,OAAO,OAAO,OAAO,KAAK,GAAG;AAExC,UAAI;AAAM,eAAO,IAAI,KAAK,SAAS,KAAK,MAAM,IAAI,GAAG,IAAI;AACzD,cAAQ;AAER,UAAI,OAAO,KAAK;AACZ,eAAO;AACP,eAAO;MACV,OAAM;AACH,cAAM;AACN,cAAM;MACT;IACJ;AAED,SAAK,MAAM;AACX,SAAK,MAAM;AAEX,WAAO;;EAGX,qBAAkB;AACd,UAAM,MAAM,KAAK;AACjB,UAAM,OAAO,KAAK;AAClB,UAAM,QAAQ,QAAQ,IAAI,KAAK,IAAI;AACnC,UAAM,OAAO,QAAQ;AACrB,UAAM,MAAM,KAAK;AACjB,QAAI,MAAM,KAAK;AACf,UAAM,OAAO,KAAM,MAAM;AACzB,QAAI,OAAO;AAGX,QAAI,KAAK,SAAS;AACd,eAAS,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG;AAC3B,aAAK,MAAM,MAAM,CAAC,IAAI;MACzB;AACD,aAAO;AACP,aAAO;IACV,WAAU,MAAM,IAAI;AACjB,YAAM,IAAI,UAAU,kDAAkD;IACzE;AAKD,UAAM,SAAS,IAAI,WAAW,IAAI;AAElC,QAAI;AAAK,UAAI,OAAO,OAAO,OAAO,KAAK,GAAG;AAE1C,QAAI;AAAM,aAAO,IAAI,KAAK,SAAS,KAAK,MAAM,IAAI,CAAC;AAEnD,SAAK,MAAM;AACX,SAAK,MAAM;AAEX,WAAO;;EAGX,oBAAoB,MAAgB;AAChC,QAAI,CAAC,SAAS,IAAI;AAAG,YAAM,IAAI,UAAU,6BAA6B;AAEtE,UAAM,MAAM,KAAK;AACjB,UAAM,OAAO,KAAK;AAClB,UAAM,QAAQ,QAAQ,IAAI,KAAK,IAAI;AACnC,UAAM,OAAO,QAAQ;AACrB,QAAI,MAAM,KAAK;AACf,QAAI,MAAM,KAAK;AACf,QAAI,OAAO;AACX,QAAI,OAAO,KAAK,UAAU;AAC1B,QAAI,OAAO;AACX,QAAI,OAAQ,MAAM,OAAQ;AAC1B,QAAI,OAAO;AACX,QAAI,OAAO;AAEX,QAAI,KAAK,SAAS;AACd,aAAO,MAAM,OAAO,QAAQ;AAC5B,cAAQ;IACX;AAED,UAAM,SAAS,IAAI,WAAW,IAAI;AAElC,WAAO,OAAO,GAAG;AACb,aAAO,YAAY,MAAM,MAAM,KAAK,MAAM,MAAM,IAAI;AACpD,aAAO;AACP,cAAQ;AACR,cAAQ;AAER,aAAO,IAAI,OAAO,OAAO,OAAO,KAAK,OAAO,CAAC,OAAO,OAAO,EAAE;AAE7D,UAAI;AAAM,eAAO,IAAI,KAAK,SAAS,KAAK,MAAM,IAAI,GAAG,IAAI;AACzD,cAAQ;AAER,UAAI,OAAO,KAAK;AACZ,eAAO;AACP,eAAO;MACV,OAAM;AACH,cAAM;AACN,cAAM;MACT;IACJ;AAED,SAAK,MAAM;AACX,SAAK,MAAM;AAEX,WAAO;;EAGX,qBAAkB;AACd,UAAM,MAAM,KAAK;AACjB,UAAM,OAAO,KAAK;AAClB,UAAM,QAAQ,QAAQ,IAAI,KAAK,IAAI;AACnC,UAAM,OAAO,QAAQ;AACrB,UAAM,MAAM,KAAK;AACjB,UAAM,MAAM,KAAK;AACjB,QAAI,OAAO;AAEX,QAAI,MAAM,GAAG;AACT,UAAI,MAAM,IAAI;AAEV,cAAM,IAAI,MAAM,kDAAkD;MAIrE;AAED,UAAI,OAAO,OAAO,OAAO,KAAK,GAAG;AAEjC;;QAA2C,KAAK;QAAS;AACrD,cAAM,MAAM,KAAK,MAAM,OAAO,CAAC;AAC/B,YAAI,MAAM,KAAK,MAAM,MAAM,MAAM;AAAM,gBAAM,IAAI,MAAM,aAAa;AAEpE,YAAI,SAAS;AACb,iBAAS,IAAI,KAAK,IAAI,GAAG;AAAK,oBAAU,MAAM,KAAK,MAAM,OAAO,CAAC;AACjE,YAAI;AAAQ,gBAAM,IAAI,MAAM,aAAa;AAEzC,gBAAQ;MACX;IACJ;AAED,UAAM,SAAS,IAAI,WAAW,IAAI;AAElC,QAAI,OAAO,GAAG;AACV,aAAO,IAAI,KAAK,SAAS,KAAK,MAAM,IAAI,CAAC;IAC5C;AAED,SAAK,MAAM;AACX,SAAK,MAAM;AAEX,WAAO;;AAEd;IClOY,gBAAA,SAAO;EAWhB,YAAY,KAAiB,IAAiB,UAAU,MAAM,KAAS;AACnE,SAAK,MAAM,MAAM,MAAM,IAAI,IAAI,KAAK,IAAI,SAAS,KAAK;;EAT1D,OAAO,QAAQ,MAAkB,KAAiB,UAAU,MAAM,IAAe;AAC7E,WAAO,IAAI,SAAQ,KAAK,IAAI,OAAO,EAAE,QAAQ,IAAI;;EAGrD,OAAO,QAAQ,MAAkB,KAAiB,UAAU,MAAM,IAAe;AAC7E,WAAO,IAAI,SAAQ,KAAK,IAAI,OAAO,EAAE,QAAQ,IAAI;;EAOrD,QAAQ,MAAgB;AACpB,UAAM,KAAK,KAAK,IAAI,oBAAoB,IAAI;AAC5C,UAAM,KAAK,KAAK,IAAI,mBAAkB;AAEtC,WAAO,UAAU,IAAI,EAAE;;EAG3B,QAAQ,MAAgB;AACpB,UAAM,KAAK,KAAK,IAAI,oBAAoB,IAAI;AAC5C,UAAM,KAAK,KAAK,IAAI,mBAAkB;AAEtC,WAAO,UAAU,IAAI,EAAE;;AAE9B;;;ACuBM,SAAS,WAAW,YAAY,QAAQ,KAAK,MAAM;AACtD,MAAI,IAAI,UAAU,QAAQ,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,yBAAyB,QAAQ,GAAG,IAAI,MAAM;AAC3H,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa;AAAY,QAAI,QAAQ,SAAS,YAAY,QAAQ,KAAK,IAAI;;AACxH,aAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG;AAAK,UAAI,IAAI,WAAW,CAAC;AAAG,aAAK,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,QAAQ,KAAK,CAAC,IAAI,EAAE,QAAQ,GAAG,MAAM;AAChJ,SAAO,IAAI,KAAK,KAAK,OAAO,eAAe,QAAQ,KAAK,CAAC,GAAG;AAChE;ACxDO,IAAM,gBAAN,MAAMI,uBAAsB,OAAM;;AACT,WAAA;EAA3B,OAAO,MAAM,YAAY;AAAkB,GAAA,cAAA,WAAA,QAAA,MAAA;AACpB,WAAA;EAAvB,OAAO,MAAM,QAAQ;AAAgB,GAAA,cAAA,WAAA,SAAA,MAAA;AACf,WAAA;EAAtB,OAAO,MAAM,OAAO;AAAoB,GAAA,cAAA,WAAA,cAAA,MAAA;AACjB,WAAA;EAAvB,OAAO,MAAM,QAAQ;AAAmB,GAAA,cAAA,WAAA,YAAA,MAAA;AAJhC,gBAAa,WAAA;EADzB,OAAO,KAAK,gBAAgB;AAChB,GAAA,aAAa;AAQnB,IAAM,aAAN,MAAMC,oBAAmB,OAAM;;AACZ,WAAA;EAArB,OAAO,MAAM,MAAM;AAAqB,GAAA,WAAA,WAAA,gBAAA,MAAA;AACb,WAAA;EAA3B,OAAO,MAAM,YAAY;AAAyB,GAAA,WAAA,WAAA,eAAA,MAAA;AACR,WAAA;EAA1C,OAAO,MAAM,UAAU,EAAC,WAAW,KAAI,CAAC;AAAqB,GAAA,WAAA,WAAA,cAAA,MAAA;AAHrD,aAAU,WAAA;EADtB,OAAO,KAAK,aAAa;AACb,GAAA,UAAU;AAOhB,IAAM,WAAN,MAAMC,kBAAiB,OAAM;;AACA,WAAA;EAA/B,OAAO,MAAM,gBAAgB;AAA2B,GAAA,SAAA,WAAA,cAAA,MAAA;AADhD,WAAQ,WAAA;EADpB,OAAO,KAAK,WAAW;AACX,GAAA,QAAQ;ACfd,IAAM,cAAN,MAAMC,qBAAoB,OAAM;;AACP,WAAA;EAA3B,OAAO,MAAM,YAAY;AAAkB,GAAA,YAAA,WAAA,QAAA,MAAA;AACpB,WAAA;EAAvB,OAAO,MAAM,QAAQ;AAAgB,GAAA,YAAA,WAAA,SAAA,MAAA;AACf,WAAA;EAAtB,OAAO,MAAM,OAAO;AAAoB,GAAA,YAAA,WAAA,cAAA,MAAA;AACjB,WAAA;EAAvB,OAAO,MAAM,QAAQ;AAAmB,GAAA,YAAA,WAAA,YAAA,MAAA;AAJhC,cAAW,WAAA;EADvB,OAAO,KAAK,cAAc;AACd,GAAA,WAAW;AAQjB,IAAM,cAAN,MAAMC,qBAAoB,OAAM;;AACb,WAAA;EAArB,OAAO,MAAM,MAAM;AAAqB,GAAA,YAAA,WAAA,gBAAA,MAAA;AACb,WAAA;EAA3B,OAAO,MAAM,YAAY;AAAyB,GAAA,YAAA,WAAA,eAAA,MAAA;AACR,WAAA;EAA1C,OAAO,MAAM,UAAU,EAAC,WAAW,KAAI,CAAC;AAAqB,GAAA,YAAA,WAAA,cAAA,MAAA;AAHrD,cAAW,WAAA;EADvB,OAAO,KAAK,cAAc;AACd,GAAA,WAAW;AAOjB,IAAM,WAAN,MAAMC,kBAAiB,OAAM;;AACA,WAAA;EAA/B,OAAO,MAAM,gBAAgB;AAA2B,GAAA,SAAA,WAAA,cAAA,MAAA;AADhD,WAAQ,WAAA;EADpB,OAAO,KAAK,WAAW;AACX,GAAA,QAAQ;ACfd,eAAe,gBAAgB,cAAc,QAAQ,GAAC;AAEzD,QAAM,mBAAmB,MAAM,QAAQ,EAAC,GAAG,cAAc,WAAW,UAAU,UAAS,CAAC;AAExF,MAAI,CAAC,kBAAkB;AAEnB,UAAM,IAAI,MAAM,iBAAiB,QAAQ;EAC5C;AAGD,MAAI,OAAO,iBAAiB,aAAa,UAAU;AAC/C,UAAM,IAAI,MAAM,iBAAiB,QAAQ;EAC5C;AAGD,QAAM,UAAU,KAAK,MAAM,gBAAgB;AAE3C,MAAI,QAAQ,OAAO,UAAa,QAAQ,OAAO,UAAa,QAAQ,QAAQ,QAAW;AACnF,UAAM,IAAI,MAAM,EAAE,mBAAmB,EAAC,SAAS,yCAAwC,CAAC,CAAC;EAC5F;AAED,SAAO;AACX;SCUgB,OAAI;AAChB,MAAIC,QAAO,IACP;AACJ,QAAM,QAAQ;AACd,OAAK,KAAK,GAAG,KAAK,IAAI,MAAM,GAAG;AAC3B,YAAQ,IAAE;MACN,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;AACD,QAAAA,SAAQ;AACR;MACJ,KAAK;AACD,QAAAA,SAAQ;AACR;MACJ,KAAK;AACD,QAAAA,SAAQ,MAAO,KAAK,OAAM,IAAK,IAAM,IAAI,CAAE;AAC3C;MACJ;AACI,QAAAA,SAAQ,MAAO,KAAK,OAAM,IAAK,KAAM,CAAC;IAC7C;EACJ;AACD,SAAOA;AACX;SAGgB,oBAAiB;AAE7B,MAAI,gBAAe,KAAM,iBAAgB,GAAI;AACzC,WAAO;EACV;AAED,MAAI,YAAW,GAAI;AAEf,WAAO;EACV;AACD,MAAI,aAAY,GAAI;AAEhB,WAAO;EACV;AACD,MAAI,gBAAe,KAAM,QAAO,GAAI;AAEhC,WAAO;EACV;AACD,MAAI,gBAAe,GAAI;AAEnB,UAAM,WAAW;AACjB,QAAI,KAAK,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,IAAI;AAC9C,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,YAAM,SAAS,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,SAAS,MAAM,CAAC;IACpE;AACD,WAAO;EACV;AAED,MAAI,UAAS,KAAM,UAAS,GAAI;AAC5B,WAAO;EACV;AAED,MAAI,UAAS,KAAM,OAAM,GAAI;AACzB,WAAO;EACV;AAED,MAAI,UAAS,KAAM,QAAO,GAAI;AAC1B,WAAO;EACV;AAED,MAAI,UAAS,KAAM,QAAO,GAAI;AAC1B,WAAO;EACV;AAED,MAAI,UAAS,KAAM,iBAAgB,GAAI;AACnC,WAAO;EACV;AAED,MAAI,UAAS,KAAM,eAAc,GAAI;AACjC,WAAO;EACV;AAED,SAAO,OAAO,SAAS;AAC3B;SAEgB,kBAAe;AAC3B,SAAO,kBAAkB,KAAK,UAAU,SAAS;AACrD;SAEgB,cAAW;AACvB,SAAO,QAAQ,KAAK,UAAU,SAAS;AAC3C;SAEgB,iBAAc;AAC1B,SAAO,0BAA0B,KAAK,UAAU,SAAS;AAC7D;SAEgB,YAAS;AACrB,SAAO,WAAW,KAAK,UAAU,SAAS;AAC9C;SAEgB,eAAY;AACxB,SAAO,QAAQ,KAAK,UAAU,SAAS;AAC3C;SAEgB,UAAO;AACnB,SAAO,MAAM,KAAK,UAAU,SAAS,KAAK,QAAQ,KAAK,UAAU,SAAS;AAC9E;SAEgB,SAAM;AAClB,SAAO,MAAM,KAAK,UAAU,SAAS;AACzC;SAEgB,UAAO;AACnB,SAAO,UAAU,OAAO,KAAK,OAAO,UAAU,OAAO,EAAE,YAAY;AACvE;SAEgB,YAAS;AACrB,SAAO,UAAU,KAAK,UAAU,SAAS;AAC7C;SAEgB,mBAAgB;AAG5B,SAAO,CAAC,CAAC,OAAO;AACpB;SAEgB,mBAAgB;AAC5B,SACI,KAAK,KAAK,UAAU,SAAS,KAC5B,UAAU,KAAK,UAAU,SAAS,KAAK,iBAAgB;AAEhE;SAEgB,gBAAa;AACzB,SACI,gBAAe,KACf,YAAW,KACX,eAAc,KACd,aAAY,KACZ,UAAS,KACT,iBAAgB;AAExB;AChIO,eAAe,sBAClB,SACA,SAAe;AAGf,QAAM,aAAa,WAAW,SAAS,IAAI;AAC3C,QAAM,aAAa,WAAW,SAAQ;AAGtC,QAAM,aAAa,YAAY,KAAK;IAChC,cAAc,QAAQ;IACtB,aAAa;IACb,YAAY,aAAY;EAC3B,CAAA;AAGD,QAAM,eAAe,EAAE,QAAQ,SAAS,QAAQ,OAAO,WAAW;AAGlE,QAAM,kBAAkB,uBAAuB,OAAO;AAGtD,QAAM,UAA0B,eAC1B,OACA,QAAQ,QACR,QAAQ,KAAK,QAAQ,MAAM,GAAG,KAAK,IACnC;AAEN,QAAM,WAAsB,eACtB,QAAQ,OAAO,IAAI,CAAC,MAAM,QAAQ,KAAK,EAAE,GAAG,KAAK,CAAC,IAClD,CAAA;AAGN,QAAM,UAAU,eAAe,SAC3B;IACI,UAAU,gBAAgB,eAAe;IACzC,OAAO,OAAO,QAAQ,OAAO;IAC7B;IACA;IACA,MAAM;MACF,MAAM;MACN,OAAO,OAAO,QAAQ,OAAO;IAChC;EACJ,GACD,QAAQ,UAAU;AAGtB,QAAM,oBAAoB,QAAQ,MAAK;AACvC,MAAI,OAAO,WAAW,aAAa;AAC/B,UAAM,YAAY,kBAAiB;AACnC,sBAAkB,WAAW,eAAe,IAAI;AAChD,QAAI,cAAc,QAAW;AACzB,wBAAkB,WAAW,eAAe,SAAS;IACxD;EACJ;AAGD,SAAO;IACH,UAAU;IACV;IACA;IACA;IACA;;AAER;AASgB,SAAA,uBAAuB,SAAyB,SAAO;AACnE,QAAM,WAAW,uBAAuB,OAAO;AAE/C,UAAQ,YAAY,GAAG,SAAS,OAAO,IAAI,SAAS,OAAO,IAAI,IAAI;AAEnE,SAAO;AACX;SAEgB,eAAY;AACxB,QAAM,UAAU;AAChB,MAAI,QAAQ,0BAA0B,OAAO;AAC7C,MAAI,OAAO,cAAc,aAAa;AAClC,aAAS,MAAM,UAAU;EAC5B;AACD,SAAO;AACX;AAEM,SAAU,gBAAgB,iBAA+B;AAC3D,QAAM,EAAC,SAAS,QAAO,IAAI;AAC3B,SAAO;IACH,KAAK,GAAG,OAAO,IAAI,OAAO;IAC1B,YAAY;;AAEpB;AAEA,SAAS,uBAAuB,SAAO;AACnC,SAAO;IACH,SAAS;IACT,SAAS,KAAI;;AAErB;AAEM,SAAU,YACZ,SACA,YACA,WACA,OAAc;AAEd,QAAM,SAAS,WAAW,aAAa,SAAS;AAChD,MAAI,CAAC,OAAO;AACR,YAAQ,OAAO,OAAM;EACxB;AACD,QAAM,MAAM,YAAY,KAAK,WAAW,OAAO,EAAC,QAAQ,MAAK,CAAC,EAAE,UAAU,OAAO,KAAK,CAAC;AACvF,QAAM,MAAM,IAAI,QAAQ,IAAI,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,MAAM,MAAM,IAAI,EAAE,CAAC;AACvE,QAAM,aAAa,MAAM,KAAK,IAAI,QAAQ,MAAM,KAAK,SAAS,MAAM,EAAE,KAAK,CAAC;AAC5E,QAAM,eAAe,IAAI,SAAS,YAAY,KAAK,IAAI,KAAK,EAAE,MAAM,MAAM;AAC1E,QAAM,WAAW,aAAa,UAAU,GAAG,IAAI;AAC/C,SAAO,cAAc,KAAK;IACtB,MAAM,WAAW,SAAQ;IACzB;IACA;IACA;EACH,CAAA;AACL;AAEO,eAAe,4BAA4B,kBAAkB,SAAqB;AACrF,MAAI,CAAC,iBAAiB,OAAO,iBAAiB,IAAI,WAAW,GAAG;AAC5D,UAAM,IAAI,MAAM,oDAAoD;EACvE;AAED,MAAI;AACJ,MAAI,CAAC,QAAQ,SAAS,QAAQ,OAAO,SAAS,GAAG;AAC7C,QAAI,CAAC,iBAAiB,KAAK;AACvB,YAAM,IAAI,MAAM,mEAAmE;IACtF;EACJ,OAAM;AACH,YAAQ,QAAQ,SAAS,QAAQ,OAAO,CAAC;AAEzC,QAAI,iBAAiB,OAAO,OAAO,MAAM,EAAE,MAAM,iBAAiB,KAAK;AACnE,YAAM,IAAI,MAAM,iCAAiC;IACpD;EACJ;AACL;AAEM,SAAU,8BAA8B,SAAwB;AAClE,QAAM,aAAuB,CAAA;AAE7B,MAAI,QAAQ;AACZ,MAAI,MAA0B,QAAQ;AAEtC,SAAO,KAAK;AACR,eAAW,KAAK,OAAO,GAAG,CAAC;AAE3B,UAAM,QAAQ,MAAM,KAAK,EAAE;AAE3B;EACH;AAGD,SAAO,CAAC,GAAG,IAAI,IAAI,UAAU,CAAC,EAAE,IAAI,CAAC,MAAM,UAAU,KAAK,CAAC,CAAC;AAChE;AAEM,SAAU,WAAW,QAAW;AAClC,SAAO,QAAQ;AACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChNA,IAAA,sBAAe;EACX;EACA;EACA,WAAW;EACX,WAAW;;ACkCT,IAAO,qBAAP,cAAkC,qBAAoB;EAqBxD,YAAY,SAA6B;AACrC,UAAK;AART,SAAE,KAAG;AAKL,SAAY,eAAG;AAYN,SAAA,SAA6B;;MAElC,qBAAqB;;MAErB,0BAA0B;;AAKrB,SAAA,WAAiC,qBAAqB,KAAK;MAChE,MAAM;MACN,aAAa;MACb,MAAM,KAAK,KAAK;QACZ,MAAM;QACN,OAAO;OACV;MACD,UAAU;MACV,UAAU;IACb,CAAA;AAzBG,SAAK,WAAU,mCAAS,YAAW;AACnC,SAAK,SAAS,mCAAS;;;;;;;;EA+B3B,MAAM,SAAqB;AACvB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACnC,WAAK,YAAY,OAAO,EACnB,KAAK,CAAC,aAAY;AACf,gBAAQ,QAAQ;MACpB,CAAC,EACA,MAAM,CAACC,WAAS;AACb,eAAOA,MAAK;MAChB,CAAC;IACT,CAAC;;EAGL,MAAM,YAAY,SAAqB;;AACnC,QAAI,CAAC,QAAQ,IAAI;AACb,YAAM,IAAI,MAAM,iBAAiB;IACpC;AAGD,UAAM,IAAI,QAAQ,GAAG,aAAa,KAAK,EAAE;AAGzC,UAAM,EAAC,UAAU,SAAS,mBAAmB,YAAY,WAAU,IAC/D,MAAM,sBAAsB,SAAS,KAAK,OAAO;AAGrD,UAAM,WAA4B;MAC9B;QACI,MAAM;QACN,OAAO,EAAE,cAAc,EAAC,SAAS,gBAAe,CAAC;QACjD,MAAM;UACF,MAAM,kBAAkB,OAAO,MAAM,OAAO,MAAM;UAClD,OAAO,EAAE,cAAc,EAAC,SAAS,gBAAe,CAAC;UACjD,SAAS;QACZ;MACJ;;AAIL,QAAI,CAAC,cAAa,GAAI;AAClB,eAAS,QAAQ;QACb,MAAM;QACN,MAAM,QAAQ,OAAO,MAAM,OAAO,MAAM;MAC3C,CAAA;IACJ;AAGD,WAAO,SAAS,OAAO,kBAAkB,OAAO,MAAM,OAAO,MAAM;AAGnE,UAAM,kBAAiB,aAAQ,OAAR,mBAAY,OAAO;MACtC,OAAO,EAAE,eAAe,EAAC,SAAS,sBAAqB,CAAC;MACxD,MAAM,EAAE,cAAc;QAClB,SACI;OACP;MACD;IACH;AAED,mBAAe,MAAM,MAAK;AAEtB,cAAQ,KAAK,cAAc;IAC/B,CAAC;AAGD,UAAM,mBAAoC,MAAM,gBAAgB,UAAU,KAAK,QAAQ,CAAC;AACxF,gCAA4B,kBAAkB,OAAO;AAErD,QAAI,CAAC,iBAAiB,OAAO,CAAC,iBAAiB,MAAM,CAAC,iBAAiB,IAAI;AACvE,YAAM,IAAI,MAAM,2BAA2B;IAC9C;AAED,QAAI,iBAAiB,WAAW,iBAAiB,YAAY,iBAAiB,WAAW;AACrF,WAAK,KAAK,aAAa;AACvB,WAAK,KAAK,aAAa;AACvB,WAAK,KAAK,YACN,iBAAiB,YAAY,UAAU,KAAK,iBAAiB,QAAQ;AACzE,WAAK,KAAK,aAAa,iBAAiB;AACxC,WAAK,KAAK,cAAc,iBAAiB;AAEzC,UAAI;AACA,YAAI,iBAAiB,WAAW;AAC5B,gBAAM,WAAW,KAAK,MAAM,iBAAiB,SAAS;AACtD,eAAK,KAAK,aAAa,SAAS;AAChC,eAAK,KAAK,YAAY,SAAS;AAC/B,eAAK,KAAK,aAAa,SAAS;QACnC;MACJ,SAAQ,GAAG;MAEX;IACJ;AAED,UAAM,mBAAmB,MAAM,uBAAuB,YAClD,kBACA,QAAQ,UAAU;AAGtB,UAAM,gBAAgB,iBAAiB,iBAAiB,iBAAiB,GAAG;AAE5E,WAAO;MACH,OAAO,YAAY,KAAK,iBAAiB,GAAG;MAC5C,iBAAiB,gBAAgB,KAAK;QAClC,OAAO,iBAAiB;QACxB,YAAY,iBAAiB;OAChC;MACD;;;;;;;;;;;EAYR,KACI,UACA,SAAwB;AAExB,WAAO,KAAK,qBAAqB,UAAU,OAAO;;EAG9C,MAAM,qBACV,UACA,SAAwB;AAExB,QAAI,CAAC,QAAQ,IAAI;AACb,YAAM,IAAI,MAAM,iBAAiB;IACpC;AAGD,UAAM,IAAI,QAAQ,GAAG,aAAa,KAAK,EAAE;AAGzC,UAAM,aAAa,SAAS,YAAY,WAAW,OAAM;AACzD,UAAM,MAAM,oBAAI,KAAI;AACpB,UAAM,YAAY,KAAK,MAAM,WAAW,QAAO,IAAK,IAAI,QAAO,CAAE;AAGjE,UAAM,kBAAkB,MAAM,QAAQ,cAAc,EAAC,aAAa,SAAS,YAAW,CAAC;AAGvF,oBAAgB,WACZ,QACA,SAAS,KAAK;MACV;IACH,CAAA,CAAC;AAIN,UAAM,WAAW,uBAAuB,iBAAiB,KAAK,OAAO;AAErE,UAAM,UAAU,gBAAgB,OAAO,MAAM,KAAK;AAGlD,UAAM,eAAe,KAAK,KAAK,eAAe;AAG9C,UAAM,oBAAoB,gBAAgB,MAAK;AAC/C,UAAM,YAAY,kBAAiB;AACnC,sBAAkB,WAAW,eAAe,IAAI;AAChD,QAAI,WAAW;AACX,wBAAkB,WAAW,eAAe,SAAS;IACxD;AAED,QAAI,KAAK,KAAK,YAAY;AACtB,UAAI,KAAK,KAAK,WAAW;AACrB,eAAO,SAAS,OAAO,KAAK,KAAK;MACpC,WAAU,gBAAe,GAAI;AAC1B,eAAO,SAAS,OAAO;MAC1B;IACJ;AAED,UAAM,eAAe,MAAK;;AACtB,oBAAQ,OAAR,mBAAY,OAAO;QACf,OAAO,EAAE,gCAAgC,EAAC,SAAS,gBAAe,CAAC;QACnE,MAAM,EAAE,+BAA+B;UACnC,SACI;SACP;QACD,UAAU;UACN;YACI,MAAM;YACN,MAAM,OAAO,OAAO;UACvB;UACD;YACI,MAAM;YACN,OAAO,EAAE,qCAAqC,EAAC,SAAS,cAAa,CAAC;YACtE,MAAM;cACF,MAAM,OAAO,iBAAiB;cAC9B,OAAO,EAAE,qCAAqC,EAAC,SAAS,cAAa,CAAC;YACzE;UACJ;QACJ;MACJ;IACL;AAGA,UAAM,gBAA4C,QAAQ,GAAG,OAAO;MAChE,OAAO,EAAE,kBAAkB,EAAC,SAAS,wBAAuB,CAAC;MAC7D,MAAM,EAAE,iBAAiB;QACrB,aAAa,KAAK,KAAK;QACvB,SAAS,sCAAsC,KAAK,KAAK,WAAW;OACvE;MACD,UAAU;QACN;UACI,MAAM;UACN,MAAM;YACF,OAAO,EAAE,kBAAkB,EAAC,SAAS,mCAAkC,CAAC;YACxE,KAAK,WAAW,YAAW;UAC9B;QACJ;QACD;UACI,MAAM;UACN,OAAO,EAAE,kBAAkB,EAAC,SAAS,uCAAsC,CAAC;UAC5E,MAAM;YACF,SAAS,eACH,MAAO,OAAO,SAAS,OAAO,kBAAkB,OAAM,IACtD;YACN,OAAO,EAAE,kBAAkB;cACvB,SAAS;aACZ;UACJ;QACJ;MACJ;IACJ,CAAA;AAGD,UAAM,QAAQ,WAAW,MAAK;AAC1B,UAAI,CAAC,QAAQ,IAAI;AACb,cAAM,IAAI,MAAM,iBAAiB;MACpC;AACD,oBAAc,OACV,EAAE,iBAAiB,EAAC,SAAS,yCAAwC,CAAC,CAAC;OAE5E,SAAS;AAGZ,kBAAc,MAAM,MAAM,aAAa,KAAK,CAAC;AAG7C,UAAM,kBAAkB,gBAAgB,UAAU,KAAK,QAAQ,CAAC;AAGhE,QAAI,KAAK,KAAK,YAAY;AACtB,YAAM,UAAU,IAAI,IAAI,KAAK,KAAK,UAAU,EAAE;AAC9C,YAAM,UAAU,IAAI,IAAI,KAAK,KAAK,UAAU,EAAE,SAAS,UAAU,CAAC;AAClE,YAAM,gBAAgB,aACjB,KAAK,KAAK,aAAa,oBAAoB,iBAAiB,OACzD,MACA,OACA,MAAM,GAEV,WAAW,KAAK,KAAK,KAAK,UAAU,GACpC,UAAU,KAAK,KAAK,KAAK,SAAS,CAAC;AAGvC,WAAK,WAAW,OAAO,EAAC,QAAQ,cAAa,CAAC,EAAE,OAAO;QACnD;QACA;MACH,CAAA;IACJ,OAAM;AAEH,aAAO,SAAS,OAAO,kBAAkB,OAAM;IAClD;AAGD,UAAM,mBAAmB,MAAM,QAAQ,KAAK,CAAC,iBAAiB,aAAa,CAAC,EAAE,QAC1E,MAAK;AAED,mBAAa,KAAK;IACtB,CAAC;AAGL,UAAM,gBACF,WAAW,gBAAgB,KAC3B,8BAA8B,gBAAgB,EAAE,SAAS;AAE7D,QAAI,eAAe;AAEf,YAAM,kBAAkB,MAAM,uBAAuB,YACjD,kBACA,QAAQ,UAAU;AAItB,aAAO;QACH,YAAY,8BAA8B,gBAAgB;QAC1D,UAAU;;IAEjB;AAED,UAAM,cAAc,EAAE,uBAAuB,EAAC,SAAS,iCAAgC,CAAC;AAExF,kBAAc,OAAO,WAAW;AAGhC,UAAM,IAAI,MAAM,WAAW;;AAElC;", "names": ["EventEmitter", "globalBuoy", "ListenerEncoding", "EventEmitter", "error", "SendResult", "s", "i", "stdlib", "foreign", "buffer", "SealedMessage", "LinkCreate", "LinkInfo", "BuoyMessage", "BuoySession", "BuoyInfo", "uuid", "error"]}