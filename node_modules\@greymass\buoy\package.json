{"name": "@greymass/buoy", "description": "Client for buoy message forwarder", "version": "1.0.4", "homepage": "https://github.com/greymass/buoy-client", "license": "BSD-3-<PERSON><PERSON>", "main": "lib/buoy-client.js", "module": "lib/buoy-client.m.js", "types": "lib/buoy-client.d.ts", "sideEffects": false, "files": ["lib/*", "src/*"], "scripts": {"prepare": "make"}, "dependencies": {"eventemitter3": "^4.0.7", "tslib": "^2.1.0"}, "devDependencies": {"@rollup/plugin-alias": "^3.1.4", "@rollup/plugin-commonjs": "^19.0.1", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.0.2", "@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-typescript": "^8.1.1", "@rollup/plugin-virtual": "^2.0.3", "@types/chai": "^4.2.21", "@types/mocha": "^9.0.0", "@types/node": "^16.4.1", "@types/ws": "^7.4.7", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "chai": "^4.3.4", "cross-fetch": "^3.1.4", "eslint": "^7.19.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.2.0", "gh-pages": "^3.1.0", "isomorphic-ws": "^4.0.1", "mocha": "^9.0.2", "node-fetch": "^2.6.1", "nyc": "^15.1.0", "prettier": "^2.2.1", "rollup": "^2.38.2", "rollup-plugin-dts": "^3.0.1", "ts-node": "^10.1.0", "tsconfig-paths": "^3.10.1", "typedoc": "^0.21.4", "typescript": "^4.1.2", "ws": "^7.5.3"}}