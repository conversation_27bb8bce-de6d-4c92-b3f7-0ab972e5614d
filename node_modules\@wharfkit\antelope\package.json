{"name": "@wharfkit/antelope", "description": "Library for working with Antelope powered blockchains.", "version": "1.0.13", "homepage": "https://github.com/wharfkit/antelope", "license": "BSD-3-Clause-No-Military-License", "main": "lib/antelope.js", "module": "lib/antelope.m.js", "types": "lib/antelope.d.ts", "browser": {"buffer": false, "crypto": false}, "sideEffects": false, "files": ["lib/*", "src/*"], "scripts": {"prepare": "make"}, "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "elliptic": "^6.5.4", "hash.js": "^1.0.0", "pako": "^2.1.0", "tslib": "^2.0.3"}, "devDependencies": {"@rollup/plugin-alias": "^4.0.3", "@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^11.0.0", "@rollup/plugin-virtual": "^3.0.1", "@types/bn.js": "^5.1.0", "@types/elliptic": "^6.4.12", "@types/mocha": "^10.0.0", "@types/node": "^18.6.5", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "chai": "^4.3.4", "eslint": "^8.26.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-es-x": "^6.0.0", "eslint-plugin-prettier": "^4.0.0", "gh-pages": "^5.0.0", "mocha": "^10.1.0", "node-fetch": "^2.6.1", "nyc": "^15.1.0", "prettier": "^2.2.1", "rollup": "^3.17.2", "rollup-plugin-dts": "^5.2.0", "ts-node": "^10.0.0", "tsconfig-paths": "^4.1.0", "typedoc": "^0.23.10", "typescript": "^4.1.2"}}