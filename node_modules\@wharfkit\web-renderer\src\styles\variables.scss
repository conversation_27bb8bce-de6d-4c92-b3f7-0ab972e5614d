/**
Globals and custom properties

To change font size and white space:
--wharfkit-font-base
--wharfkit-space-base

*/

/******************
    Light Theme
******************/

@mixin light-theme {
    color-scheme: light;
    --body-background-color: white;
    --body-text-color: var(--color-primary-700);
    --body-text-color-variant: var(--color-neutral-700);

    --header-background-color: var(--color-primary-700);
    --header-text-color: var(--color-primary-50);
    --header-button-background: var(--header-background-color);
    --header-button-outline: var(--color-primary-900);

    --button-text-color: var(--wharf-blue);
    --button-text-color-active: var(--wharf-blue);
    --button-outline-active: inset 0 0 0 2px var(--button-text-color);

    --button-primary-background: var(--color-secondary-200);
    --button-primary-background-hover: var(--color-secondary-200);
    --button-primary-background-active: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)),
        var(--color-secondary-200);
    --button-primary-outline-hover: inset 0 0 0 1px #00000030,
        inset 0 0 0 1px var(--color-secondary-200);

    --button-secondary-background: var(--color-custom-1);
    --button-secondary-background-hover: var(--color-custom-1);
    --button-secondary-background-active: var(--color-secondary-200);
    --button-secondary-outline-hover: inset 0 0 0 1px #00000020,
        inset 0 0 0 1px var(--color-secondary-200);

    --button-outlined-background-active: var(--color-secondary-200);
    --button-outlined-outline: inset 0 0 0 1px var(--color-secondary-200);
    --button-outlined-outline-hover: inset 0 0 0 1px #00000020,
        inset 0 0 0 1px var(--color-secondary-200);

    --input-placeholder-color: var(--color-neutral-500);
    --input-background-focus: var(--color-neutral-100);
    --input-border-color: #00000030;
    --input-border-color-hover: var(--color-custom-5);
    --input-border-color-focus: var(--wharf-blue);

    --qr-border-color: inset 0 0 0 1px rgba(0 0 0 / 0.3), inset 0 0 0 1px var(--seafoam-mint);

    --checkbox-stroke: var(--wharf-blue);
    --checkbox-fill: var(--reef-turquoise);

    --error-color: var(--color-error-1);

    --list-item-background-color-hover: var(--color-custom-9);
    --list-item-text-color-hover: var(--color-secondary-500);
    --list-divider-color: #00000010;

    --text-area-background: var(--swell-mist);
    --text-area-text-color: #242424;

    --loading-circle-color: var(--reef-turquoise);
    --loading-circle-track-color: rgba(0 0 0 / 0.05);

    --wave-foreground-color: var(--body-background-color);
    --wave-midground-color: var(--seafoam-mint);
    --wave-background-color: var(--reef-turquoise);
}

@mixin dark-theme {
    color-scheme: dark;
    --body-background-color: var(--color-primary-990);
    --body-text-color: white;
    --body-text-color-variant: var(--color-neutral-300);

    --header-background-color: var(--color-primary-700);
    --header-text-color: var(--color-primary-50);
    --header-button-background: var(--header-background-color);
    --header-button-outline: var(--color-primary-900);

    --button-text-color: var(--body-text-color);
    --button-text-color-active: var(--reef-turquoise);
    --button-outline-active: inset 0 0 0 2px var(--reef-turquoise);

    --button-primary-background: var(--color-secondary-500);
    --button-primary-background-hover: var(--color-secondary-500);
    --button-primary-background-active: var(--color-custom-2);
    --button-primary-outline-hover: inset 0 0 0 1px white;

    --button-secondary-background: var(--color-primary-800);
    --button-secondary-background-hover: var(--color-primary-800);
    --button-secondary-background-active: var(--color-primary-900);
    --button-secondary-outline-hover: inset 0 0 0 1px #ffffff30;

    --button-outlined-background-active: var(--color-primary-900);
    --button-outlined-outline: inset 0 0 0 1px #00000030;
    --button-outlined-outline-hover: inset 0 0 0 1px #ffffff30;

    --input-placeholder-color: #ffffff75;
    --input-background-focus: var(--color-custom-4);
    --input-border-color: #ffffff30;
    --input-border-color-hover: var(--color-custom-6);
    --input-border-color-focus: var(--reef-turquoise);

    --qr-border-color: inset 0 0 0 1px rgba(0 0 0 / 0.3);

    --checkbox-stroke: var(--reef-turquoise);
    --checkbox-fill: var(--color-custom-3);

    --error-color: var(--color-error-3);

    --list-item-background-color-hover: var(--color-custom-8);
    --list-item-text-color-hover: var(--color-secondary-400);
    --list-divider-color: #ffffff10;

    --text-area-background: var(--color-primary-900);
    --text-area-text-color: #ffffff;

    --loading-circle-color: var(--seafoam-mint);
    --loading-circle-track-color: rgba(0 0 0 / 0.05);

    --wave-foreground-color: var(--body-background-color);
    --wave-midground-color: #2f3445;
    --wave-background-color: #3e4356;
}

// Default theme, no user or system preference
:host dialog {
    @include light-theme;
}

// No system preference, User prefers dark theme
:host dialog[data-theme='dark'] {
    @include dark-theme;
}

// System dark mode enables dark theme
@media (prefers-color-scheme: dark) {
    :host dialog {
        @include dark-theme;
    }

    // User prefers light theme in system dark mode
    :host dialog[data-theme='light'] {
        @include light-theme;
    }
}

:host dialog {
    /******************
          Colors
    ******************/

    /* Brand */
    --wharf-blue: var(--color-primary-700);
    --reef-turquoise: var(--color-secondary-300);
    --seafoam-mint: var(--color-secondary-200);
    --swell-mist: var(--color-accent-50);

    /* Primary */
    --color-primary-50: #f6f7f9;
    --color-primary-100: #ededf1;
    --color-primary-200: #d6d8e1;
    --color-primary-300: #b3b8c6;
    --color-primary-400: #8991a7;
    --color-primary-500: #6b738c;
    --color-primary-600: #565c73;
    --color-primary-700: #494e62; /* Wharf Blue */
    --color-primary-800: #3c4050;
    --color-primary-900: #363944;
    --color-primary-990: #252835;

    /* Secondary */
    --color-secondary-50: #f1fcf9;
    --color-secondary-100: #cef9ed;
    --color-secondary-200: #b2f2e1; /* Seafoam Mint */
    --color-secondary-300: #7be7ce; /* Reef Turquoise */
    --color-secondary-400: #35ccae;
    --color-secondary-500: #1cb095;
    --color-secondary-600: #148d79;
    --color-secondary-700: #147163;
    --color-secondary-800: #155a51;
    --color-secondary-900: #164b43;

    /* Accent */
    --color-accent-50: #f4faf4; /* Swell Mist */
    --color-accent-100: #e4f4e5;
    --color-accent-200: #cae8cc;
    --color-accent-300: #a1d4a3;
    --color-accent-400: #70b874;
    --color-accent-500: #4c9b51;
    --color-accent-600: #3a7f3e;
    --color-accent-700: #306534;
    --color-accent-800: #2a512d;
    --color-accent-900: #244326;

    /* Neutrals */
    --color-neutral-100: #f7f7f7;
    --color-neutral-200: #e3e3e3;
    --color-neutral-300: #c8c8c8;
    --color-neutral-400: #a4a4a4;
    --color-neutral-500: #818181;
    --color-neutral-600: #666666;
    --color-neutral-700: #515151;
    --color-neutral-800: #434343;
    --color-neutral-900: #383838;

    /* Custom */

    --color-custom-1: #f3f8f3;
    --color-custom-2: #415e60;
    --color-custom-3: #344b4d;
    --color-custom-4: #3d435a;
    --color-custom-5: #8ec2b4;
    --color-custom-6: #777b8b;
    --color-custom-7: #35ccae10;
    --color-custom-8: #575c6e;
    --color-custom-9: #f4f5f7;
    --color-error-1: #cd3939;
    --color-error-2: #ff5454;
    --color-error-3: #ffacac;

    /******************
        Typography
    ******************/
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
        Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --measure: 35ch;

    /* Font Sizing - Fixed */

    --fs-0: calc(var(--fs-2) * 0.75); /* 12px */
    --fs-1: calc(var(--fs-2) * 0.875); /* 14px */
    --fs-2: var(--wharfkit-font-base, 1rem); /* 16px */
    --fs-3: calc(var(--fs-2) * 1.25); /* 20px */

    /* Font Sizing - Fluid */
    /* @link https://utopia.fyi/type/calculator?c=320,12,1.125,1240,16,1.25,3,0,&s=0.75|0.5|0.25,1.5|2|3|4|6,s-l&g=s,l,xl,12 */

    /******************
      Size and Spacing
    ******************/

    /* Spacing Scale - Fixed */
    --ratio: 1.5;
    --space-4xs: calc(var(--space-3xs) / var(--ratio)); /* 2px */
    --space-3xs: calc(var(--space-2xs) / var(--ratio)); /* 3px */
    --space-2xs: calc(var(--space-xs) / var(--ratio)); /* 5px */
    --space-xs: calc(var(--space-s) / var(--ratio)); /* 7px */
    --space-s: calc(var(--space-m) / var(--ratio)); /* 11px */
    --space-m: var(--wharfkit-space-base, 1rem); /* 16px */
    --space-l: calc(var(--space-m) * var(--ratio)); /* 24px */
    --space-xl: calc(var(--space-l) * var(--ratio)); /* 36px */
    --space-2xl: calc(var(--space-xl) * var(--ratio)); /* 54px */
    --space-3xl: calc(var(--space-2xl) * var(--ratio)); /* 81px */
    --space-4xl: calc(var(--space-3xl) * var(--ratio)); /* 121px */
    --space-5xl: calc(var(--space-4xl) * var(--ratio)); /* 182px */
    --space-6xl: calc(var(--space-5xl) * var(--ratio)); /* 273px */
    --space-7xl: calc(var(--space-6xl) * var(--ratio)); /* 409px */
    --space-8xl: calc(var(--space-7xl) * var(--ratio)); /* 614px */
    --space-9xl: calc(var(--space-8xl) * var(--ratio)); /* 921px */

    /* Spacing Scale - Fluid */
    /* @link https://utopia.fyi/space/calculator?c=320,12,1.2,1240,16,1.25,3,0,&s=0.75|0.5|0.25,1.5|2|3|4|6|10|16|25|30|40,s-l&g=s,l,xl,12 */

    /* Other sizes */
    --border-radius-outer: 1.5rem; /* 24px */
    --border-radius-inner: 0.75rem; /* 12px */
    --header-height: var(--space-3xl);
}

* {
    box-sizing: border-box;
}

::selection {
    color: var(--wharf-blue);
    background-color: var(--seafoam-mint);
}
