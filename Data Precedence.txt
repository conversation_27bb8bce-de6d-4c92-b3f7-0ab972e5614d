Data Precedence
Assets have both an immutable and a mutable data field. These field can define the same attributes multiple times, in which case immutable data > mutable data.

Asset can also reference a template. Templates only have immutable data. If the template and the asset define the same attribute multiple times, it's template data > asset data

So overall, it's immutable template > immutable asset > mutable asset.