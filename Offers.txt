Offers
AtomicAssets offers native two sided trade offers, just like users are already used to them from platforms like Steam. This means that an account can send a trade offer to any other account, offering to trade some of his assets for some of the other accounts asset. If the recipient of the offer accepts it, the assets are automatically transferred to the respective other party.

Offers only need to include a single asset, no matter which side. That means that leaving one side empty (gifts) are possible. This can also be used by smart contracts like decentralized exchanges. If a user for example wanted to put up one of his assets on the market for a certain price, the exchange could have him sent a trade offer with this asset and only accept it if a buyer for the asset is found. Therefore, users can keep ownership over their asset until it is sold.

How to use offers
To create a new offer, use the createoffer action
To cancel an offer you previously created, use the canceloffer action
To accept an offer, use the acceptoffer action
To decline an offer you received, use the declineoffer action
Where are offers stored
Offers are stored in the offers table. On top of the primary ID index, they are also indexed by the senders and the recipients, making it very easy to find the relevant offers for an account.

Note: An offer's validity is checked when it is created, but it is not automatically deleted when it becomes invalid (because one of the sides loses ownership of at least one of the items included in the offer).