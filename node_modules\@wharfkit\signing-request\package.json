{"name": "@wharfkit/signing-request", "version": "3.2.0", "description": "Signing Request (ESR / EEP-7) encoder and decoder for Antelope blockchains", "homepage": "https://github.com/greymass/eosio-signing-request", "license": "MIT", "main": "lib/signing-request.js", "module": "lib/signing-request.m.js", "types": "lib/signing-request.d.ts", "sideEffects": false, "files": ["src/*", "lib/*"], "scripts": {"prepare": "make"}, "dependencies": {"@wharfkit/antelope": "^1.0.7", "tslib": "^2.0.3"}, "devDependencies": {"@rollup/plugin-typescript": "^8.1.1", "@types/bn.js": "^5.1.0", "@types/mocha": "^9.0.0", "@types/node": "^16.9.6", "@typescript-eslint/eslint-plugin": "^5.22.0", "@typescript-eslint/parser": "^5.22.0", "eslint": "^8.14.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0", "mocha": "^9.1.1", "node-fetch": "^3.0.0", "nyc": "^15.1.0", "prettier": "^2.2.1", "rollup": "^2.38.1", "rollup-plugin-dts": "^4.0.0", "ts-node": "^10.0.0", "typescript": "^4.3.2"}}