/**
 * @wharfkit/wallet-plugin-anchor v1.5.0
 * https://github.com/wharfkit/wallet-plugin-anchor
 *
 * @license
 * Copyright (c) 2023 Greymass Inc. All Rights Reserved.
 * 
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 
 * 1.  Redistribution of source code must retain the above copyright notice, this
 *     list of conditions and the following disclaimer.
 * 
 * 2.  Redistribution in binary form must reproduce the above copyright notice,
 *     this list of conditions and the following disclaimer in the documentation
 *     and/or other materials provided with the distribution.
 * 
 * 3.  Neither the name of the copyright holder nor the names of its contributors
 *     may be used to endorse or promote products derived from this software without
 *     specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
 * OF THE POSSIBILITY OF SUCH DAMAGE.
 * 
 * YOU ACKNOWLEDGE THAT THIS SOFTWARE IS NOT DESIGNED, LICENSED OR INTENDED FOR USE
 * IN THE DESIGN, CONSTRUCTION, OPERATION OR MAINTENANCE OF ANY MILITARY FACILITY.
 */
import { AbstractWalletPlugin, Checksum256, PermissionLevel, PublicKey, PrivateKey, WalletPluginConfig, WalletPluginMetadata, LoginContext, WalletPluginLoginResponse, ResolvedSigningRequest, TransactContext, WalletPluginSignResponse } from '@wharfkit/session';
import WebSocket from 'isomorphic-ws';

interface WalletPluginOptions {
    buoyUrl?: string;
    buoyWs?: WebSocket;
}
declare class WalletPluginAnchor extends AbstractWalletPlugin {
    chain: Checksum256 | undefined;
    auth: PermissionLevel | undefined;
    requestKey: PublicKey | undefined;
    privateKey: PrivateKey | undefined;
    signerKey: PublicKey | undefined;
    channelUrl: string | undefined;
    channelName: string | undefined;
    buoyUrl: string;
    buoyWs: WebSocket | undefined;
    /**
     * The unique identifier for the wallet plugin.
     */
    id: string;
    /**
     * The translations for this plugin
     */
    translations: {
        en: {
            login: {
                title: string;
                body: string;
                link: string;
            };
            transact: {
                title: string;
                body: string;
                label: string;
                link: string;
                await: string;
            };
            error: {
                expired: string;
                invalid_response: string;
                not_completed: string;
                cancelled: string;
            };
        };
        ko: {};
        'zh-Hans': {
            login: {
                link: string;
                body: string;
                title: string;
            };
            error: {
                cancelled: string;
                not_completed: string;
                invalid_response: string;
                expired: string;
            };
            transact: {
                await: string;
                link: string;
                label: string;
                body: string;
                title: string;
            };
        };
        'zh-Hant': {};
    };
    constructor(options?: WalletPluginOptions);
    /**
     * The logic configuration for the wallet plugin.
     */
    readonly config: WalletPluginConfig;
    /**
     * The metadata for the wallet plugin to be displayed in the user interface.
     */
    readonly metadata: WalletPluginMetadata;
    /**
     * Performs the wallet logic required to login and return the chain and permission level to use.
     *
     * @param options WalletPluginLoginOptions
     * @returns Promise<WalletPluginLoginResponse>
     */
    login(context: LoginContext): Promise<WalletPluginLoginResponse>;
    handleLogin(context: LoginContext): Promise<WalletPluginLoginResponse>;
    /**
     * Performs the wallet logic required to sign a transaction and return the signature.
     *
     * @param chain ChainDefinition
     * @param resolved ResolvedSigningRequest
     * @returns Promise<Signature>
     */
    sign(resolved: ResolvedSigningRequest, context: TransactContext): Promise<WalletPluginSignResponse>;
    private handleSigningRequest;
}

export { WalletPluginAnchor };
