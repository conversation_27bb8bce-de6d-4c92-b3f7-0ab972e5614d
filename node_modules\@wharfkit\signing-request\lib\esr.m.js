/**
 * EOSIO Signing Request v2.5.3
 * https://github.com/greymass/eosio-signing-request
 *
 * @license
 * Copyright © 2021 Greymass Inc.
 * 
 * Permission is hereby granted, free of charge, to any person
 * obtaining a copy of this software and associated documentation
 * files (the “Software”), to deal in the Software without
 * restriction, including without limitation the rights to use,
 * copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following
 * conditions:
 * 
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 * OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 * WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 * 
 * YOU ACKNOWLEDGE THAT THIS SOFTWARE IS NOT DESIGNED, LICENSED OR
 * INTENDED FOR USE IN THE DESIGN, CONSTRUCTION, OPERATION OR
 * MAINTENANCE OF ANY MILITARY FACILITY.
 */
import { TypeAlias, Variant, Checksum256, isInstanceOf, UInt8, Struct, PermissionLevel, Action, Transaction, Name, TimePointSec, Signature, Serializer, Authority, Bytes, ABIEncoder, ABIDecoder, ABI, UInt16, UInt32 } from '@wharfkit/antelope';
import { __decorate } from 'tslib';

/**
 * Base64u - URL-Safe Base64 variant no padding.
 * Based on https://gist.github.com/jonleighton/958841
 */
const baseCharset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
const lookup = new Uint8Array(256);
for (let i = 0; i < 62; i++) {
    lookup[baseCharset.charCodeAt(i)] = i;
}
// support both urlsafe and standard base64
lookup[43] = lookup[45] = 62;
lookup[47] = lookup[95] = 63;
function encode(data, urlSafe = true) {
    const byteLength = data.byteLength;
    const byteRemainder = byteLength % 3;
    const mainLength = byteLength - byteRemainder;
    const charset = baseCharset + (urlSafe ? '-_' : '+/');
    const parts = [];
    let a;
    let b;
    let c;
    let d;
    let chunk;
    // Main loop deals with bytes in chunks of 3
    for (let i = 0; i < mainLength; i += 3) {
        // Combine the three bytes into a single integer
        chunk = (data[i] << 16) | (data[i + 1] << 8) | data[i + 2];
        // Use bitmasks to extract 6-bit segments from the triplet
        a = (chunk & 16515072) >> 18; // 16515072 = (2^6 - 1) << 18
        b = (chunk & 258048) >> 12; // 258048   = (2^6 - 1) << 12
        c = (chunk & 4032) >> 6; // 4032     = (2^6 - 1) << 6
        d = chunk & 63; // 63       =  2^6 - 1
        // Convert the raw binary segments to the appropriate ASCII encoding
        parts.push(charset[a] + charset[b] + charset[c] + charset[d]);
    }
    // Deal with the remaining bytes
    if (byteRemainder === 1) {
        chunk = data[mainLength];
        a = (chunk & 252) >> 2; // 252 = (2^6 - 1) << 2
        // Set the 4 least significant bits to zero
        b = (chunk & 3) << 4; // 3   = 2^2 - 1
        parts.push(charset[a] + charset[b]);
    }
    else if (byteRemainder === 2) {
        chunk = (data[mainLength] << 8) | data[mainLength + 1];
        a = (chunk & 64512) >> 10; // 64512 = (2^6 - 1) << 10
        b = (chunk & 1008) >> 4; // 1008  = (2^6 - 1) << 4
        // Set the 2 least significant bits to zero
        c = (chunk & 15) << 2; // 15    = 2^4 - 1
        parts.push(charset[a] + charset[b] + charset[c]);
    }
    return parts.join('');
}
function decode(input) {
    const byteLength = input.length * 0.75;
    const data = new Uint8Array(byteLength);
    let a;
    let b;
    let c;
    let d;
    let p = 0;
    for (let i = 0; i < input.length; i += 4) {
        a = lookup[input.charCodeAt(i)];
        b = lookup[input.charCodeAt(i + 1)];
        c = lookup[input.charCodeAt(i + 2)];
        d = lookup[input.charCodeAt(i + 3)];
        data[p++] = (a << 2) | (b >> 4);
        data[p++] = ((b & 15) << 4) | (c >> 2);
        data[p++] = ((c & 3) << 6) | (d & 63);
    }
    return data;
}

var base64u = /*#__PURE__*/Object.freeze({
    __proto__: null,
    encode: encode,
    decode: decode
});

/** Chain ID aliases. */
var ChainName;
(function (ChainName) {
    ChainName[ChainName["UNKNOWN"] = 0] = "UNKNOWN";
    ChainName[ChainName["EOS"] = 1] = "EOS";
    ChainName[ChainName["TELOS"] = 2] = "TELOS";
    ChainName[ChainName["JUNGLE"] = 3] = "JUNGLE";
    ChainName[ChainName["KYLIN"] = 4] = "KYLIN";
    ChainName[ChainName["WORBLI"] = 5] = "WORBLI";
    ChainName[ChainName["BOS"] = 6] = "BOS";
    ChainName[ChainName["MEETONE"] = 7] = "MEETONE";
    ChainName[ChainName["INSIGHTS"] = 8] = "INSIGHTS";
    ChainName[ChainName["BEOS"] = 9] = "BEOS";
    ChainName[ChainName["WAX"] = 10] = "WAX";
    ChainName[ChainName["PROTON"] = 11] = "PROTON";
    ChainName[ChainName["FIO"] = 12] = "FIO";
})(ChainName || (ChainName = {}));
let ChainId = class ChainId extends Checksum256 {
    static from(value) {
        if (isInstanceOf(value, this)) {
            return value;
        }
        if (typeof value === 'number') {
            value = ChainIdLookup.get(value);
            if (!value) {
                throw new Error('Unknown chain id alias');
            }
        }
        return super.from(value);
    }
    get chainVariant() {
        const name = this.chainName;
        if (name !== ChainName.UNKNOWN) {
            return ChainIdVariant.from(['chain_alias', name]);
        }
        return ChainIdVariant.from(this);
    }
    get chainName() {
        const cid = this.hexString;
        for (const [n, id] of ChainIdLookup) {
            if (id === cid) {
                return n;
            }
        }
        return ChainName.UNKNOWN;
    }
};
ChainId = __decorate([
    TypeAlias('chain_id')
], ChainId);
let ChainAlias = class ChainAlias extends UInt8 {
};
ChainAlias = __decorate([
    TypeAlias('chain_alias')
], ChainAlias);
let ChainIdVariant = class ChainIdVariant extends Variant {
    get chainId() {
        if (isInstanceOf(this.value, ChainId)) {
            return this.value;
        }
        return ChainId.from(Number(this.value.value));
    }
};
ChainIdVariant = __decorate([
    Variant.type('variant_id', [ChainAlias, ChainId])
], ChainIdVariant);
const ChainIdLookup = new Map([
    [ChainName.EOS, 'aca376f206b8fc25a6ed44dbdc66547c36c6c33e3a119ffbeaef943642f0e906'],
    [ChainName.TELOS, '4667b205c6838ef70ff7988f6e8257e8be0e1284a2f59699054a018f743b1d11'],
    [ChainName.JUNGLE, 'e70aaab8997e1dfce58fbfac80cbbb8fecec7b99cf982a9444273cbc64c41473'],
    [ChainName.KYLIN, '5fff1dae8dc8e2fc4d5b23b2c7665c97f9e9d8edf2b6485a86ba311c25639191'],
    [ChainName.WORBLI, '73647cde120091e0a4b85bced2f3cfdb3041e266cbbe95cee59b73235a1b3b6f'],
    [ChainName.BOS, 'd5a3d18fbb3c084e3b1f3fa98c21014b5f3db536cc15d08f9f6479517c6a3d86'],
    [ChainName.MEETONE, 'cfe6486a83bad4962f232d48003b1824ab5665c36778141034d75e57b956e422'],
    [ChainName.INSIGHTS, 'b042025541e25a472bffde2d62edd457b7e70cee943412b1ea0f044f88591664'],
    [ChainName.BEOS, 'b912d19a6abd2b1b05611ae5be473355d64d95aeff0c09bedc8c166cd6468fe4'],
    [ChainName.WAX, '1064487b3cd1a897ce03ae5b6a865651747e2e152090f99c1d19d44e01aea5a4'],
    [ChainName.PROTON, '384da888112027f0321850a169f737c33e53b388aad48b5adace4bab97f437e0'],
    [ChainName.FIO, '21dcae42c0182200e93f954a074011f9048a7624c6fe81d3c9541a614a88bd1c'],
]);

/** SigningRequest ABI and typedefs. */
var RequestFlags_1;
let AccountName = class AccountName extends Name {
};
AccountName = __decorate([
    TypeAlias('account_name')
], AccountName);
let PermissionName = class PermissionName extends Name {
};
PermissionName = __decorate([
    TypeAlias('permission_name')
], PermissionName);
let IdentityV2 = class IdentityV2 extends Struct {
};
__decorate([
    Struct.field(PermissionLevel, { optional: true })
], IdentityV2.prototype, "permission", void 0);
IdentityV2 = __decorate([
    Struct.type('identity')
], IdentityV2);
let IdentityV3 = class IdentityV3 extends Struct {
};
__decorate([
    Struct.field('name')
], IdentityV3.prototype, "scope", void 0);
__decorate([
    Struct.field(PermissionLevel, { optional: true })
], IdentityV3.prototype, "permission", void 0);
IdentityV3 = __decorate([
    Struct.type('identity')
], IdentityV3);
let RequestVariantV2 = class RequestVariantV2 extends Variant {
};
RequestVariantV2 = __decorate([
    Variant.type('variant_req', [Action, { type: Action, array: true }, Transaction, IdentityV2])
], RequestVariantV2);
let RequestVariantV3 = class RequestVariantV3 extends Variant {
};
RequestVariantV3 = __decorate([
    Variant.type('variant_req', [Action, { type: Action, array: true }, Transaction, IdentityV3])
], RequestVariantV3);
let RequestFlags = RequestFlags_1 = class RequestFlags extends UInt8 {
    get broadcast() {
        return (Number(this) & RequestFlags_1.broadcast) !== 0;
    }
    set broadcast(enabled) {
        this.setFlag(RequestFlags_1.broadcast, enabled);
    }
    get background() {
        return (Number(this) & RequestFlags_1.background) !== 0;
    }
    set background(enabled) {
        this.setFlag(RequestFlags_1.background, enabled);
    }
    setFlag(flag, enabled) {
        if (enabled) {
            // TODO: implement bitwise operators in core, bn.js setbit does not work
            this.value = UInt8.from(Number(this) | flag).value;
        }
        else {
            this.value = UInt8.from(Number(this) & ~flag).value;
        }
    }
};
RequestFlags.broadcast = 1 << 0;
RequestFlags.background = 1 << 1;
RequestFlags = RequestFlags_1 = __decorate([
    TypeAlias('request_flags')
], RequestFlags);
let InfoPair = class InfoPair extends Struct {
};
__decorate([
    Struct.field('string')
], InfoPair.prototype, "key", void 0);
__decorate([
    Struct.field('bytes')
], InfoPair.prototype, "value", void 0);
InfoPair = __decorate([
    Struct.type('info_pair')
], InfoPair);
let RequestDataV2 = class RequestDataV2 extends Struct {
};
__decorate([
    Struct.field(ChainIdVariant)
], RequestDataV2.prototype, "chain_id", void 0);
__decorate([
    Struct.field(RequestVariantV2)
], RequestDataV2.prototype, "req", void 0);
__decorate([
    Struct.field(RequestFlags)
], RequestDataV2.prototype, "flags", void 0);
__decorate([
    Struct.field('string')
], RequestDataV2.prototype, "callback", void 0);
__decorate([
    Struct.field(InfoPair, { array: true })
], RequestDataV2.prototype, "info", void 0);
RequestDataV2 = __decorate([
    Struct.type('signing_request')
], RequestDataV2);
let RequestDataV3 = class RequestDataV3 extends Struct {
};
__decorate([
    Struct.field(ChainIdVariant)
], RequestDataV3.prototype, "chain_id", void 0);
__decorate([
    Struct.field(RequestVariantV3)
], RequestDataV3.prototype, "req", void 0);
__decorate([
    Struct.field(RequestFlags)
], RequestDataV3.prototype, "flags", void 0);
__decorate([
    Struct.field('string')
], RequestDataV3.prototype, "callback", void 0);
__decorate([
    Struct.field(InfoPair, { array: true })
], RequestDataV3.prototype, "info", void 0);
RequestDataV3 = __decorate([
    Struct.type('signing_request')
], RequestDataV3);
let RequestSignature = class RequestSignature extends Struct {
};
__decorate([
    Struct.field('name')
], RequestSignature.prototype, "signer", void 0);
__decorate([
    Struct.field('signature')
], RequestSignature.prototype, "signature", void 0);
RequestSignature = __decorate([
    Struct.type('request_signature')
], RequestSignature);

var IdentityProof_1;
let IdentityProof = IdentityProof_1 = class IdentityProof extends Struct {
    static from(value) {
        if (isInstanceOf(value, IdentityProof_1)) {
            return value;
        }
        else if (typeof value === 'string') {
            return IdentityProof_1.fromString(value);
        }
        else {
            return super.from(value);
        }
    }
    /**
     * Create a new instance from an EOSIO authorization header string.
     * "EOSIO <base64payload>"
     */
    static fromString(string) {
        const parts = string.split(' ');
        if (parts.length !== 2 || parts[0] !== 'EOSIO') {
            throw new Error('Invalid IdentityProof string');
        }
        const data = decode(parts[1]);
        return Serializer.decode({ data, type: IdentityProof_1 });
    }
    /** Create a new instance from a callback payload. */
    static fromPayload(payload, options = {}) {
        const request = SigningRequest.from(payload.req, options);
        if (!(request.version >= 3 && request.isIdentity())) {
            throw new Error('Not an identity request');
        }
        return this.from({
            chainId: payload.cid || request.getChainId(),
            scope: request.getIdentityScope(),
            expiration: payload.ex,
            signer: { actor: payload.sa, permission: payload.sp },
            signature: payload.sig,
        });
    }
    /**
     * Transaction this proof resolves to.
     * @internal
     */
    get transaction() {
        const action = Action.from({
            account: '',
            name: 'identity',
            authorization: [this.signer],
            data: IdentityV3.from({ scope: this.scope, permission: this.signer }),
        });
        return Transaction.from({
            ref_block_num: 0,
            ref_block_prefix: 0,
            expiration: this.expiration,
            actions: [action],
        });
    }
    /**
     * Recover the public key that signed this proof.
     */
    recover() {
        return this.signature.recoverDigest(this.transaction.signingDigest(this.chainId));
    }
    /**
     * Verify that given authority signed this proof.
     * @param auth The accounts signing authority.
     * @param currentTime Time to verify expiry against, if unset will use system time.
     */
    verify(auth, currentTime) {
        const now = TimePointSec.from(currentTime || new Date()).toMilliseconds();
        return (now < this.expiration.toMilliseconds() &&
            Authority.from(auth).hasPermission(this.recover()));
    }
    /**
     * Encode the proof to an `EOSIO` auth header string.
     */
    toString() {
        const data = Serializer.encode({ object: this });
        return `EOSIO ${encode(data.array, false)}`;
    }
};
__decorate([
    Struct.field(ChainId)
], IdentityProof.prototype, "chainId", void 0);
__decorate([
    Struct.field(Name)
], IdentityProof.prototype, "scope", void 0);
__decorate([
    Struct.field(TimePointSec)
], IdentityProof.prototype, "expiration", void 0);
__decorate([
    Struct.field(PermissionLevel)
], IdentityProof.prototype, "signer", void 0);
__decorate([
    Struct.field(Signature)
], IdentityProof.prototype, "signature", void 0);
IdentityProof = IdentityProof_1 = __decorate([
    Struct.type('identity_proof')
], IdentityProof);

/**
 * EOSIO Signing Request (ESR).
 */
/** Current supported protocol version, backwards compatible with version 2. */
const ProtocolVersion = 3;
/**
 * The placeholder name: `............1` aka `uint64(1)`.
 * If used in action data will be resolved to current signer.
 * If used in as an authorization permission will be resolved to
 * the signers permission level.
 *
 * Example action:
 * ```
 * { account: "eosio.token",
 *   name: "transfer",
 *   authorization: [{actor: "............1", permission: "............1"}],
 *   data: {
 *     from: "............1",
 *     to: "bar",
 *     quantity: "42.0000 EOS",
 *     memo: "Don't panic" }}
 * ```
 * When signed by `foo@active` would resolve to:
 * ```
 * { account: "eosio.token",
 *   name: "transfer",
 *   authorization: [{actor: "foo", permission: "active"}],
 *   data: {
 *     from: "foo",
 *     to: "bar",
 *     quantity: "42.0000 EOS",
 *     memo: "Don't panic" }}
 * ```
 */
const PlaceholderName = Name.from('............1'); // aka uint64(1)
/** Placeholder that will resolve to signer permission name. */
const PlaceholderPermission = Name.from('............2'); // aka uint64(2)
const PlaceholderAuth = PermissionLevel.from({
    actor: PlaceholderName,
    permission: PlaceholderPermission,
});
class SigningRequest {
    /**
     * Create a new signing request.
     * Normally not used directly, see the `create` and `from` class methods.
     */
    constructor(version, data, zlib, abiProvider, signature) {
        if (data.flags.broadcast && data.req.variantName === 'identity') {
            throw new Error('Invalid request (identity request cannot be broadcast)');
        }
        this.version = version;
        this.data = data;
        this.zlib = zlib;
        this.abiProvider = abiProvider;
        this.signature = signature;
    }
    /** Return the identity ABI for given version. */
    static identityAbi(version) {
        const abi = Serializer.synthesize(this.identityType(version));
        abi.actions = [{ name: 'identity', type: 'identity', ricardian_contract: '' }];
        return abi;
    }
    /** Return the ABISerializableType identity type for given version. */
    static identityType(version) {
        return version === 2 ? IdentityV2 : IdentityV3;
    }
    /** Return the ABISerializableType storage type for given version. */
    static storageType(version) {
        return version === 2 ? RequestDataV2 : RequestDataV3;
    }
    /** Create a new signing request. */
    static async create(args, options = {}) {
        let actions;
        if (args.action) {
            actions = [args.action];
        }
        else if (args.actions) {
            actions = args.actions;
        }
        else if (args.transaction) {
            actions = args.transaction.actions || [];
        }
        else {
            actions = [];
        }
        const requiredAbis = actions
            .filter((action) => !Bytes.isBytes(action.data) &&
            action.data.constructor.abiName === undefined)
            .map((action) => Name.from(action.account));
        const abis = {};
        if (requiredAbis.length > 0) {
            const provider = options.abiProvider;
            if (!provider) {
                throw new Error('Missing abi provider');
            }
            const accountAbis = await Promise.all(requiredAbis.map((account) => provider.getAbi(account)));
            for (const [idx, abi] of accountAbis.entries()) {
                abis[requiredAbis[idx].toString()] = abi;
            }
        }
        return this.createSync(args, options, abis);
    }
    /**
     * Synchronously create a new signing request.
     * @throws If an un-encoded action with no abi def is encountered.
     */
    static createSync(args, options = {}, abis = {}) {
        let version = 2;
        const data = {};
        const encode = (action) => encodeAction(action, abis);
        // multi-chain requests requires version 3
        if (args.chainId === null) {
            version = 3;
        }
        // set the request data
        if (args.identity !== undefined) {
            if (args.identity.scope) {
                version = 3;
            }
            data.req = ['identity', this.identityType(version).from(args.identity)];
        }
        else if (args.action && !args.actions && !args.transaction) {
            data.req = ['action', encode(args.action)];
        }
        else if (args.actions && !args.action && !args.transaction) {
            if (args.actions.length === 1) {
                data.req = ['action', encode(args.actions[0])];
            }
            else {
                data.req = ['action[]', args.actions.map(encode)];
            }
        }
        else if (args.transaction && !args.action && !args.actions) {
            const tx = args.transaction;
            // set default values if missing
            if (tx.expiration === undefined) {
                tx.expiration = '1970-01-01T00:00:00.000';
            }
            if (tx.ref_block_num === undefined) {
                tx.ref_block_num = 0;
            }
            if (tx.ref_block_prefix === undefined) {
                tx.ref_block_prefix = 0;
            }
            if (tx.context_free_actions === undefined) {
                tx.context_free_actions = [];
            }
            if (tx.transaction_extensions === undefined) {
                tx.transaction_extensions = [];
            }
            if (tx.delay_sec === undefined) {
                tx.delay_sec = 0;
            }
            if (tx.max_cpu_usage_ms === undefined) {
                tx.max_cpu_usage_ms = 0;
            }
            if (tx.max_net_usage_words === undefined) {
                tx.max_net_usage_words = 0;
            }
            if (tx.actions === undefined) {
                tx.actions = [];
            }
            if (tx.context_free_actions === undefined) {
                tx.context_free_actions = [];
            }
            // encode actions if needed
            tx.actions = tx.actions.map(encode);
            data.req = ['transaction', tx];
        }
        else {
            throw new TypeError('Invalid arguments: Must have exactly one of action, actions or transaction');
        }
        // set the chain id
        if (args.chainId === null) {
            data.chain_id = ChainIdVariant.from(['chain_alias', 0]);
        }
        else {
            data.chain_id = ChainId.from(args.chainId || ChainName.EOS).chainVariant;
        }
        // request flags and callback
        const flags = RequestFlags.from(0);
        let callback = '';
        flags.broadcast = args.broadcast !== undefined ? args.broadcast : data.req[0] !== 'identity';
        if (typeof args.callback === 'string') {
            callback = args.callback;
        }
        else if (typeof args.callback === 'object') {
            callback = args.callback.url;
            flags.background = args.callback.background || false;
        }
        data.flags = flags;
        data.callback = callback;
        // info pairs
        data.info = [];
        if (typeof args.info === 'object') {
            for (const key in args.info) {
                const isOwn = Object.prototype.hasOwnProperty.call(args.info, key);
                if (isOwn) {
                    let value = args.info[key];
                    if (typeof value === 'string') {
                        value = Bytes.from(value, 'utf8');
                    }
                    else if (!(value instanceof Bytes)) {
                        value = Serializer.encode({ object: value });
                    }
                    data.info.push({ key, value });
                }
            }
        }
        if (args.chainIds && args.chainId === null) {
            const ids = args.chainIds.map((id) => ChainId.from(id).chainVariant);
            data.info.push({
                key: 'chain_ids',
                value: Serializer.encode({ object: ids, type: { type: ChainIdVariant, array: true } }),
            });
        }
        const req = new SigningRequest(version, this.storageType(version).from(data), options.zlib, options.abiProvider);
        // sign the request if given a signature provider
        if (options.signatureProvider) {
            req.sign(options.signatureProvider);
        }
        return req;
    }
    /** Creates an identity request. */
    static identity(args, options = {}) {
        let permission = {
            actor: args.account || PlaceholderName,
            permission: args.permission || PlaceholderPermission,
        };
        if (permission.actor === PlaceholderName &&
            permission.permission === PlaceholderPermission) {
            permission = undefined;
        }
        return this.createSync({
            ...args,
            identity: {
                permission,
                scope: args.scope,
            },
            broadcast: false,
        }, options);
    }
    /**
     * Create a request from a chain id and serialized transaction.
     * @param chainId The chain id where the transaction is valid.
     * @param serializedTransaction The serialized transaction.
     * @param options Creation options.
     */
    static fromTransaction(chainId, serializedTransaction, options = {}) {
        const id = ChainId.from(chainId);
        serializedTransaction = Bytes.from(serializedTransaction);
        const encoder = new ABIEncoder();
        encoder.writeByte(2); // header
        encoder.writeArray(Serializer.encode({ object: id.chainVariant }).array);
        encoder.writeByte(2); // transaction variant
        encoder.writeArray(Bytes.from(serializedTransaction).array);
        encoder.writeByte(RequestFlags.broadcast);
        encoder.writeByte(0); // callback
        encoder.writeByte(0); // info
        return SigningRequest.fromData(encoder.getData(), options);
    }
    /** Creates a signing request from encoded `esr:` uri string. */
    static from(uri, options = {}) {
        if (typeof uri !== 'string') {
            throw new Error('Invalid request uri');
        }
        const [, path] = uri.split(':');
        const data = decode(path.startsWith('//') ? path.slice(2) : path);
        return SigningRequest.fromData(data, options);
    }
    static fromData(data, options = {}) {
        data = Bytes.from(data);
        const header = data.array[0];
        const version = header & ~(1 << 7);
        if (version !== 2 && version !== 3) {
            throw new Error('Unsupported protocol version');
        }
        let payload = data.droppingFirst(1);
        if ((header & (1 << 7)) !== 0) {
            if (!options.zlib) {
                throw new Error('Compressed URI needs zlib');
            }
            payload = Bytes.from(options.zlib.inflateRaw(payload.array));
        }
        const decoder = new ABIDecoder(payload.array);
        const req = Serializer.decode({ data: decoder, type: this.storageType(version) });
        let sig;
        if (decoder.canRead()) {
            sig = Serializer.decode({ data: decoder, type: RequestSignature });
        }
        return new SigningRequest(version, req, options.zlib, options.abiProvider, sig);
    }
    /**
     * Sign the request, mutating.
     * @param signatureProvider The signature provider that provides a signature for the signer.
     */
    sign(signatureProvider) {
        const message = this.getSignatureDigest();
        this.signature = RequestSignature.from(signatureProvider.sign(message));
    }
    /**
     * Get the signature digest for this request.
     */
    getSignatureDigest() {
        // protocol version + utf8 "request"
        const prefix = [this.version, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74];
        return Checksum256.hash(Bytes.from(prefix).appending(this.getData()));
    }
    /**
     * Set the signature data for this request, mutating.
     * @param signer Account name of signer.
     * @param signature The signature string.
     */
    setSignature(signer, signature) {
        this.signature = RequestSignature.from({ signer, signature });
    }
    /**
     * Set the request callback, mutating.
     * @param url Where the callback should be sent.
     * @param background Whether the callback should be sent in the background.
     */
    setCallback(url, background) {
        this.data.callback = url;
        this.data.flags.background = background;
    }
    /**
     * Set broadcast flag.
     * @param broadcast Whether the transaction should be broadcast by receiver.
     */
    setBroadcast(broadcast) {
        this.data.flags.broadcast = broadcast;
    }
    /**
     * Encode this request into an `esr:` uri.
     * @argument compress Whether to compress the request data using zlib,
     *                    defaults to true if omitted and zlib is present;
     *                    otherwise false.
     * @argument slashes Whether add slashes after the protocol scheme, i.e. `esr://`.
     *                   Defaults to true.
     * @returns An esr uri string.
     */
    encode(compress, slashes, scheme = 'esr:') {
        const shouldCompress = compress !== undefined ? compress : this.zlib !== undefined;
        if (shouldCompress && this.zlib === undefined) {
            throw new Error('Need zlib to compress');
        }
        let header = this.version;
        const data = this.getData();
        const sigData = this.getSignatureData();
        let array = new Uint8Array(data.byteLength + sigData.byteLength);
        array.set(data, 0);
        array.set(sigData, data.byteLength);
        if (shouldCompress) {
            const deflated = this.zlib.deflateRaw(array);
            if (array.byteLength > deflated.byteLength) {
                header |= 1 << 7;
                array = deflated;
            }
        }
        const out = new Uint8Array(1 + array.byteLength);
        out[0] = header;
        out.set(array, 1);
        if (slashes !== false) {
            scheme += '//';
        }
        return scheme + encode(out);
    }
    /** Get the request data without header or signature. */
    getData() {
        return Serializer.encode({ object: this.data }).array;
    }
    /** Get signature data, returns an empty array if request is not signed. */
    getSignatureData() {
        if (!this.signature) {
            return new Uint8Array(0);
        }
        return Serializer.encode({ object: this.signature }).array;
    }
    /** ABI definitions required to resolve request. */
    getRequiredAbis() {
        return this.getRawActions()
            .filter((action) => !isIdentity(action))
            .map((action) => action.account)
            .filter((value, index, self) => self.indexOf(value) === index);
    }
    /** Whether TaPoS values are required to resolve request. */
    requiresTapos() {
        const tx = this.getRawTransaction();
        return !this.isIdentity() && !hasTapos(tx);
    }
    /** Resolve required ABI definitions. */
    async fetchAbis(abiProvider) {
        const required = this.getRequiredAbis();
        if (required.length > 0) {
            const provider = abiProvider || this.abiProvider;
            if (!provider) {
                throw new Error('Missing ABI provider');
            }
            const abis = new Map();
            await Promise.all(required.map(async (account) => {
                abis.set(account.toString(), ABI.from(await provider.getAbi(account)));
            }));
            return abis;
        }
        else {
            return new Map();
        }
    }
    /**
     * Decode raw actions actions to object representations.
     * @param abis ABI defenitions required to decode all actions.
     * @param signer Placeholders in actions will be resolved to signer if set.
     */
    resolveActions(abis, signer) {
        return this.getRawActions().map((rawAction) => {
            let abi;
            if (isIdentity(rawAction)) {
                abi = this.constructor.identityAbi(this.version);
            }
            else {
                const rawAbi = abis.get(rawAction.account.toString());
                if (!rawAbi) {
                    throw new Error(`Missing ABI definition for ${rawAction.account}`);
                }
                abi = ABI.from(rawAbi);
            }
            const type = abi.getActionType(rawAction.name);
            if (!type) {
                throw new Error(`Missing type for action ${rawAction.account}:${rawAction.name} in ABI`);
            }
            let data = rawAction.decodeData(abi);
            let authorization = rawAction.authorization;
            if (signer) {
                const signerPerm = PermissionLevel.from(signer);
                const resolve = (value) => {
                    if (value instanceof Name) {
                        if (value.equals(PlaceholderName)) {
                            return signerPerm.actor;
                        }
                        else if (value.equals(PlaceholderPermission)) {
                            return signerPerm.permission;
                        }
                        else {
                            return value;
                        }
                    }
                    else if (Array.isArray(value)) {
                        return value.map(resolve);
                    }
                    else if (typeof value === 'object' && value !== null) {
                        for (const key of Object.keys(value)) {
                            value[key] = resolve(value[key]);
                        }
                        return value;
                    }
                    else {
                        return value;
                    }
                };
                data = resolve(data);
                authorization = authorization.map((auth) => {
                    let { actor, permission } = auth;
                    if (actor.equals(PlaceholderName)) {
                        actor = signerPerm.actor;
                    }
                    if (permission.equals(PlaceholderPermission)) {
                        permission = signerPerm.permission;
                    }
                    // backwards compatibility, actor placeholder will also resolve to permission when used in auth
                    if (permission.equals(PlaceholderName)) {
                        permission = signerPerm.permission;
                    }
                    return PermissionLevel.from({ actor, permission });
                });
            }
            return {
                ...rawAction,
                authorization,
                data,
            };
        });
    }
    resolveTransaction(abis, signer, ctx = {}) {
        const tx = this.getRawTransaction();
        if (!this.isIdentity() && !hasTapos(tx)) {
            if (ctx.expiration !== undefined &&
                ctx.ref_block_num !== undefined &&
                ctx.ref_block_prefix !== undefined) {
                tx.expiration = TimePointSec.from(ctx.expiration);
                tx.ref_block_num = UInt16.from(ctx.ref_block_num, 'truncate');
                tx.ref_block_prefix = UInt32.from(ctx.ref_block_prefix);
            }
            else if (ctx.block_num !== undefined &&
                ctx.ref_block_prefix !== undefined &&
                ctx.timestamp !== undefined) {
                tx.expiration = expirationTime(ctx.timestamp, ctx.expire_seconds);
                tx.ref_block_num = UInt16.from(ctx.block_num, 'truncate');
                tx.ref_block_prefix = UInt32.from(ctx.ref_block_prefix);
            }
            else {
                throw new Error('Invalid transaction context, need either a reference block or explicit TaPoS values');
            }
        }
        else if (this.isIdentity() && this.version > 2) {
            // From ESR version 3 all identity requests have expiration
            tx.expiration = ctx.expiration
                ? TimePointSec.from(ctx.expiration)
                : expirationTime(ctx.timestamp, ctx.expire_seconds);
        }
        const actions = this.resolveActions(abis, signer);
        // TODO: resolve context free actions
        const context_free_actions = tx.context_free_actions;
        return { ...tx, context_free_actions, actions };
    }
    resolve(abis, signer, ctx = {}) {
        const tx = this.resolveTransaction(abis, signer, ctx);
        const actions = tx.actions.map((action) => {
            let abi;
            if (isIdentity(action)) {
                abi = this.constructor.identityAbi(this.version);
            }
            else {
                abi = abis.get(action.account.toString());
            }
            if (!abi) {
                throw new Error(`Missing ABI definition for ${action.account}`);
            }
            const type = abi.getActionType(action.name);
            const data = Serializer.encode({ object: action.data, type, abi });
            return Action.from({ ...action, data });
        });
        const transaction = Transaction.from({ ...tx, actions });
        let chainId;
        if (this.isMultiChain()) {
            if (!ctx.chainId) {
                throw new Error('Missing chosen chain ID for multi-chain request');
            }
            chainId = ChainId.from(ctx.chainId);
            const ids = this.getChainIds();
            if (ids && !ids.some((id) => chainId.equals(id))) {
                throw new Error('Trying to resolve for chain ID not defined in request');
            }
        }
        else {
            chainId = this.getChainId();
        }
        return new ResolvedSigningRequest(this, PermissionLevel.from(signer), transaction, tx, chainId);
    }
    /**
     * Get the id of the chain where this request is valid.
     * @returns The 32-byte chain id as hex encoded string.
     */
    getChainId() {
        return this.data.chain_id.chainId;
    }
    /**
     * Chain IDs this request is valid for, only valid for multi chain requests. Value of `null` when `isMultiChain` is true denotes any chain.
     */
    getChainIds() {
        if (!this.isMultiChain()) {
            return null;
        }
        const ids = this.getInfoKey('chain_ids', { type: ChainIdVariant, array: true });
        if (ids) {
            return ids.map((id) => id.chainId);
        }
        return null;
    }
    /**
     * Set chain IDs this request is valid for, only considered for multi chain requests.
     */
    setChainIds(ids) {
        const value = ids.map((id) => ChainId.from(id).chainVariant);
        this.setInfoKey('chain_ids', value, { type: ChainIdVariant, array: true });
    }
    /**
     * True if chainId is set to chain alias `0` which indicates that the request is valid for any chain.
     */
    isMultiChain() {
        return (this.data.chain_id.variantIdx === 0 &&
            this.data.chain_id.value.equals(ChainName.UNKNOWN));
    }
    /** Return the actions in this request with action data encoded. */
    getRawActions() {
        const req = this.data.req;
        switch (req.variantName) {
            case 'action':
                return [req.value];
            case 'action[]':
                return req.value;
            case 'identity': {
                if (this.version === 2) {
                    const id = req.value;
                    let data = '0101000000000000000200000000000000'; // placeholder permission
                    let authorization = [PlaceholderAuth];
                    if (id.permission) {
                        data = Serializer.encode({ object: id });
                        authorization = [id.permission];
                    }
                    return [
                        Action.from({
                            account: '',
                            name: 'identity',
                            authorization,
                            data,
                        }),
                    ];
                }
                else {
                    // eslint-disable-next-line prefer-const
                    let { scope, permission } = req.value;
                    if (!permission) {
                        permission = PlaceholderAuth;
                    }
                    const data = Serializer.encode({ object: { scope, permission }, type: IdentityV3 });
                    return [
                        Action.from({
                            account: '',
                            name: 'identity',
                            authorization: [permission],
                            data,
                        }),
                    ];
                }
            }
            case 'transaction':
                return req.value.actions;
            default:
                throw new Error('Invalid signing request data');
        }
    }
    /** Unresolved transaction. */
    getRawTransaction() {
        const req = this.data.req;
        switch (req.variantName) {
            case 'transaction':
                return Transaction.from({ ...req.value });
            case 'action':
            case 'action[]':
            case 'identity':
                return Transaction.from({
                    actions: this.getRawActions(),
                    context_free_actions: [],
                    transaction_extensions: [],
                    expiration: '1970-01-01T00:00:00.000',
                    ref_block_num: 0,
                    ref_block_prefix: 0,
                    max_cpu_usage_ms: 0,
                    max_net_usage_words: 0,
                    delay_sec: 0,
                });
            default:
                throw new Error('Invalid signing request data');
        }
    }
    /** Whether the request is an identity request. */
    isIdentity() {
        return this.data.req.variantName === 'identity';
    }
    /** Whether the request should be broadcast by signer. */
    shouldBroadcast() {
        if (this.isIdentity()) {
            return false;
        }
        return this.data.flags.broadcast;
    }
    /**
     * Present if the request is an identity request and requests a specific account.
     * @note This returns `nil` unless a specific identity has been requested,
     *       use `isIdentity` to check id requests.
     */
    getIdentity() {
        if (!this.isIdentity()) {
            return null;
        }
        const id = this.data.req.value;
        if (id.permission && !id.permission.actor.equals(PlaceholderName)) {
            return id.permission.actor;
        }
        return null;
    }
    /**
     * Present if the request is an identity request and requests a specific permission.
     * @note This returns `nil` unless a specific permission has been requested,
     *       use `isIdentity` to check id requests.
     */
    getIdentityPermission() {
        if (!this.isIdentity()) {
            return null;
        }
        const id = this.data.req.value;
        if (id.permission && !id.permission.permission.equals(PlaceholderPermission)) {
            return id.permission.permission;
        }
        return null;
    }
    /**
     * Present if the request is an identity request and requests a specific permission.
     * @note This returns `nil` unless a specific permission has been requested,
     *       use `isIdentity` to check id requests.
     */
    getIdentityScope() {
        if (!this.isIdentity() || this.version <= 2) {
            return null;
        }
        const id = this.data.req.value;
        return id.scope;
    }
    /** Get raw info dict */
    getRawInfo() {
        const rv = {};
        for (const { key, value } of this.data.info) {
            rv[key] = value;
        }
        return rv;
    }
    getRawInfoKey(key) {
        const pair = this.data.info.find((pair) => pair.key === key);
        if (pair) {
            return pair.value;
        }
    }
    setRawInfoKey(key, value) {
        let pair = this.data.info.find((pair) => pair.key === key);
        if (!pair) {
            pair = InfoPair.from({ key, value });
            this.data.info.push(pair);
        }
        else {
            pair.value = Bytes.from(value);
        }
    }
    /** Set a metadata key. */
    setInfoKey(key, object, type) {
        let data;
        if (typeof object === 'string' && !type) {
            // match old behavior where strings encode to raw utf8 as opposed to
            // eosio-abi encoded strings (varuint32 length prefix + utf8 bytes)
            data = Bytes.from(object, 'utf8');
        }
        else {
            data = Serializer.encode({ object, type });
        }
        this.setRawInfoKey(key, data);
    }
    getInfoKey(key, type) {
        const data = this.getRawInfoKey(key);
        if (data) {
            if (type) {
                return Serializer.decode({ data, type });
            }
            else {
                // assume utf8 string if no type is given
                return data.utf8String;
            }
        }
    }
    /** Return a deep copy of this request. */
    clone() {
        let signature;
        if (this.signature) {
            signature = RequestSignature.from(JSON.parse(JSON.stringify(this.signature)));
        }
        const RequestData = this.constructor.storageType(this.version);
        const data = RequestData.from(JSON.parse(JSON.stringify(this.data)));
        return new SigningRequest(this.version, data, this.zlib, this.abiProvider, signature);
    }
    // Convenience methods.
    toString() {
        return this.encode();
    }
    toJSON() {
        return this.encode();
    }
}
class ResolvedSigningRequest {
    constructor(request, signer, transaction, resolvedTransaction, chainId) {
        this.request = request;
        this.signer = signer;
        this.transaction = transaction;
        this.resolvedTransaction = resolvedTransaction;
        this.chainId = chainId;
    }
    /** Recreate a resolved request from a callback payload. */
    static async fromPayload(payload, options = {}) {
        const request = SigningRequest.from(payload.req, options);
        const abis = await request.fetchAbis();
        return request.resolve(abis, { actor: payload.sa, permission: payload.sp }, {
            ref_block_num: payload.rbn,
            ref_block_prefix: payload.rid,
            expiration: payload.ex,
            chainId: payload.cid || request.getChainId(),
        });
    }
    get serializedTransaction() {
        return Serializer.encode({ object: this.transaction }).array;
    }
    get signingDigest() {
        return this.transaction.signingDigest(this.chainId);
    }
    get signingData() {
        return this.transaction.signingData(this.chainId);
    }
    getCallback(signatures, blockNum) {
        const { callback, flags } = this.request.data;
        if (!callback || callback.length === 0) {
            return null;
        }
        if (!signatures || signatures.length === 0) {
            throw new Error('Must have at least one signature to resolve callback');
        }
        const sigs = signatures.map((sig) => Signature.from(sig));
        const payload = {
            sig: String(sigs[0]),
            tx: String(this.transaction.id),
            rbn: String(this.transaction.ref_block_num),
            rid: String(this.transaction.ref_block_prefix),
            ex: String(this.transaction.expiration),
            req: this.request.encode(),
            sa: String(this.signer.actor),
            sp: String(this.signer.permission),
            cid: String(this.chainId),
        };
        for (const [n, sig] of sigs.slice(1).entries()) {
            payload[`sig${n}`] = String(sig);
        }
        if (blockNum) {
            payload.bn = String(UInt32.from(blockNum));
        }
        const url = callback.replace(/({{([a-z0-9]+)}})/g, (_1, _2, m) => {
            return payload[m] || '';
        });
        return {
            background: flags.background,
            payload,
            url,
        };
    }
    getIdentityProof(signature) {
        if (!this.request.isIdentity()) {
            throw new Error('Not a identity request');
        }
        return IdentityProof.from({
            chainId: this.chainId,
            scope: this.request.getIdentityScope(),
            expiration: this.transaction.expiration,
            signer: this.signer,
            signature,
        });
    }
}
function encodeAction(action, abis) {
    if (Bytes.isBytes(action.data) || action.data.constructor.abiName !== undefined) {
        return Action.from(action);
    }
    const abi = abis[String(Name.from(action.account))];
    if (!abi) {
        throw new Error(`Missing ABI for ${action.account}`);
    }
    return Action.from(action, abi);
}
function isIdentity(action) {
    const account = Name.from(action.account);
    const name = Name.from(action.name);
    return account.rawValue.equals(0) && name.equals('identity');
}
function hasTapos(tx) {
    return !(tx.expiration.equals(0) && tx.ref_block_num.equals(0) && tx.ref_block_prefix.equals(0));
}
function expirationTime(timestamp, expireSeconds = 60) {
    const ts = TimePointSec.from(timestamp || new Date());
    const exp = UInt32.from(expireSeconds);
    return TimePointSec.fromInteger(ts.value.adding(exp));
}

export { AccountName, base64u as Base64u, ChainAlias, ChainId, ChainIdVariant, ChainName, IdentityProof, IdentityV2, IdentityV3, InfoPair, PermissionName, PlaceholderAuth, PlaceholderName, PlaceholderPermission, ProtocolVersion, RequestDataV2, RequestDataV3, RequestFlags, RequestSignature, RequestVariantV2, RequestVariantV3, ResolvedSigningRequest, SigningRequest };
//# sourceMappingURL=esr.m.js.map
