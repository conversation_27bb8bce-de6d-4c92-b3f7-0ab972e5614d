{"version": 3, "sources": ["../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/internal/utils.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/internal/environment.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/internal/loop.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/internal/dom.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/internal/style_manager.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/internal/lifecycle.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/internal/scheduler.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/internal/transitions.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/internal/each.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/internal/spread.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/internal/Component.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/shared/version.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/internal/disclose-version/index.js", "../../@wharfkit/web-renderer/src/ui/components/BodyTitle.svelte", "../../@wharfkit/web-renderer/src/ui/components/BodyText.svelte", "../../@wharfkit/web-renderer/src/ui/components/icons.ts", "../../@wharfkit/web-renderer/src/ui/components/Icon.svelte", "../../@wharfkit/web-renderer/src/ui/components/Message.svelte", "../../@wharfkit/web-renderer/src/ui/components/ErrorMessage.svelte", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/store/index.js", "../../@wharfkit/web-renderer/src/ui/state.ts", "../../@wharfkit/web-renderer/src/ui/Error.svelte", "../../@wharfkit/web-renderer/src/ui/components/List.svelte", "../../@wharfkit/web-renderer/src/ui/components/ListItem.svelte", "../../@wharfkit/web-renderer/src/lib/utils.ts", "../../@wharfkit/web-renderer/src/ui/login/Blockchain.svelte", "../../@wharfkit/web-renderer/src/ui/components/Button.svelte", "../../@wharfkit/web-renderer/src/ui/components/TextInput.svelte", "../../@wharfkit/web-renderer/src/ui/components/WarningMessage.svelte", "../../@wharfkit/web-renderer/src/ui/login/Permission.svelte", "../../@wharfkit/web-renderer/src/ui/login/Wallet.svelte", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/motion/utils.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/easing/index.js", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/motion/tweened.js", "../../@wharfkit/web-renderer/src/ui/components/Countdown.svelte", "../../@wharfkit/web-renderer/node_modules/svelte/src/runtime/transition/index.js", "../../@wharfkit/web-renderer/src/ui/components/Transition.svelte", "../../@wharfkit/web-renderer/src/ui/Login.svelte", "../../@wharfkit/web-renderer/src/ui/components/ButtonGroup.svelte", "../../@wharfkit/web-renderer/src/ui/components/Accept.svelte", "../../@wharfkit/web-renderer/src/ui/components/Asset.svelte", "../../@wharfkit/web-renderer/src/ui/components/Close.svelte", "../../@wharfkit/web-renderer/src/ui/components/Link.svelte", "../../@wharfkit/web-renderer/src/lib/qrcode/ErrorCorrectLevel.ts", "../../@wharfkit/web-renderer/src/lib/qrcode/mode.ts", "../../@wharfkit/web-renderer/src/lib/qrcode/8BitByte.ts", "../../@wharfkit/web-renderer/src/lib/qrcode/BitBuffer.ts", "../../@wharfkit/web-renderer/src/lib/qrcode/math.ts", "../../@wharfkit/web-renderer/src/lib/qrcode/Polynomial.ts", "../../@wharfkit/web-renderer/src/lib/qrcode/RSBlock.ts", "../../@wharfkit/web-renderer/src/lib/qrcode/util.ts", "../../@wharfkit/web-renderer/src/lib/qrcode/QRCode.ts", "../../@wharfkit/web-renderer/src/lib/qrcode/index.ts", "../../@wharfkit/web-renderer/src/ui/components/Qr.svelte", "../../@wharfkit/web-renderer/src/ui/components/Textarea.svelte", "../../@wharfkit/web-renderer/src/ui/Prompt.svelte", "../../@wharfkit/web-renderer/src/ui/settings/About.svelte", "../../@wharfkit/web-renderer/src/ui/components/ListOption.svelte", "../../@wharfkit/web-renderer/src/ui/settings/Selector.svelte", "../../@wharfkit/web-renderer/src/ui/Settings.svelte", "../../@wharfkit/web-renderer/src/ui/Transact.svelte", "../../@wharfkit/web-renderer/src/ui/createAccount/AccountPlugin.svelte", "../../@wharfkit/web-renderer/src/ui/CreateAccount.svelte", "../../@wharfkit/web-renderer/src/ui/components/HeaderButton.svelte", "../../@wharfkit/web-renderer/src/ui/components/HeaderWaves.svelte", "../../@wharfkit/web-renderer/src/ui/components/Header.svelte", "../../@wharfkit/web-renderer/src/ui/components/Modal.svelte", "../../@wharfkit/web-renderer/src/ui/App.svelte", "../../@wharfkit/web-renderer/node_modules/@sveltekit-i18n/base/dist/index.js", "../../@wharfkit/web-renderer/node_modules/@sveltekit-i18n/parser-default/dist/index.js", "../../@wharfkit/web-renderer/node_modules/sveltekit-i18n/dist/index.js", "../../@wharfkit/web-renderer/src/lib/translations.ts", "../../@wharfkit/web-renderer/src/index.ts"], "sourcesContent": ["/** @returns {void} */\nexport function noop() {}\n\nexport const identity = (x) => x;\n\n/**\n * @template T\n * @template S\n * @param {T} tar\n * @param {S} src\n * @returns {T & S}\n */\nexport function assign(tar, src) {\n\t// @ts-ignore\n\tfor (const k in src) tar[k] = src[k];\n\treturn /** @type {T & S} */ (tar);\n}\n\n// Adapted from https://github.com/then/is-promise/blob/master/index.js\n// Distributed under MIT License https://github.com/then/is-promise/blob/master/LICENSE\n/**\n * @param {any} value\n * @returns {value is PromiseLike<any>}\n */\nexport function is_promise(value) {\n\treturn (\n\t\t!!value &&\n\t\t(typeof value === 'object' || typeof value === 'function') &&\n\t\ttypeof (/** @type {any} */ (value).then) === 'function'\n\t);\n}\n\n/** @returns {void} */\nexport function add_location(element, file, line, column, char) {\n\telement.__svelte_meta = {\n\t\tloc: { file, line, column, char }\n\t};\n}\n\nexport function run(fn) {\n\treturn fn();\n}\n\nexport function blank_object() {\n\treturn Object.create(null);\n}\n\n/**\n * @param {Function[]} fns\n * @returns {void}\n */\nexport function run_all(fns) {\n\tfns.forEach(run);\n}\n\n/**\n * @param {any} thing\n * @returns {thing is Function}\n */\nexport function is_function(thing) {\n\treturn typeof thing === 'function';\n}\n\n/** @returns {boolean} */\nexport function safe_not_equal(a, b) {\n\treturn a != a ? b == b : a !== b || (a && typeof a === 'object') || typeof a === 'function';\n}\n\nlet src_url_equal_anchor;\n\n/**\n * @param {string} element_src\n * @param {string} url\n * @returns {boolean}\n */\nexport function src_url_equal(element_src, url) {\n\tif (element_src === url) return true;\n\tif (!src_url_equal_anchor) {\n\t\tsrc_url_equal_anchor = document.createElement('a');\n\t}\n\t// This is actually faster than doing URL(..).href\n\tsrc_url_equal_anchor.href = url;\n\treturn element_src === src_url_equal_anchor.href;\n}\n\n/** @param {string} srcset */\nfunction split_srcset(srcset) {\n\treturn srcset.split(',').map((src) => src.trim().split(' ').filter(Boolean));\n}\n\n/**\n * @param {HTMLSourceElement | HTMLImageElement} element_srcset\n * @param {string | undefined | null} srcset\n * @returns {boolean}\n */\nexport function srcset_url_equal(element_srcset, srcset) {\n\tconst element_urls = split_srcset(element_srcset.srcset);\n\tconst urls = split_srcset(srcset || '');\n\n\treturn (\n\t\turls.length === element_urls.length &&\n\t\turls.every(\n\t\t\t([url, width], i) =>\n\t\t\t\twidth === element_urls[i][1] &&\n\t\t\t\t// We need to test both ways because Vite will create an a full URL with\n\t\t\t\t// `new URL(asset, import.meta.url).href` for the client when `base: './'`, and the\n\t\t\t\t// relative URLs inside srcset are not automatically resolved to absolute URLs by\n\t\t\t\t// browsers (in contrast to img.src). This means both SSR and DOM code could\n\t\t\t\t// contain relative or absolute URLs.\n\t\t\t\t(src_url_equal(element_urls[i][0], url) || src_url_equal(url, element_urls[i][0]))\n\t\t)\n\t);\n}\n\n/** @returns {boolean} */\nexport function not_equal(a, b) {\n\treturn a != a ? b == b : a !== b;\n}\n\n/** @returns {boolean} */\nexport function is_empty(obj) {\n\treturn Object.keys(obj).length === 0;\n}\n\n/** @returns {void} */\nexport function validate_store(store, name) {\n\tif (store != null && typeof store.subscribe !== 'function') {\n\t\tthrow new Error(`'${name}' is not a store with a 'subscribe' method`);\n\t}\n}\n\nexport function subscribe(store, ...callbacks) {\n\tif (store == null) {\n\t\tfor (const callback of callbacks) {\n\t\t\tcallback(undefined);\n\t\t}\n\t\treturn noop;\n\t}\n\tconst unsub = store.subscribe(...callbacks);\n\treturn unsub.unsubscribe ? () => unsub.unsubscribe() : unsub;\n}\n\n/**\n * Get the current value from a store by subscribing and immediately unsubscribing.\n *\n * https://svelte.dev/docs/svelte-store#get\n * @template T\n * @param {import('../store/public.js').Readable<T>} store\n * @returns {T}\n */\nexport function get_store_value(store) {\n\tlet value;\n\tsubscribe(store, (_) => (value = _))();\n\treturn value;\n}\n\n/** @returns {void} */\nexport function component_subscribe(component, store, callback) {\n\tcomponent.$$.on_destroy.push(subscribe(store, callback));\n}\n\nexport function create_slot(definition, ctx, $$scope, fn) {\n\tif (definition) {\n\t\tconst slot_ctx = get_slot_context(definition, ctx, $$scope, fn);\n\t\treturn definition[0](slot_ctx);\n\t}\n}\n\nfunction get_slot_context(definition, ctx, $$scope, fn) {\n\treturn definition[1] && fn ? assign($$scope.ctx.slice(), definition[1](fn(ctx))) : $$scope.ctx;\n}\n\nexport function get_slot_changes(definition, $$scope, dirty, fn) {\n\tif (definition[2] && fn) {\n\t\tconst lets = definition[2](fn(dirty));\n\t\tif ($$scope.dirty === undefined) {\n\t\t\treturn lets;\n\t\t}\n\t\tif (typeof lets === 'object') {\n\t\t\tconst merged = [];\n\t\t\tconst len = Math.max($$scope.dirty.length, lets.length);\n\t\t\tfor (let i = 0; i < len; i += 1) {\n\t\t\t\tmerged[i] = $$scope.dirty[i] | lets[i];\n\t\t\t}\n\t\t\treturn merged;\n\t\t}\n\t\treturn $$scope.dirty | lets;\n\t}\n\treturn $$scope.dirty;\n}\n\n/** @returns {void} */\nexport function update_slot_base(\n\tslot,\n\tslot_definition,\n\tctx,\n\t$$scope,\n\tslot_changes,\n\tget_slot_context_fn\n) {\n\tif (slot_changes) {\n\t\tconst slot_context = get_slot_context(slot_definition, ctx, $$scope, get_slot_context_fn);\n\t\tslot.p(slot_context, slot_changes);\n\t}\n}\n\n/** @returns {void} */\nexport function update_slot(\n\tslot,\n\tslot_definition,\n\tctx,\n\t$$scope,\n\tdirty,\n\tget_slot_changes_fn,\n\tget_slot_context_fn\n) {\n\tconst slot_changes = get_slot_changes(slot_definition, $$scope, dirty, get_slot_changes_fn);\n\tupdate_slot_base(slot, slot_definition, ctx, $$scope, slot_changes, get_slot_context_fn);\n}\n\n/** @returns {any[] | -1} */\nexport function get_all_dirty_from_scope($$scope) {\n\tif ($$scope.ctx.length > 32) {\n\t\tconst dirty = [];\n\t\tconst length = $$scope.ctx.length / 32;\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tdirty[i] = -1;\n\t\t}\n\t\treturn dirty;\n\t}\n\treturn -1;\n}\n\n/** @returns {{}} */\nexport function exclude_internal_props(props) {\n\tconst result = {};\n\tfor (const k in props) if (k[0] !== '$') result[k] = props[k];\n\treturn result;\n}\n\n/** @returns {{}} */\nexport function compute_rest_props(props, keys) {\n\tconst rest = {};\n\tkeys = new Set(keys);\n\tfor (const k in props) if (!keys.has(k) && k[0] !== '$') rest[k] = props[k];\n\treturn rest;\n}\n\n/** @returns {{}} */\nexport function compute_slots(slots) {\n\tconst result = {};\n\tfor (const key in slots) {\n\t\tresult[key] = true;\n\t}\n\treturn result;\n}\n\n/** @returns {(this: any, ...args: any[]) => void} */\nexport function once(fn) {\n\tlet ran = false;\n\treturn function (...args) {\n\t\tif (ran) return;\n\t\tran = true;\n\t\tfn.call(this, ...args);\n\t};\n}\n\nexport function null_to_empty(value) {\n\treturn value == null ? '' : value;\n}\n\nexport function set_store_value(store, ret, value) {\n\tstore.set(value);\n\treturn ret;\n}\n\nexport const has_prop = (obj, prop) => Object.prototype.hasOwnProperty.call(obj, prop);\n\nexport function action_destroyer(action_result) {\n\treturn action_result && is_function(action_result.destroy) ? action_result.destroy : noop;\n}\n\n/** @param {number | string} value\n * @returns {[number, string]}\n */\nexport function split_css_unit(value) {\n\tconst split = typeof value === 'string' && value.match(/^\\s*(-?[\\d.]+)([^\\s]*)\\s*$/);\n\treturn split ? [parseFloat(split[1]), split[2] || 'px'] : [/** @type {number} */ (value), 'px'];\n}\n\nexport const contenteditable_truthy_values = ['', true, 1, 'true', 'contenteditable'];\n", "import { noop } from './utils.js';\n\nexport const is_client = typeof window !== 'undefined';\n\n/** @type {() => number} */\nexport let now = is_client ? () => window.performance.now() : () => Date.now();\n\nexport let raf = is_client ? (cb) => requestAnimationFrame(cb) : noop;\n\n// used internally for testing\n/** @returns {void} */\nexport function set_now(fn) {\n\tnow = fn;\n}\n\n/** @returns {void} */\nexport function set_raf(fn) {\n\traf = fn;\n}\n", "import { raf } from './environment.js';\n\nconst tasks = new Set();\n\n/**\n * @param {number} now\n * @returns {void}\n */\nfunction run_tasks(now) {\n\ttasks.forEach((task) => {\n\t\tif (!task.c(now)) {\n\t\t\ttasks.delete(task);\n\t\t\ttask.f();\n\t\t}\n\t});\n\tif (tasks.size !== 0) raf(run_tasks);\n}\n\n/**\n * For testing purposes only!\n * @returns {void}\n */\nexport function clear_loops() {\n\ttasks.clear();\n}\n\n/**\n * Creates a new task that runs on each raf frame\n * until it returns a falsy value or is aborted\n * @param {import('./private.js').TaskCallback} callback\n * @returns {import('./private.js').Task}\n */\nexport function loop(callback) {\n\t/** @type {import('./private.js').TaskEntry} */\n\tlet task;\n\tif (tasks.size === 0) raf(run_tasks);\n\treturn {\n\t\tpromise: new Promise((fulfill) => {\n\t\t\ttasks.add((task = { c: callback, f: fulfill }));\n\t\t}),\n\t\tabort() {\n\t\t\ttasks.delete(task);\n\t\t}\n\t};\n}\n", "import { ResizeObserverSingleton } from './ResizeObserverSingleton.js';\nimport { contenteditable_truthy_values, has_prop } from './utils.js';\n// Track which nodes are claimed during hydration. Unclaimed nodes can then be removed from the DOM\n// at the end of hydration without touching the remaining nodes.\nlet is_hydrating = false;\n\n/**\n * @returns {void}\n */\nexport function start_hydrating() {\n\tis_hydrating = true;\n}\n\n/**\n * @returns {void}\n */\nexport function end_hydrating() {\n\tis_hydrating = false;\n}\n\n/**\n * @param {number} low\n * @param {number} high\n * @param {(index: number) => number} key\n * @param {number} value\n * @returns {number}\n */\nfunction upper_bound(low, high, key, value) {\n\t// Return first index of value larger than input value in the range [low, high)\n\twhile (low < high) {\n\t\tconst mid = low + ((high - low) >> 1);\n\t\tif (key(mid) <= value) {\n\t\t\tlow = mid + 1;\n\t\t} else {\n\t\t\thigh = mid;\n\t\t}\n\t}\n\treturn low;\n}\n\n/**\n * @param {NodeEx} target\n * @returns {void}\n */\nfunction init_hydrate(target) {\n\tif (target.hydrate_init) return;\n\ttarget.hydrate_init = true;\n\t// We know that all children have claim_order values since the unclaimed have been detached if target is not <head>\n\n\tlet children = /** @type {ArrayLike<NodeEx2>} */ (target.childNodes);\n\t// If target is <head>, there may be children without claim_order\n\tif (target.nodeName === 'HEAD') {\n\t\tconst myChildren = [];\n\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\tconst node = children[i];\n\t\t\tif (node.claim_order !== undefined) {\n\t\t\t\tmyChildren.push(node);\n\t\t\t}\n\t\t}\n\t\tchildren = myChildren;\n\t}\n\t/*\n\t * Reorder claimed children optimally.\n\t * We can reorder claimed children optimally by finding the longest subsequence of\n\t * nodes that are already claimed in order and only moving the rest. The longest\n\t * subsequence of nodes that are claimed in order can be found by\n\t * computing the longest increasing subsequence of .claim_order values.\n\t *\n\t * This algorithm is optimal in generating the least amount of reorder operations\n\t * possible.\n\t *\n\t * Proof:\n\t * We know that, given a set of reordering operations, the nodes that do not move\n\t * always form an increasing subsequence, since they do not move among each other\n\t * meaning that they must be already ordered among each other. Thus, the maximal\n\t * set of nodes that do not move form a longest increasing subsequence.\n\t */\n\t// Compute longest increasing subsequence\n\t// m: subsequence length j => index k of smallest value that ends an increasing subsequence of length j\n\tconst m = new Int32Array(children.length + 1);\n\t// Predecessor indices + 1\n\tconst p = new Int32Array(children.length);\n\tm[0] = -1;\n\tlet longest = 0;\n\tfor (let i = 0; i < children.length; i++) {\n\t\tconst current = children[i].claim_order;\n\t\t// Find the largest subsequence length such that it ends in a value less than our current value\n\t\t// upper_bound returns first greater value, so we subtract one\n\t\t// with fast path for when we are on the current longest subsequence\n\t\tconst seqLen =\n\t\t\t(longest > 0 && children[m[longest]].claim_order <= current\n\t\t\t\t? longest + 1\n\t\t\t\t: upper_bound(1, longest, (idx) => children[m[idx]].claim_order, current)) - 1;\n\t\tp[i] = m[seqLen] + 1;\n\t\tconst newLen = seqLen + 1;\n\t\t// We can guarantee that current is the smallest value. Otherwise, we would have generated a longer sequence.\n\t\tm[newLen] = i;\n\t\tlongest = Math.max(newLen, longest);\n\t}\n\t// The longest increasing subsequence of nodes (initially reversed)\n\n\t/**\n\t * @type {NodeEx2[]}\n\t */\n\tconst lis = [];\n\t// The rest of the nodes, nodes that will be moved\n\n\t/**\n\t * @type {NodeEx2[]}\n\t */\n\tconst toMove = [];\n\tlet last = children.length - 1;\n\tfor (let cur = m[longest] + 1; cur != 0; cur = p[cur - 1]) {\n\t\tlis.push(children[cur - 1]);\n\t\tfor (; last >= cur; last--) {\n\t\t\ttoMove.push(children[last]);\n\t\t}\n\t\tlast--;\n\t}\n\tfor (; last >= 0; last--) {\n\t\ttoMove.push(children[last]);\n\t}\n\tlis.reverse();\n\t// We sort the nodes being moved to guarantee that their insertion order matches the claim order\n\ttoMove.sort((a, b) => a.claim_order - b.claim_order);\n\t// Finally, we move the nodes\n\tfor (let i = 0, j = 0; i < toMove.length; i++) {\n\t\twhile (j < lis.length && toMove[i].claim_order >= lis[j].claim_order) {\n\t\t\tj++;\n\t\t}\n\t\tconst anchor = j < lis.length ? lis[j] : null;\n\t\ttarget.insertBefore(toMove[i], anchor);\n\t}\n}\n\n/**\n * @param {Node} target\n * @param {Node} node\n * @returns {void}\n */\nexport function append(target, node) {\n\ttarget.appendChild(node);\n}\n\n/**\n * @param {Node} target\n * @param {string} style_sheet_id\n * @param {string} styles\n * @returns {void}\n */\nexport function append_styles(target, style_sheet_id, styles) {\n\tconst append_styles_to = get_root_for_style(target);\n\tif (!append_styles_to.getElementById(style_sheet_id)) {\n\t\tconst style = element('style');\n\t\tstyle.id = style_sheet_id;\n\t\tstyle.textContent = styles;\n\t\tappend_stylesheet(append_styles_to, style);\n\t}\n}\n\n/**\n * @param {Node} node\n * @returns {ShadowRoot | Document}\n */\nexport function get_root_for_style(node) {\n\tif (!node) return document;\n\tconst root = node.getRootNode ? node.getRootNode() : node.ownerDocument;\n\tif (root && /** @type {ShadowRoot} */ (root).host) {\n\t\treturn /** @type {ShadowRoot} */ (root);\n\t}\n\treturn node.ownerDocument;\n}\n\n/**\n * @param {Node} node\n * @returns {CSSStyleSheet}\n */\nexport function append_empty_stylesheet(node) {\n\tconst style_element = element('style');\n\t// For transitions to work without 'style-src: unsafe-inline' Content Security Policy,\n\t// these empty tags need to be allowed with a hash as a workaround until we move to the Web Animations API.\n\t// Using the hash for the empty string (for an empty tag) works in all browsers except Safari.\n\t// So as a workaround for the workaround, when we append empty style tags we set their content to /* empty */.\n\t// The hash 'sha256-9OlNO0DNEeaVzHL4RZwCLsBHA8WBQ8toBp/4F5XV2nc=' will then work even in Safari.\n\tstyle_element.textContent = '/* empty */';\n\tappend_stylesheet(get_root_for_style(node), style_element);\n\treturn style_element.sheet;\n}\n\n/**\n * @param {ShadowRoot | Document} node\n * @param {HTMLStyleElement} style\n * @returns {CSSStyleSheet}\n */\nfunction append_stylesheet(node, style) {\n\tappend(/** @type {Document} */ (node).head || node, style);\n\treturn style.sheet;\n}\n\n/**\n * @param {NodeEx} target\n * @param {NodeEx} node\n * @returns {void}\n */\nexport function append_hydration(target, node) {\n\tif (is_hydrating) {\n\t\tinit_hydrate(target);\n\t\tif (\n\t\t\ttarget.actual_end_child === undefined ||\n\t\t\t(target.actual_end_child !== null && target.actual_end_child.parentNode !== target)\n\t\t) {\n\t\t\ttarget.actual_end_child = target.firstChild;\n\t\t}\n\t\t// Skip nodes of undefined ordering\n\t\twhile (target.actual_end_child !== null && target.actual_end_child.claim_order === undefined) {\n\t\t\ttarget.actual_end_child = target.actual_end_child.nextSibling;\n\t\t}\n\t\tif (node !== target.actual_end_child) {\n\t\t\t// We only insert if the ordering of this node should be modified or the parent node is not target\n\t\t\tif (node.claim_order !== undefined || node.parentNode !== target) {\n\t\t\t\ttarget.insertBefore(node, target.actual_end_child);\n\t\t\t}\n\t\t} else {\n\t\t\ttarget.actual_end_child = node.nextSibling;\n\t\t}\n\t} else if (node.parentNode !== target || node.nextSibling !== null) {\n\t\ttarget.appendChild(node);\n\t}\n}\n\n/**\n * @param {Node} target\n * @param {Node} node\n * @param {Node} [anchor]\n * @returns {void}\n */\nexport function insert(target, node, anchor) {\n\ttarget.insertBefore(node, anchor || null);\n}\n\n/**\n * @param {NodeEx} target\n * @param {NodeEx} node\n * @param {NodeEx} [anchor]\n * @returns {void}\n */\nexport function insert_hydration(target, node, anchor) {\n\tif (is_hydrating && !anchor) {\n\t\tappend_hydration(target, node);\n\t} else if (node.parentNode !== target || node.nextSibling != anchor) {\n\t\ttarget.insertBefore(node, anchor || null);\n\t}\n}\n\n/**\n * @param {Node} node\n * @returns {void}\n */\nexport function detach(node) {\n\tif (node.parentNode) {\n\t\tnode.parentNode.removeChild(node);\n\t}\n}\n\n/**\n * @returns {void} */\nexport function destroy_each(iterations, detaching) {\n\tfor (let i = 0; i < iterations.length; i += 1) {\n\t\tif (iterations[i]) iterations[i].d(detaching);\n\t}\n}\n\n/**\n * @template {keyof HTMLElementTagNameMap} K\n * @param {K} name\n * @returns {HTMLElementTagNameMap[K]}\n */\nexport function element(name) {\n\treturn document.createElement(name);\n}\n\n/**\n * @template {keyof HTMLElementTagNameMap} K\n * @param {K} name\n * @param {string} is\n * @returns {HTMLElementTagNameMap[K]}\n */\nexport function element_is(name, is) {\n\treturn document.createElement(name, { is });\n}\n\n/**\n * @template T\n * @template {keyof T} K\n * @param {T} obj\n * @param {K[]} exclude\n * @returns {Pick<T, Exclude<keyof T, K>>}\n */\nexport function object_without_properties(obj, exclude) {\n\tconst target = /** @type {Pick<T, Exclude<keyof T, K>>} */ ({});\n\tfor (const k in obj) {\n\t\tif (\n\t\t\thas_prop(obj, k) &&\n\t\t\t// @ts-ignore\n\t\t\texclude.indexOf(k) === -1\n\t\t) {\n\t\t\t// @ts-ignore\n\t\t\ttarget[k] = obj[k];\n\t\t}\n\t}\n\treturn target;\n}\n\n/**\n * @template {keyof SVGElementTagNameMap} K\n * @param {K} name\n * @returns {SVGElement}\n */\nexport function svg_element(name) {\n\treturn document.createElementNS('http://www.w3.org/2000/svg', name);\n}\n\n/**\n * @param {string} data\n * @returns {Text}\n */\nexport function text(data) {\n\treturn document.createTextNode(data);\n}\n\n/**\n * @returns {Text} */\nexport function space() {\n\treturn text(' ');\n}\n\n/**\n * @returns {Text} */\nexport function empty() {\n\treturn text('');\n}\n\n/**\n * @param {string} content\n * @returns {Comment}\n */\nexport function comment(content) {\n\treturn document.createComment(content);\n}\n\n/**\n * @param {EventTarget} node\n * @param {string} event\n * @param {EventListenerOrEventListenerObject} handler\n * @param {boolean | AddEventListenerOptions | EventListenerOptions} [options]\n * @returns {() => void}\n */\nexport function listen(node, event, handler, options) {\n\tnode.addEventListener(event, handler, options);\n\treturn () => node.removeEventListener(event, handler, options);\n}\n\n/**\n * @returns {(event: any) => any} */\nexport function prevent_default(fn) {\n\treturn function (event) {\n\t\tevent.preventDefault();\n\t\t// @ts-ignore\n\t\treturn fn.call(this, event);\n\t};\n}\n\n/**\n * @returns {(event: any) => any} */\nexport function stop_propagation(fn) {\n\treturn function (event) {\n\t\tevent.stopPropagation();\n\t\t// @ts-ignore\n\t\treturn fn.call(this, event);\n\t};\n}\n\n/**\n * @returns {(event: any) => any} */\nexport function stop_immediate_propagation(fn) {\n\treturn function (event) {\n\t\tevent.stopImmediatePropagation();\n\t\t// @ts-ignore\n\t\treturn fn.call(this, event);\n\t};\n}\n\n/**\n * @returns {(event: any) => void} */\nexport function self(fn) {\n\treturn function (event) {\n\t\t// @ts-ignore\n\t\tif (event.target === this) fn.call(this, event);\n\t};\n}\n\n/**\n * @returns {(event: any) => void} */\nexport function trusted(fn) {\n\treturn function (event) {\n\t\t// @ts-ignore\n\t\tif (event.isTrusted) fn.call(this, event);\n\t};\n}\n\n/**\n * @param {Element} node\n * @param {string} attribute\n * @param {string} [value]\n * @returns {void}\n */\nexport function attr(node, attribute, value) {\n\tif (value == null) node.removeAttribute(attribute);\n\telse if (node.getAttribute(attribute) !== value) node.setAttribute(attribute, value);\n}\n/**\n * List of attributes that should always be set through the attr method,\n * because updating them through the property setter doesn't work reliably.\n * In the example of `width`/`height`, the problem is that the setter only\n * accepts numeric values, but the attribute can also be set to a string like `50%`.\n * If this list becomes too big, rethink this approach.\n */\nconst always_set_through_set_attribute = ['width', 'height'];\n\n/**\n * @param {Element & ElementCSSInlineStyle} node\n * @param {{ [x: string]: string }} attributes\n * @returns {void}\n */\nexport function set_attributes(node, attributes) {\n\t// @ts-ignore\n\tconst descriptors = Object.getOwnPropertyDescriptors(node.__proto__);\n\tfor (const key in attributes) {\n\t\tif (attributes[key] == null) {\n\t\t\tnode.removeAttribute(key);\n\t\t} else if (key === 'style') {\n\t\t\tnode.style.cssText = attributes[key];\n\t\t} else if (key === '__value') {\n\t\t\t/** @type {any} */ (node).value = node[key] = attributes[key];\n\t\t} else if (\n\t\t\tdescriptors[key] &&\n\t\t\tdescriptors[key].set &&\n\t\t\talways_set_through_set_attribute.indexOf(key) === -1\n\t\t) {\n\t\t\tnode[key] = attributes[key];\n\t\t} else {\n\t\t\tattr(node, key, attributes[key]);\n\t\t}\n\t}\n}\n\n/**\n * @param {Element & ElementCSSInlineStyle} node\n * @param {{ [x: string]: string }} attributes\n * @returns {void}\n */\nexport function set_svg_attributes(node, attributes) {\n\tfor (const key in attributes) {\n\t\tattr(node, key, attributes[key]);\n\t}\n}\n\n/**\n * @param {Record<string, unknown>} data_map\n * @returns {void}\n */\nexport function set_custom_element_data_map(node, data_map) {\n\tObject.keys(data_map).forEach((key) => {\n\t\tset_custom_element_data(node, key, data_map[key]);\n\t});\n}\n\n/**\n * @returns {void} */\nexport function set_custom_element_data(node, prop, value) {\n\tif (prop in node) {\n\t\tnode[prop] = typeof node[prop] === 'boolean' && value === '' ? true : value;\n\t} else {\n\t\tattr(node, prop, value);\n\t}\n}\n\n/**\n * @param {string} tag\n */\nexport function set_dynamic_element_data(tag) {\n\treturn /-/.test(tag) ? set_custom_element_data_map : set_attributes;\n}\n\n/**\n * @returns {void}\n */\nexport function xlink_attr(node, attribute, value) {\n\tnode.setAttributeNS('http://www.w3.org/1999/xlink', attribute, value);\n}\n\n/**\n * @param {HTMLElement} node\n * @returns {string}\n */\nexport function get_svelte_dataset(node) {\n\treturn node.dataset.svelteH;\n}\n\n/**\n * @returns {unknown[]} */\nexport function get_binding_group_value(group, __value, checked) {\n\tconst value = new Set();\n\tfor (let i = 0; i < group.length; i += 1) {\n\t\tif (group[i].checked) value.add(group[i].__value);\n\t}\n\tif (!checked) {\n\t\tvalue.delete(__value);\n\t}\n\treturn Array.from(value);\n}\n\n/**\n * @param {HTMLInputElement[]} group\n * @returns {{ p(...inputs: HTMLInputElement[]): void; r(): void; }}\n */\nexport function init_binding_group(group) {\n\t/**\n\t * @type {HTMLInputElement[]} */\n\tlet _inputs;\n\treturn {\n\t\t/* push */ p(...inputs) {\n\t\t\t_inputs = inputs;\n\t\t\t_inputs.forEach((input) => group.push(input));\n\t\t},\n\t\t/* remove */ r() {\n\t\t\t_inputs.forEach((input) => group.splice(group.indexOf(input), 1));\n\t\t}\n\t};\n}\n\n/**\n * @param {number[]} indexes\n * @returns {{ u(new_indexes: number[]): void; p(...inputs: HTMLInputElement[]): void; r: () => void; }}\n */\nexport function init_binding_group_dynamic(group, indexes) {\n\t/**\n\t * @type {HTMLInputElement[]} */\n\tlet _group = get_binding_group(group);\n\n\t/**\n\t * @type {HTMLInputElement[]} */\n\tlet _inputs;\n\n\tfunction get_binding_group(group) {\n\t\tfor (let i = 0; i < indexes.length; i++) {\n\t\t\tgroup = group[indexes[i]] = group[indexes[i]] || [];\n\t\t}\n\t\treturn group;\n\t}\n\n\t/**\n\t * @returns {void} */\n\tfunction push() {\n\t\t_inputs.forEach((input) => _group.push(input));\n\t}\n\n\t/**\n\t * @returns {void} */\n\tfunction remove() {\n\t\t_inputs.forEach((input) => _group.splice(_group.indexOf(input), 1));\n\t}\n\treturn {\n\t\t/* update */ u(new_indexes) {\n\t\t\tindexes = new_indexes;\n\t\t\tconst new_group = get_binding_group(group);\n\t\t\tif (new_group !== _group) {\n\t\t\t\tremove();\n\t\t\t\t_group = new_group;\n\t\t\t\tpush();\n\t\t\t}\n\t\t},\n\t\t/* push */ p(...inputs) {\n\t\t\t_inputs = inputs;\n\t\t\tpush();\n\t\t},\n\t\t/* remove */ r: remove\n\t};\n}\n\n/** @returns {number} */\nexport function to_number(value) {\n\treturn value === '' ? null : +value;\n}\n\n/** @returns {any[]} */\nexport function time_ranges_to_array(ranges) {\n\tconst array = [];\n\tfor (let i = 0; i < ranges.length; i += 1) {\n\t\tarray.push({ start: ranges.start(i), end: ranges.end(i) });\n\t}\n\treturn array;\n}\n\n/**\n * @param {Element} element\n * @returns {ChildNode[]}\n */\nexport function children(element) {\n\treturn Array.from(element.childNodes);\n}\n\n/**\n * @param {ChildNodeArray} nodes\n * @returns {void}\n */\nfunction init_claim_info(nodes) {\n\tif (nodes.claim_info === undefined) {\n\t\tnodes.claim_info = { last_index: 0, total_claimed: 0 };\n\t}\n}\n\n/**\n * @template {ChildNodeEx} R\n * @param {ChildNodeArray} nodes\n * @param {(node: ChildNodeEx) => node is R} predicate\n * @param {(node: ChildNodeEx) => ChildNodeEx | undefined} processNode\n * @param {() => R} createNode\n * @param {boolean} dontUpdateLastIndex\n * @returns {R}\n */\nfunction claim_node(nodes, predicate, processNode, createNode, dontUpdateLastIndex = false) {\n\t// Try to find nodes in an order such that we lengthen the longest increasing subsequence\n\tinit_claim_info(nodes);\n\tconst resultNode = (() => {\n\t\t// We first try to find an element after the previous one\n\t\tfor (let i = nodes.claim_info.last_index; i < nodes.length; i++) {\n\t\t\tconst node = nodes[i];\n\t\t\tif (predicate(node)) {\n\t\t\t\tconst replacement = processNode(node);\n\t\t\t\tif (replacement === undefined) {\n\t\t\t\t\tnodes.splice(i, 1);\n\t\t\t\t} else {\n\t\t\t\t\tnodes[i] = replacement;\n\t\t\t\t}\n\t\t\t\tif (!dontUpdateLastIndex) {\n\t\t\t\t\tnodes.claim_info.last_index = i;\n\t\t\t\t}\n\t\t\t\treturn node;\n\t\t\t}\n\t\t}\n\t\t// Otherwise, we try to find one before\n\t\t// We iterate in reverse so that we don't go too far back\n\t\tfor (let i = nodes.claim_info.last_index - 1; i >= 0; i--) {\n\t\t\tconst node = nodes[i];\n\t\t\tif (predicate(node)) {\n\t\t\t\tconst replacement = processNode(node);\n\t\t\t\tif (replacement === undefined) {\n\t\t\t\t\tnodes.splice(i, 1);\n\t\t\t\t} else {\n\t\t\t\t\tnodes[i] = replacement;\n\t\t\t\t}\n\t\t\t\tif (!dontUpdateLastIndex) {\n\t\t\t\t\tnodes.claim_info.last_index = i;\n\t\t\t\t} else if (replacement === undefined) {\n\t\t\t\t\t// Since we spliced before the last_index, we decrease it\n\t\t\t\t\tnodes.claim_info.last_index--;\n\t\t\t\t}\n\t\t\t\treturn node;\n\t\t\t}\n\t\t}\n\t\t// If we can't find any matching node, we create a new one\n\t\treturn createNode();\n\t})();\n\tresultNode.claim_order = nodes.claim_info.total_claimed;\n\tnodes.claim_info.total_claimed += 1;\n\treturn resultNode;\n}\n\n/**\n * @param {ChildNodeArray} nodes\n * @param {string} name\n * @param {{ [key: string]: boolean }} attributes\n * @param {(name: string) => Element | SVGElement} create_element\n * @returns {Element | SVGElement}\n */\nfunction claim_element_base(nodes, name, attributes, create_element) {\n\treturn claim_node(\n\t\tnodes,\n\t\t/** @returns {node is Element | SVGElement} */\n\t\t(node) => node.nodeName === name,\n\t\t/** @param {Element} node */\n\t\t(node) => {\n\t\t\tconst remove = [];\n\t\t\tfor (let j = 0; j < node.attributes.length; j++) {\n\t\t\t\tconst attribute = node.attributes[j];\n\t\t\t\tif (!attributes[attribute.name]) {\n\t\t\t\t\tremove.push(attribute.name);\n\t\t\t\t}\n\t\t\t}\n\t\t\tremove.forEach((v) => node.removeAttribute(v));\n\t\t\treturn undefined;\n\t\t},\n\t\t() => create_element(name)\n\t);\n}\n\n/**\n * @param {ChildNodeArray} nodes\n * @param {string} name\n * @param {{ [key: string]: boolean }} attributes\n * @returns {Element | SVGElement}\n */\nexport function claim_element(nodes, name, attributes) {\n\treturn claim_element_base(nodes, name, attributes, element);\n}\n\n/**\n * @param {ChildNodeArray} nodes\n * @param {string} name\n * @param {{ [key: string]: boolean }} attributes\n * @returns {Element | SVGElement}\n */\nexport function claim_svg_element(nodes, name, attributes) {\n\treturn claim_element_base(nodes, name, attributes, svg_element);\n}\n\n/**\n * @param {ChildNodeArray} nodes\n * @returns {Text}\n */\nexport function claim_text(nodes, data) {\n\treturn claim_node(\n\t\tnodes,\n\t\t/** @returns {node is Text} */\n\t\t(node) => node.nodeType === 3,\n\t\t/** @param {Text} node */\n\t\t(node) => {\n\t\t\tconst dataStr = '' + data;\n\t\t\tif (node.data.startsWith(dataStr)) {\n\t\t\t\tif (node.data.length !== dataStr.length) {\n\t\t\t\t\treturn node.splitText(dataStr.length);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tnode.data = dataStr;\n\t\t\t}\n\t\t},\n\t\t() => text(data),\n\t\ttrue // Text nodes should not update last index since it is likely not worth it to eliminate an increasing subsequence of actual elements\n\t);\n}\n\n/**\n * @returns {Text} */\nexport function claim_space(nodes) {\n\treturn claim_text(nodes, ' ');\n}\n\n/**\n * @param {ChildNodeArray} nodes\n * @returns {Comment}\n */\nexport function claim_comment(nodes, data) {\n\treturn claim_node(\n\t\tnodes,\n\t\t/** @returns {node is Comment} */\n\t\t(node) => node.nodeType === 8,\n\t\t/** @param {Comment} node */\n\t\t(node) => {\n\t\t\tnode.data = '' + data;\n\t\t\treturn undefined;\n\t\t},\n\t\t() => comment(data),\n\t\ttrue\n\t);\n}\n\nfunction get_comment_idx(nodes, text, start) {\n\tfor (let i = start; i < nodes.length; i += 1) {\n\t\tconst node = nodes[i];\n\t\tif (node.nodeType === 8 /* comment node */ && node.textContent.trim() === text) {\n\t\t\treturn i;\n\t\t}\n\t}\n\treturn -1;\n}\n\n/**\n * @param {boolean} is_svg\n * @returns {HtmlTagHydration}\n */\nexport function claim_html_tag(nodes, is_svg) {\n\t// find html opening tag\n\tconst start_index = get_comment_idx(nodes, 'HTML_TAG_START', 0);\n\tconst end_index = get_comment_idx(nodes, 'HTML_TAG_END', start_index + 1);\n\tif (start_index === -1 || end_index === -1) {\n\t\treturn new HtmlTagHydration(is_svg);\n\t}\n\n\tinit_claim_info(nodes);\n\tconst html_tag_nodes = nodes.splice(start_index, end_index - start_index + 1);\n\tdetach(html_tag_nodes[0]);\n\tdetach(html_tag_nodes[html_tag_nodes.length - 1]);\n\tconst claimed_nodes = html_tag_nodes.slice(1, html_tag_nodes.length - 1);\n\tfor (const n of claimed_nodes) {\n\t\tn.claim_order = nodes.claim_info.total_claimed;\n\t\tnodes.claim_info.total_claimed += 1;\n\t}\n\treturn new HtmlTagHydration(is_svg, claimed_nodes);\n}\n\n/**\n * @param {Text} text\n * @param {unknown} data\n * @returns {void}\n */\nexport function set_data(text, data) {\n\tdata = '' + data;\n\tif (text.data === data) return;\n\ttext.data = /** @type {string} */ (data);\n}\n\n/**\n * @param {Text} text\n * @param {unknown} data\n * @returns {void}\n */\nexport function set_data_contenteditable(text, data) {\n\tdata = '' + data;\n\tif (text.wholeText === data) return;\n\ttext.data = /** @type {string} */ (data);\n}\n\n/**\n * @param {Text} text\n * @param {unknown} data\n * @param {string} attr_value\n * @returns {void}\n */\nexport function set_data_maybe_contenteditable(text, data, attr_value) {\n\tif (~contenteditable_truthy_values.indexOf(attr_value)) {\n\t\tset_data_contenteditable(text, data);\n\t} else {\n\t\tset_data(text, data);\n\t}\n}\n\n/**\n * @returns {void} */\nexport function set_input_value(input, value) {\n\tinput.value = value == null ? '' : value;\n}\n\n/**\n * @returns {void} */\nexport function set_input_type(input, type) {\n\ttry {\n\t\tinput.type = type;\n\t} catch (e) {\n\t\t// do nothing\n\t}\n}\n\n/**\n * @returns {void} */\nexport function set_style(node, key, value, important) {\n\tif (value == null) {\n\t\tnode.style.removeProperty(key);\n\t} else {\n\t\tnode.style.setProperty(key, value, important ? 'important' : '');\n\t}\n}\n\n/**\n * @returns {void} */\nexport function select_option(select, value, mounting) {\n\tfor (let i = 0; i < select.options.length; i += 1) {\n\t\tconst option = select.options[i];\n\t\tif (option.__value === value) {\n\t\t\toption.selected = true;\n\t\t\treturn;\n\t\t}\n\t}\n\tif (!mounting || value !== undefined) {\n\t\tselect.selectedIndex = -1; // no option should be selected\n\t}\n}\n\n/**\n * @returns {void} */\nexport function select_options(select, value) {\n\tfor (let i = 0; i < select.options.length; i += 1) {\n\t\tconst option = select.options[i];\n\t\toption.selected = ~value.indexOf(option.__value);\n\t}\n}\n\nexport function select_value(select) {\n\tconst selected_option = select.querySelector(':checked');\n\treturn selected_option && selected_option.__value;\n}\n\nexport function select_multiple_value(select) {\n\treturn [].map.call(select.querySelectorAll(':checked'), (option) => option.__value);\n}\n// unfortunately this can't be a constant as that wouldn't be tree-shakeable\n// so we cache the result instead\n\n/**\n * @type {boolean} */\nlet crossorigin;\n\n/**\n * @returns {boolean} */\nexport function is_crossorigin() {\n\tif (crossorigin === undefined) {\n\t\tcrossorigin = false;\n\t\ttry {\n\t\t\tif (typeof window !== 'undefined' && window.parent) {\n\t\t\t\tvoid window.parent.document;\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tcrossorigin = true;\n\t\t}\n\t}\n\treturn crossorigin;\n}\n\n/**\n * @param {HTMLElement} node\n * @param {() => void} fn\n * @returns {() => void}\n */\nexport function add_iframe_resize_listener(node, fn) {\n\tconst computed_style = getComputedStyle(node);\n\tif (computed_style.position === 'static') {\n\t\tnode.style.position = 'relative';\n\t}\n\tconst iframe = element('iframe');\n\tiframe.setAttribute(\n\t\t'style',\n\t\t'display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; ' +\n\t\t\t'overflow: hidden; border: 0; opacity: 0; pointer-events: none; z-index: -1;'\n\t);\n\tiframe.setAttribute('aria-hidden', 'true');\n\tiframe.tabIndex = -1;\n\tconst crossorigin = is_crossorigin();\n\n\t/**\n\t * @type {() => void}\n\t */\n\tlet unsubscribe;\n\tif (crossorigin) {\n\t\tiframe.src = \"data:text/html,<script>onresize=function(){parent.postMessage(0,'*')}</script>\";\n\t\tunsubscribe = listen(\n\t\t\twindow,\n\t\t\t'message',\n\t\t\t/** @param {MessageEvent} event */ (event) => {\n\t\t\t\tif (event.source === iframe.contentWindow) fn();\n\t\t\t}\n\t\t);\n\t} else {\n\t\tiframe.src = 'about:blank';\n\t\tiframe.onload = () => {\n\t\t\tunsubscribe = listen(iframe.contentWindow, 'resize', fn);\n\t\t\t// make sure an initial resize event is fired _after_ the iframe is loaded (which is asynchronous)\n\t\t\t// see https://github.com/sveltejs/svelte/issues/4233\n\t\t\tfn();\n\t\t};\n\t}\n\tappend(node, iframe);\n\treturn () => {\n\t\tif (crossorigin) {\n\t\t\tunsubscribe();\n\t\t} else if (unsubscribe && iframe.contentWindow) {\n\t\t\tunsubscribe();\n\t\t}\n\t\tdetach(iframe);\n\t};\n}\nexport const resize_observer_content_box = /* @__PURE__ */ new ResizeObserverSingleton({\n\tbox: 'content-box'\n});\nexport const resize_observer_border_box = /* @__PURE__ */ new ResizeObserverSingleton({\n\tbox: 'border-box'\n});\nexport const resize_observer_device_pixel_content_box = /* @__PURE__ */ new ResizeObserverSingleton(\n\t{ box: 'device-pixel-content-box' }\n);\nexport { ResizeObserverSingleton };\n\n/**\n * @returns {void} */\nexport function toggle_class(element, name, toggle) {\n\t// The `!!` is required because an `undefined` flag means flipping the current state.\n\telement.classList.toggle(name, !!toggle);\n}\n\n/**\n * @template T\n * @param {string} type\n * @param {T} [detail]\n * @param {{ bubbles?: boolean, cancelable?: boolean }} [options]\n * @returns {CustomEvent<T>}\n */\nexport function custom_event(type, detail, { bubbles = false, cancelable = false } = {}) {\n\treturn new CustomEvent(type, { detail, bubbles, cancelable });\n}\n\n/**\n * @param {string} selector\n * @param {HTMLElement} parent\n * @returns {ChildNodeArray}\n */\nexport function query_selector_all(selector, parent = document.body) {\n\treturn Array.from(parent.querySelectorAll(selector));\n}\n\n/**\n * @param {string} nodeId\n * @param {HTMLElement} head\n * @returns {any[]}\n */\nexport function head_selector(nodeId, head) {\n\tconst result = [];\n\tlet started = 0;\n\tfor (const node of head.childNodes) {\n\t\tif (node.nodeType === 8 /* comment node */) {\n\t\t\tconst comment = node.textContent.trim();\n\t\t\tif (comment === `HEAD_${nodeId}_END`) {\n\t\t\t\tstarted -= 1;\n\t\t\t\tresult.push(node);\n\t\t\t} else if (comment === `HEAD_${nodeId}_START`) {\n\t\t\t\tstarted += 1;\n\t\t\t\tresult.push(node);\n\t\t\t}\n\t\t} else if (started > 0) {\n\t\t\tresult.push(node);\n\t\t}\n\t}\n\treturn result;\n}\n/** */\nexport class HtmlTag {\n\t/**\n\t * @private\n\t * @default false\n\t */\n\tis_svg = false;\n\t/** parent for creating node */\n\te = undefined;\n\t/** html tag nodes */\n\tn = undefined;\n\t/** target */\n\tt = undefined;\n\t/** anchor */\n\ta = undefined;\n\tconstructor(is_svg = false) {\n\t\tthis.is_svg = is_svg;\n\t\tthis.e = this.n = null;\n\t}\n\n\t/**\n\t * @param {string} html\n\t * @returns {void}\n\t */\n\tc(html) {\n\t\tthis.h(html);\n\t}\n\n\t/**\n\t * @param {string} html\n\t * @param {HTMLElement | SVGElement} target\n\t * @param {HTMLElement | SVGElement} anchor\n\t * @returns {void}\n\t */\n\tm(html, target, anchor = null) {\n\t\tif (!this.e) {\n\t\t\tif (this.is_svg)\n\t\t\t\tthis.e = svg_element(/** @type {keyof SVGElementTagNameMap} */ (target.nodeName));\n\t\t\t/** #7364  target for <template> may be provided as #document-fragment(11) */ else\n\t\t\t\tthis.e = element(\n\t\t\t\t\t/** @type {keyof HTMLElementTagNameMap} */ (\n\t\t\t\t\t\ttarget.nodeType === 11 ? 'TEMPLATE' : target.nodeName\n\t\t\t\t\t)\n\t\t\t\t);\n\t\t\tthis.t =\n\t\t\t\ttarget.tagName !== 'TEMPLATE'\n\t\t\t\t\t? target\n\t\t\t\t\t: /** @type {HTMLTemplateElement} */ (target).content;\n\t\t\tthis.c(html);\n\t\t}\n\t\tthis.i(anchor);\n\t}\n\n\t/**\n\t * @param {string} html\n\t * @returns {void}\n\t */\n\th(html) {\n\t\tthis.e.innerHTML = html;\n\t\tthis.n = Array.from(\n\t\t\tthis.e.nodeName === 'TEMPLATE' ? this.e.content.childNodes : this.e.childNodes\n\t\t);\n\t}\n\n\t/**\n\t * @returns {void} */\n\ti(anchor) {\n\t\tfor (let i = 0; i < this.n.length; i += 1) {\n\t\t\tinsert(this.t, this.n[i], anchor);\n\t\t}\n\t}\n\n\t/**\n\t * @param {string} html\n\t * @returns {void}\n\t */\n\tp(html) {\n\t\tthis.d();\n\t\tthis.h(html);\n\t\tthis.i(this.a);\n\t}\n\n\t/**\n\t * @returns {void} */\n\td() {\n\t\tthis.n.forEach(detach);\n\t}\n}\n\nexport class HtmlTagHydration extends HtmlTag {\n\t/** @type {Element[]} hydration claimed nodes */\n\tl = undefined;\n\n\tconstructor(is_svg = false, claimed_nodes) {\n\t\tsuper(is_svg);\n\t\tthis.e = this.n = null;\n\t\tthis.l = claimed_nodes;\n\t}\n\n\t/**\n\t * @param {string} html\n\t * @returns {void}\n\t */\n\tc(html) {\n\t\tif (this.l) {\n\t\t\tthis.n = this.l;\n\t\t} else {\n\t\t\tsuper.c(html);\n\t\t}\n\t}\n\n\t/**\n\t * @returns {void} */\n\ti(anchor) {\n\t\tfor (let i = 0; i < this.n.length; i += 1) {\n\t\t\tinsert_hydration(this.t, this.n[i], anchor);\n\t\t}\n\t}\n}\n\n/**\n * @param {NamedNodeMap} attributes\n * @returns {{}}\n */\nexport function attribute_to_object(attributes) {\n\tconst result = {};\n\tfor (const attribute of attributes) {\n\t\tresult[attribute.name] = attribute.value;\n\t}\n\treturn result;\n}\n\n/**\n * @param {HTMLElement} element\n * @returns {{}}\n */\nexport function get_custom_elements_slots(element) {\n\tconst result = {};\n\telement.childNodes.forEach(\n\t\t/** @param {Element} node */ (node) => {\n\t\t\tresult[node.slot || 'default'] = true;\n\t\t}\n\t);\n\treturn result;\n}\n\nexport function construct_svelte_component(component, props) {\n\treturn new component(props);\n}\n\n/**\n * @typedef {Node & {\n * \tclaim_order?: number;\n * \thydrate_init?: true;\n * \tactual_end_child?: NodeEx;\n * \tchildNodes: NodeListOf<NodeEx>;\n * }} NodeEx\n */\n\n/** @typedef {ChildNode & NodeEx} ChildNodeEx */\n\n/** @typedef {NodeEx & { claim_order: number }} NodeEx2 */\n\n/**\n * @typedef {ChildNodeEx[] & {\n * \tclaim_info?: {\n * \t\tlast_index: number;\n * \t\ttotal_claimed: number;\n * \t};\n * }} ChildNodeArray\n */\n", "import { append_empty_stylesheet, detach, get_root_for_style } from './dom.js';\nimport { raf } from './environment.js';\n\n// we need to store the information for multiple documents because a Svelte application could also contain iframes\n// https://github.com/sveltejs/svelte/issues/3624\n/** @type {Map<Document | ShadowRoot, import('./private.d.ts').StyleInformation>} */\nconst managed_styles = new Map();\n\nlet active = 0;\n\n// https://github.com/darkskyapp/string-hash/blob/master/index.js\n/**\n * @param {string} str\n * @returns {number}\n */\nfunction hash(str) {\n\tlet hash = 5381;\n\tlet i = str.length;\n\twhile (i--) hash = ((hash << 5) - hash) ^ str.charCodeAt(i);\n\treturn hash >>> 0;\n}\n\n/**\n * @param {Document | ShadowRoot} doc\n * @param {Element & ElementCSSInlineStyle} node\n * @returns {{ stylesheet: any; rules: {}; }}\n */\nfunction create_style_information(doc, node) {\n\tconst info = { stylesheet: append_empty_stylesheet(node), rules: {} };\n\tmanaged_styles.set(doc, info);\n\treturn info;\n}\n\n/**\n * @param {Element & ElementCSSInlineStyle} node\n * @param {number} a\n * @param {number} b\n * @param {number} duration\n * @param {number} delay\n * @param {(t: number) => number} ease\n * @param {(t: number, u: number) => string} fn\n * @param {number} uid\n * @returns {string}\n */\nexport function create_rule(node, a, b, duration, delay, ease, fn, uid = 0) {\n\tconst step = 16.666 / duration;\n\tlet keyframes = '{\\n';\n\tfor (let p = 0; p <= 1; p += step) {\n\t\tconst t = a + (b - a) * ease(p);\n\t\tkeyframes += p * 100 + `%{${fn(t, 1 - t)}}\\n`;\n\t}\n\tconst rule = keyframes + `100% {${fn(b, 1 - b)}}\\n}`;\n\tconst name = `__svelte_${hash(rule)}_${uid}`;\n\tconst doc = get_root_for_style(node);\n\tconst { stylesheet, rules } = managed_styles.get(doc) || create_style_information(doc, node);\n\tif (!rules[name]) {\n\t\trules[name] = true;\n\t\tstylesheet.insertRule(`@keyframes ${name} ${rule}`, stylesheet.cssRules.length);\n\t}\n\tconst animation = node.style.animation || '';\n\tnode.style.animation = `${\n\t\tanimation ? `${animation}, ` : ''\n\t}${name} ${duration}ms linear ${delay}ms 1 both`;\n\tactive += 1;\n\treturn name;\n}\n\n/**\n * @param {Element & ElementCSSInlineStyle} node\n * @param {string} [name]\n * @returns {void}\n */\nexport function delete_rule(node, name) {\n\tconst previous = (node.style.animation || '').split(', ');\n\tconst next = previous.filter(\n\t\tname\n\t\t\t? (anim) => anim.indexOf(name) < 0 // remove specific animation\n\t\t\t: (anim) => anim.indexOf('__svelte') === -1 // remove all Svelte animations\n\t);\n\tconst deleted = previous.length - next.length;\n\tif (deleted) {\n\t\tnode.style.animation = next.join(', ');\n\t\tactive -= deleted;\n\t\tif (!active) clear_rules();\n\t}\n}\n\n/** @returns {void} */\nexport function clear_rules() {\n\traf(() => {\n\t\tif (active) return;\n\t\tmanaged_styles.forEach((info) => {\n\t\t\tconst { ownerNode } = info.stylesheet;\n\t\t\t// there is no ownerNode if it runs on jsdom.\n\t\t\tif (ownerNode) detach(ownerNode);\n\t\t});\n\t\tmanaged_styles.clear();\n\t});\n}\n", "import { custom_event } from './dom.js';\n\nexport let current_component;\n\n/** @returns {void} */\nexport function set_current_component(component) {\n\tcurrent_component = component;\n}\n\nexport function get_current_component() {\n\tif (!current_component) throw new Error('Function called outside component initialization');\n\treturn current_component;\n}\n\n/**\n * Schedules a callback to run immediately before the component is updated after any state change.\n *\n * The first time the callback runs will be before the initial `onMount`\n *\n * https://svelte.dev/docs/svelte#beforeupdate\n * @param {() => any} fn\n * @returns {void}\n */\nexport function beforeUpdate(fn) {\n\tget_current_component().$$.before_update.push(fn);\n}\n\n/**\n * The `onMount` function schedules a callback to run as soon as the component has been mounted to the DOM.\n * It must be called during the component's initialisation (but doesn't need to live *inside* the component;\n * it can be called from an external module).\n *\n * If a function is returned _synchronously_ from `onMount`, it will be called when the component is unmounted.\n *\n * `onMount` does not run inside a [server-side component](/docs#run-time-server-side-component-api).\n *\n * https://svelte.dev/docs/svelte#onmount\n * @template T\n * @param {() => import('./private.js').NotFunction<T> | Promise<import('./private.js').NotFunction<T>> | (() => any)} fn\n * @returns {void}\n */\nexport function onMount(fn) {\n\tget_current_component().$$.on_mount.push(fn);\n}\n\n/**\n * Schedules a callback to run immediately after the component has been updated.\n *\n * The first time the callback runs will be after the initial `onMount`\n *\n * https://svelte.dev/docs/svelte#afterupdate\n * @param {() => any} fn\n * @returns {void}\n */\nexport function afterUpdate(fn) {\n\tget_current_component().$$.after_update.push(fn);\n}\n\n/**\n * Schedules a callback to run immediately before the component is unmounted.\n *\n * Out of `onMount`, `beforeUpdate`, `afterUpdate` and `onDestroy`, this is the\n * only one that runs inside a server-side component.\n *\n * https://svelte.dev/docs/svelte#ondestroy\n * @param {() => any} fn\n * @returns {void}\n */\nexport function onDestroy(fn) {\n\tget_current_component().$$.on_destroy.push(fn);\n}\n\n/**\n * Creates an event dispatcher that can be used to dispatch [component events](/docs#template-syntax-component-directives-on-eventname).\n * Event dispatchers are functions that can take two arguments: `name` and `detail`.\n *\n * Component events created with `createEventDispatcher` create a\n * [CustomEvent](https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent).\n * These events do not [bubble](https://developer.mozilla.org/en-US/docs/Learn/JavaScript/Building_blocks/Events#Event_bubbling_and_capture).\n * The `detail` argument corresponds to the [CustomEvent.detail](https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent/detail)\n * property and can contain any type of data.\n *\n * The event dispatcher can be typed to narrow the allowed event names and the type of the `detail` argument:\n * ```ts\n * const dispatch = createEventDispatcher<{\n *  loaded: never; // does not take a detail argument\n *  change: string; // takes a detail argument of type string, which is required\n *  optional: number | null; // takes an optional detail argument of type number\n * }>();\n * ```\n *\n * https://svelte.dev/docs/svelte#createeventdispatcher\n * @template {Record<string, any>} [EventMap=any]\n * @returns {import('./public.js').EventDispatcher<EventMap>}\n */\nexport function createEventDispatcher() {\n\tconst component = get_current_component();\n\treturn (type, detail, { cancelable = false } = {}) => {\n\t\tconst callbacks = component.$$.callbacks[type];\n\t\tif (callbacks) {\n\t\t\t// TODO are there situations where events could be dispatched\n\t\t\t// in a server (non-DOM) environment?\n\t\t\tconst event = custom_event(/** @type {string} */ (type), detail, { cancelable });\n\t\t\tcallbacks.slice().forEach((fn) => {\n\t\t\t\tfn.call(component, event);\n\t\t\t});\n\t\t\treturn !event.defaultPrevented;\n\t\t}\n\t\treturn true;\n\t};\n}\n\n/**\n * Associates an arbitrary `context` object with the current component and the specified `key`\n * and returns that object. The context is then available to children of the component\n * (including slotted content) with `getContext`.\n *\n * Like lifecycle functions, this must be called during component initialisation.\n *\n * https://svelte.dev/docs/svelte#setcontext\n * @template T\n * @param {any} key\n * @param {T} context\n * @returns {T}\n */\nexport function setContext(key, context) {\n\tget_current_component().$$.context.set(key, context);\n\treturn context;\n}\n\n/**\n * Retrieves the context that belongs to the closest parent component with the specified `key`.\n * Must be called during component initialisation.\n *\n * https://svelte.dev/docs/svelte#getcontext\n * @template T\n * @param {any} key\n * @returns {T}\n */\nexport function getContext(key) {\n\treturn get_current_component().$$.context.get(key);\n}\n\n/**\n * Retrieves the whole context map that belongs to the closest parent component.\n * Must be called during component initialisation. Useful, for example, if you\n * programmatically create a component and want to pass the existing context to it.\n *\n * https://svelte.dev/docs/svelte#getallcontexts\n * @template {Map<any, any>} [T=Map<any, any>]\n * @returns {T}\n */\nexport function getAllContexts() {\n\treturn get_current_component().$$.context;\n}\n\n/**\n * Checks whether a given `key` has been set in the context of a parent component.\n * Must be called during component initialisation.\n *\n * https://svelte.dev/docs/svelte#hascontext\n * @param {any} key\n * @returns {boolean}\n */\nexport function hasContext(key) {\n\treturn get_current_component().$$.context.has(key);\n}\n\n// TODO figure out if we still want to support\n// shorthand events, or if we want to implement\n// a real bubbling mechanism\n/**\n * @param component\n * @param event\n * @returns {void}\n */\nexport function bubble(component, event) {\n\tconst callbacks = component.$$.callbacks[event.type];\n\tif (callbacks) {\n\t\t// @ts-ignore\n\t\tcallbacks.slice().forEach((fn) => fn.call(this, event));\n\t}\n}\n", "import { run_all } from './utils.js';\nimport { current_component, set_current_component } from './lifecycle.js';\n\nexport const dirty_components = [];\nexport const intros = { enabled: false };\nexport const binding_callbacks = [];\n\nlet render_callbacks = [];\n\nconst flush_callbacks = [];\n\nconst resolved_promise = /* @__PURE__ */ Promise.resolve();\n\nlet update_scheduled = false;\n\n/** @returns {void} */\nexport function schedule_update() {\n\tif (!update_scheduled) {\n\t\tupdate_scheduled = true;\n\t\tresolved_promise.then(flush);\n\t}\n}\n\n/** @returns {Promise<void>} */\nexport function tick() {\n\tschedule_update();\n\treturn resolved_promise;\n}\n\n/** @returns {void} */\nexport function add_render_callback(fn) {\n\trender_callbacks.push(fn);\n}\n\n/** @returns {void} */\nexport function add_flush_callback(fn) {\n\tflush_callbacks.push(fn);\n}\n\n// flush() calls callbacks in this order:\n// 1. All beforeUpdate callbacks, in order: parents before children\n// 2. All bind:this callbacks, in reverse order: children before parents.\n// 3. All afterUpdate callbacks, in order: parents before children. EXCEPT\n//    for afterUpdates called during the initial onMount, which are called in\n//    reverse order: children before parents.\n// Since callbacks might update component values, which could trigger another\n// call to flush(), the following steps guard against this:\n// 1. During beforeUpdate, any updated components will be added to the\n//    dirty_components array and will cause a reentrant call to flush(). Because\n//    the flush index is kept outside the function, the reentrant call will pick\n//    up where the earlier call left off and go through all dirty components. The\n//    current_component value is saved and restored so that the reentrant call will\n//    not interfere with the \"parent\" flush() call.\n// 2. bind:this callbacks cannot trigger new flush() calls.\n// 3. During afterUpdate, any updated components will NOT have their afterUpdate\n//    callback called a second time; the seen_callbacks set, outside the flush()\n//    function, guarantees this behavior.\nconst seen_callbacks = new Set();\n\nlet flushidx = 0; // Do *not* move this inside the flush() function\n\n/** @returns {void} */\nexport function flush() {\n\t// Do not reenter flush while dirty components are updated, as this can\n\t// result in an infinite loop. Instead, let the inner flush handle it.\n\t// Reentrancy is ok afterwards for bindings etc.\n\tif (flushidx !== 0) {\n\t\treturn;\n\t}\n\tconst saved_component = current_component;\n\tdo {\n\t\t// first, call beforeUpdate functions\n\t\t// and update components\n\t\ttry {\n\t\t\twhile (flushidx < dirty_components.length) {\n\t\t\t\tconst component = dirty_components[flushidx];\n\t\t\t\tflushidx++;\n\t\t\t\tset_current_component(component);\n\t\t\t\tupdate(component.$$);\n\t\t\t}\n\t\t} catch (e) {\n\t\t\t// reset dirty state to not end up in a deadlocked state and then rethrow\n\t\t\tdirty_components.length = 0;\n\t\t\tflushidx = 0;\n\t\t\tthrow e;\n\t\t}\n\t\tset_current_component(null);\n\t\tdirty_components.length = 0;\n\t\tflushidx = 0;\n\t\twhile (binding_callbacks.length) binding_callbacks.pop()();\n\t\t// then, once components are updated, call\n\t\t// afterUpdate functions. This may cause\n\t\t// subsequent updates...\n\t\tfor (let i = 0; i < render_callbacks.length; i += 1) {\n\t\t\tconst callback = render_callbacks[i];\n\t\t\tif (!seen_callbacks.has(callback)) {\n\t\t\t\t// ...so guard against infinite loops\n\t\t\t\tseen_callbacks.add(callback);\n\t\t\t\tcallback();\n\t\t\t}\n\t\t}\n\t\trender_callbacks.length = 0;\n\t} while (dirty_components.length);\n\twhile (flush_callbacks.length) {\n\t\tflush_callbacks.pop()();\n\t}\n\tupdate_scheduled = false;\n\tseen_callbacks.clear();\n\tset_current_component(saved_component);\n}\n\n/** @returns {void} */\nfunction update($$) {\n\tif ($$.fragment !== null) {\n\t\t$$.update();\n\t\trun_all($$.before_update);\n\t\tconst dirty = $$.dirty;\n\t\t$$.dirty = [-1];\n\t\t$$.fragment && $$.fragment.p($$.ctx, dirty);\n\t\t$$.after_update.forEach(add_render_callback);\n\t}\n}\n\n/**\n * Useful for example to execute remaining `afterUpdate` callbacks before executing `destroy`.\n * @param {Function[]} fns\n * @returns {void}\n */\nexport function flush_render_callbacks(fns) {\n\tconst filtered = [];\n\tconst targets = [];\n\trender_callbacks.forEach((c) => (fns.indexOf(c) === -1 ? filtered.push(c) : targets.push(c)));\n\ttargets.forEach((c) => c());\n\trender_callbacks = filtered;\n}\n", "import { identity as linear, is_function, noop, run_all } from './utils.js';\nimport { now } from './environment.js';\nimport { loop } from './loop.js';\nimport { create_rule, delete_rule } from './style_manager.js';\nimport { custom_event } from './dom.js';\nimport { add_render_callback } from './scheduler.js';\n\n/**\n * @type {Promise<void> | null}\n */\nlet promise;\n\n/**\n * @returns {Promise<void>}\n */\nfunction wait() {\n\tif (!promise) {\n\t\tpromise = Promise.resolve();\n\t\tpromise.then(() => {\n\t\t\tpromise = null;\n\t\t});\n\t}\n\treturn promise;\n}\n\n/**\n * @param {Element} node\n * @param {INTRO | OUTRO | boolean} direction\n * @param {'start' | 'end'} kind\n * @returns {void}\n */\nfunction dispatch(node, direction, kind) {\n\tnode.dispatchEvent(custom_event(`${direction ? 'intro' : 'outro'}${kind}`));\n}\n\nconst outroing = new Set();\n\n/**\n * @type {Outro}\n */\nlet outros;\n\n/**\n * @returns {void} */\nexport function group_outros() {\n\toutros = {\n\t\tr: 0,\n\t\tc: [],\n\t\tp: outros // parent group\n\t};\n}\n\n/**\n * @returns {void} */\nexport function check_outros() {\n\tif (!outros.r) {\n\t\trun_all(outros.c);\n\t}\n\toutros = outros.p;\n}\n\n/**\n * @param {import('./private.js').Fragment} block\n * @param {0 | 1} [local]\n * @returns {void}\n */\nexport function transition_in(block, local) {\n\tif (block && block.i) {\n\t\toutroing.delete(block);\n\t\tblock.i(local);\n\t}\n}\n\n/**\n * @param {import('./private.js').Fragment} block\n * @param {0 | 1} local\n * @param {0 | 1} [detach]\n * @param {() => void} [callback]\n * @returns {void}\n */\nexport function transition_out(block, local, detach, callback) {\n\tif (block && block.o) {\n\t\tif (outroing.has(block)) return;\n\t\toutroing.add(block);\n\t\toutros.c.push(() => {\n\t\t\toutroing.delete(block);\n\t\t\tif (callback) {\n\t\t\t\tif (detach) block.d(1);\n\t\t\t\tcallback();\n\t\t\t}\n\t\t});\n\t\tblock.o(local);\n\t} else if (callback) {\n\t\tcallback();\n\t}\n}\n\n/**\n * @type {import('../transition/public.js').TransitionConfig}\n */\nconst null_transition = { duration: 0 };\n\n/**\n * @param {Element & ElementCSSInlineStyle} node\n * @param {TransitionFn} fn\n * @param {any} params\n * @returns {{ start(): void; invalidate(): void; end(): void; }}\n */\nexport function create_in_transition(node, fn, params) {\n\t/**\n\t * @type {TransitionOptions} */\n\tconst options = { direction: 'in' };\n\tlet config = fn(node, params, options);\n\tlet running = false;\n\tlet animation_name;\n\tlet task;\n\tlet uid = 0;\n\n\t/**\n\t * @returns {void} */\n\tfunction cleanup() {\n\t\tif (animation_name) delete_rule(node, animation_name);\n\t}\n\n\t/**\n\t * @returns {void} */\n\tfunction go() {\n\t\tconst {\n\t\t\tdelay = 0,\n\t\t\tduration = 300,\n\t\t\teasing = linear,\n\t\t\ttick = noop,\n\t\t\tcss\n\t\t} = config || null_transition;\n\t\tif (css) animation_name = create_rule(node, 0, 1, duration, delay, easing, css, uid++);\n\t\ttick(0, 1);\n\t\tconst start_time = now() + delay;\n\t\tconst end_time = start_time + duration;\n\t\tif (task) task.abort();\n\t\trunning = true;\n\t\tadd_render_callback(() => dispatch(node, true, 'start'));\n\t\ttask = loop((now) => {\n\t\t\tif (running) {\n\t\t\t\tif (now >= end_time) {\n\t\t\t\t\ttick(1, 0);\n\t\t\t\t\tdispatch(node, true, 'end');\n\t\t\t\t\tcleanup();\n\t\t\t\t\treturn (running = false);\n\t\t\t\t}\n\t\t\t\tif (now >= start_time) {\n\t\t\t\t\tconst t = easing((now - start_time) / duration);\n\t\t\t\t\ttick(t, 1 - t);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn running;\n\t\t});\n\t}\n\tlet started = false;\n\treturn {\n\t\tstart() {\n\t\t\tif (started) return;\n\t\t\tstarted = true;\n\t\t\tdelete_rule(node);\n\t\t\tif (is_function(config)) {\n\t\t\t\tconfig = config(options);\n\t\t\t\twait().then(go);\n\t\t\t} else {\n\t\t\t\tgo();\n\t\t\t}\n\t\t},\n\t\tinvalidate() {\n\t\t\tstarted = false;\n\t\t},\n\t\tend() {\n\t\t\tif (running) {\n\t\t\t\tcleanup();\n\t\t\t\trunning = false;\n\t\t\t}\n\t\t}\n\t};\n}\n\n/**\n * @param {Element & ElementCSSInlineStyle} node\n * @param {TransitionFn} fn\n * @param {any} params\n * @returns {{ end(reset: any): void; }}\n */\nexport function create_out_transition(node, fn, params) {\n\t/** @type {TransitionOptions} */\n\tconst options = { direction: 'out' };\n\tlet config = fn(node, params, options);\n\tlet running = true;\n\tlet animation_name;\n\tconst group = outros;\n\tgroup.r += 1;\n\t/** @type {boolean} */\n\tlet original_inert_value;\n\n\t/**\n\t * @returns {void} */\n\tfunction go() {\n\t\tconst {\n\t\t\tdelay = 0,\n\t\t\tduration = 300,\n\t\t\teasing = linear,\n\t\t\ttick = noop,\n\t\t\tcss\n\t\t} = config || null_transition;\n\n\t\tif (css) animation_name = create_rule(node, 1, 0, duration, delay, easing, css);\n\n\t\tconst start_time = now() + delay;\n\t\tconst end_time = start_time + duration;\n\t\tadd_render_callback(() => dispatch(node, false, 'start'));\n\n\t\tif ('inert' in node) {\n\t\t\toriginal_inert_value = /** @type {HTMLElement} */ (node).inert;\n\t\t\tnode.inert = true;\n\t\t}\n\n\t\tloop((now) => {\n\t\t\tif (running) {\n\t\t\t\tif (now >= end_time) {\n\t\t\t\t\ttick(0, 1);\n\t\t\t\t\tdispatch(node, false, 'end');\n\t\t\t\t\tif (!--group.r) {\n\t\t\t\t\t\t// this will result in `end()` being called,\n\t\t\t\t\t\t// so we don't need to clean up here\n\t\t\t\t\t\trun_all(group.c);\n\t\t\t\t\t}\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tif (now >= start_time) {\n\t\t\t\t\tconst t = easing((now - start_time) / duration);\n\t\t\t\t\ttick(1 - t, t);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn running;\n\t\t});\n\t}\n\n\tif (is_function(config)) {\n\t\twait().then(() => {\n\t\t\t// @ts-ignore\n\t\t\tconfig = config(options);\n\t\t\tgo();\n\t\t});\n\t} else {\n\t\tgo();\n\t}\n\n\treturn {\n\t\tend(reset) {\n\t\t\tif (reset && 'inert' in node) {\n\t\t\t\tnode.inert = original_inert_value;\n\t\t\t}\n\t\t\tif (reset && config.tick) {\n\t\t\t\tconfig.tick(1, 0);\n\t\t\t}\n\t\t\tif (running) {\n\t\t\t\tif (animation_name) delete_rule(node, animation_name);\n\t\t\t\trunning = false;\n\t\t\t}\n\t\t}\n\t};\n}\n\n/**\n * @param {Element & ElementCSSInlineStyle} node\n * @param {TransitionFn} fn\n * @param {any} params\n * @param {boolean} intro\n * @returns {{ run(b: 0 | 1): void; end(): void; }}\n */\nexport function create_bidirectional_transition(node, fn, params, intro) {\n\t/**\n\t * @type {TransitionOptions} */\n\tconst options = { direction: 'both' };\n\tlet config = fn(node, params, options);\n\tlet t = intro ? 0 : 1;\n\n\t/**\n\t * @type {Program | null} */\n\tlet running_program = null;\n\n\t/**\n\t * @type {PendingProgram | null} */\n\tlet pending_program = null;\n\tlet animation_name = null;\n\n\t/** @type {boolean} */\n\tlet original_inert_value;\n\n\t/**\n\t * @returns {void} */\n\tfunction clear_animation() {\n\t\tif (animation_name) delete_rule(node, animation_name);\n\t}\n\n\t/**\n\t * @param {PendingProgram} program\n\t * @param {number} duration\n\t * @returns {Program}\n\t */\n\tfunction init(program, duration) {\n\t\tconst d = /** @type {Program['d']} */ (program.b - t);\n\t\tduration *= Math.abs(d);\n\t\treturn {\n\t\t\ta: t,\n\t\t\tb: program.b,\n\t\t\td,\n\t\t\tduration,\n\t\t\tstart: program.start,\n\t\t\tend: program.start + duration,\n\t\t\tgroup: program.group\n\t\t};\n\t}\n\n\t/**\n\t * @param {INTRO | OUTRO} b\n\t * @returns {void}\n\t */\n\tfunction go(b) {\n\t\tconst {\n\t\t\tdelay = 0,\n\t\t\tduration = 300,\n\t\t\teasing = linear,\n\t\t\ttick = noop,\n\t\t\tcss\n\t\t} = config || null_transition;\n\n\t\t/**\n\t\t * @type {PendingProgram} */\n\t\tconst program = {\n\t\t\tstart: now() + delay,\n\t\t\tb\n\t\t};\n\n\t\tif (!b) {\n\t\t\t// @ts-ignore todo: improve typings\n\t\t\tprogram.group = outros;\n\t\t\toutros.r += 1;\n\t\t}\n\n\t\tif ('inert' in node) {\n\t\t\tif (b) {\n\t\t\t\tif (original_inert_value !== undefined) {\n\t\t\t\t\t// aborted/reversed outro — restore previous inert value\n\t\t\t\t\tnode.inert = original_inert_value;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\toriginal_inert_value = /** @type {HTMLElement} */ (node).inert;\n\t\t\t\tnode.inert = true;\n\t\t\t}\n\t\t}\n\n\t\tif (running_program || pending_program) {\n\t\t\tpending_program = program;\n\t\t} else {\n\t\t\t// if this is an intro, and there's a delay, we need to do\n\t\t\t// an initial tick and/or apply CSS animation immediately\n\t\t\tif (css) {\n\t\t\t\tclear_animation();\n\t\t\t\tanimation_name = create_rule(node, t, b, duration, delay, easing, css);\n\t\t\t}\n\t\t\tif (b) tick(0, 1);\n\t\t\trunning_program = init(program, duration);\n\t\t\tadd_render_callback(() => dispatch(node, b, 'start'));\n\t\t\tloop((now) => {\n\t\t\t\tif (pending_program && now > pending_program.start) {\n\t\t\t\t\trunning_program = init(pending_program, duration);\n\t\t\t\t\tpending_program = null;\n\t\t\t\t\tdispatch(node, running_program.b, 'start');\n\t\t\t\t\tif (css) {\n\t\t\t\t\t\tclear_animation();\n\t\t\t\t\t\tanimation_name = create_rule(\n\t\t\t\t\t\t\tnode,\n\t\t\t\t\t\t\tt,\n\t\t\t\t\t\t\trunning_program.b,\n\t\t\t\t\t\t\trunning_program.duration,\n\t\t\t\t\t\t\t0,\n\t\t\t\t\t\t\teasing,\n\t\t\t\t\t\t\tconfig.css\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (running_program) {\n\t\t\t\t\tif (now >= running_program.end) {\n\t\t\t\t\t\ttick((t = running_program.b), 1 - t);\n\t\t\t\t\t\tdispatch(node, running_program.b, 'end');\n\t\t\t\t\t\tif (!pending_program) {\n\t\t\t\t\t\t\t// we're done\n\t\t\t\t\t\t\tif (running_program.b) {\n\t\t\t\t\t\t\t\t// intro — we can tidy up immediately\n\t\t\t\t\t\t\t\tclear_animation();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// outro — needs to be coordinated\n\t\t\t\t\t\t\t\tif (!--running_program.group.r) run_all(running_program.group.c);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\trunning_program = null;\n\t\t\t\t\t} else if (now >= running_program.start) {\n\t\t\t\t\t\tconst p = now - running_program.start;\n\t\t\t\t\t\tt = running_program.a + running_program.d * easing(p / running_program.duration);\n\t\t\t\t\t\ttick(t, 1 - t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn !!(running_program || pending_program);\n\t\t\t});\n\t\t}\n\t}\n\treturn {\n\t\trun(b) {\n\t\t\tif (is_function(config)) {\n\t\t\t\twait().then(() => {\n\t\t\t\t\tconst opts = { direction: b ? 'in' : 'out' };\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tconfig = config(opts);\n\t\t\t\t\tgo(b);\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tgo(b);\n\t\t\t}\n\t\t},\n\t\tend() {\n\t\t\tclear_animation();\n\t\t\trunning_program = pending_program = null;\n\t\t}\n\t};\n}\n\n/** @typedef {1} INTRO */\n/** @typedef {0} OUTRO */\n/** @typedef {{ direction: 'in' | 'out' | 'both' }} TransitionOptions */\n/** @typedef {(node: Element, params: any, options: TransitionOptions) => import('../transition/public.js').TransitionConfig} TransitionFn */\n\n/**\n * @typedef {Object} Outro\n * @property {number} r\n * @property {Function[]} c\n * @property {Object} p\n */\n\n/**\n * @typedef {Object} PendingProgram\n * @property {number} start\n * @property {INTRO|OUTRO} b\n * @property {Outro} [group]\n */\n\n/**\n * @typedef {Object} Program\n * @property {number} a\n * @property {INTRO|OUTRO} b\n * @property {1|-1} d\n * @property {number} duration\n * @property {number} start\n * @property {number} end\n * @property {Outro} [group]\n */\n", "import { transition_in, transition_out } from './transitions.js';\nimport { run_all } from './utils.js';\n\n// general each functions:\n\nexport function ensure_array_like(array_like_or_iterator) {\n\treturn array_like_or_iterator?.length !== undefined\n\t\t? array_like_or_iterator\n\t\t: Array.from(array_like_or_iterator);\n}\n\n// keyed each functions:\n\n/** @returns {void} */\nexport function destroy_block(block, lookup) {\n\tblock.d(1);\n\tlookup.delete(block.key);\n}\n\n/** @returns {void} */\nexport function outro_and_destroy_block(block, lookup) {\n\ttransition_out(block, 1, 1, () => {\n\t\tlookup.delete(block.key);\n\t});\n}\n\n/** @returns {void} */\nexport function fix_and_destroy_block(block, lookup) {\n\tblock.f();\n\tdestroy_block(block, lookup);\n}\n\n/** @returns {void} */\nexport function fix_and_outro_and_destroy_block(block, lookup) {\n\tblock.f();\n\toutro_and_destroy_block(block, lookup);\n}\n\n/** @returns {any[]} */\nexport function update_keyed_each(\n\told_blocks,\n\tdirty,\n\tget_key,\n\tdynamic,\n\tctx,\n\tlist,\n\tlookup,\n\tnode,\n\tdestroy,\n\tcreate_each_block,\n\tnext,\n\tget_context\n) {\n\tlet o = old_blocks.length;\n\tlet n = list.length;\n\tlet i = o;\n\tconst old_indexes = {};\n\twhile (i--) old_indexes[old_blocks[i].key] = i;\n\tconst new_blocks = [];\n\tconst new_lookup = new Map();\n\tconst deltas = new Map();\n\tconst updates = [];\n\ti = n;\n\twhile (i--) {\n\t\tconst child_ctx = get_context(ctx, list, i);\n\t\tconst key = get_key(child_ctx);\n\t\tlet block = lookup.get(key);\n\t\tif (!block) {\n\t\t\tblock = create_each_block(key, child_ctx);\n\t\t\tblock.c();\n\t\t} else if (dynamic) {\n\t\t\t// defer updates until all the DOM shuffling is done\n\t\t\tupdates.push(() => block.p(child_ctx, dirty));\n\t\t}\n\t\tnew_lookup.set(key, (new_blocks[i] = block));\n\t\tif (key in old_indexes) deltas.set(key, Math.abs(i - old_indexes[key]));\n\t}\n\tconst will_move = new Set();\n\tconst did_move = new Set();\n\t/** @returns {void} */\n\tfunction insert(block) {\n\t\ttransition_in(block, 1);\n\t\tblock.m(node, next);\n\t\tlookup.set(block.key, block);\n\t\tnext = block.first;\n\t\tn--;\n\t}\n\twhile (o && n) {\n\t\tconst new_block = new_blocks[n - 1];\n\t\tconst old_block = old_blocks[o - 1];\n\t\tconst new_key = new_block.key;\n\t\tconst old_key = old_block.key;\n\t\tif (new_block === old_block) {\n\t\t\t// do nothing\n\t\t\tnext = new_block.first;\n\t\t\to--;\n\t\t\tn--;\n\t\t} else if (!new_lookup.has(old_key)) {\n\t\t\t// remove old block\n\t\t\tdestroy(old_block, lookup);\n\t\t\to--;\n\t\t} else if (!lookup.has(new_key) || will_move.has(new_key)) {\n\t\t\tinsert(new_block);\n\t\t} else if (did_move.has(old_key)) {\n\t\t\to--;\n\t\t} else if (deltas.get(new_key) > deltas.get(old_key)) {\n\t\t\tdid_move.add(new_key);\n\t\t\tinsert(new_block);\n\t\t} else {\n\t\t\twill_move.add(old_key);\n\t\t\to--;\n\t\t}\n\t}\n\twhile (o--) {\n\t\tconst old_block = old_blocks[o];\n\t\tif (!new_lookup.has(old_block.key)) destroy(old_block, lookup);\n\t}\n\twhile (n) insert(new_blocks[n - 1]);\n\trun_all(updates);\n\treturn new_blocks;\n}\n\n/** @returns {void} */\nexport function validate_each_keys(ctx, list, get_context, get_key) {\n\tconst keys = new Map();\n\tfor (let i = 0; i < list.length; i++) {\n\t\tconst key = get_key(get_context(ctx, list, i));\n\t\tif (keys.has(key)) {\n\t\t\tlet value = '';\n\t\t\ttry {\n\t\t\t\tvalue = `with value '${String(key)}' `;\n\t\t\t} catch (e) {\n\t\t\t\t// can't stringify\n\t\t\t}\n\t\t\tthrow new Error(\n\t\t\t\t`Cannot have duplicate keys in a keyed each: Keys at index ${keys.get(\n\t\t\t\t\tkey\n\t\t\t\t)} and ${i} ${value}are duplicates`\n\t\t\t);\n\t\t}\n\t\tkeys.set(key, i);\n\t}\n}\n", "/** @returns {{}} */\nexport function get_spread_update(levels, updates) {\n\tconst update = {};\n\tconst to_null_out = {};\n\tconst accounted_for = { $$scope: 1 };\n\tlet i = levels.length;\n\twhile (i--) {\n\t\tconst o = levels[i];\n\t\tconst n = updates[i];\n\t\tif (n) {\n\t\t\tfor (const key in o) {\n\t\t\t\tif (!(key in n)) to_null_out[key] = 1;\n\t\t\t}\n\t\t\tfor (const key in n) {\n\t\t\t\tif (!accounted_for[key]) {\n\t\t\t\t\tupdate[key] = n[key];\n\t\t\t\t\taccounted_for[key] = 1;\n\t\t\t\t}\n\t\t\t}\n\t\t\tlevels[i] = n;\n\t\t} else {\n\t\t\tfor (const key in o) {\n\t\t\t\taccounted_for[key] = 1;\n\t\t\t}\n\t\t}\n\t}\n\tfor (const key in to_null_out) {\n\t\tif (!(key in update)) update[key] = undefined;\n\t}\n\treturn update;\n}\n\nexport function get_spread_object(spread_props) {\n\treturn typeof spread_props === 'object' && spread_props !== null ? spread_props : {};\n}\n", "import {\n\tadd_render_callback,\n\tflush,\n\tflush_render_callbacks,\n\tschedule_update,\n\tdirty_components\n} from './scheduler.js';\nimport { current_component, set_current_component } from './lifecycle.js';\nimport { blank_object, is_empty, is_function, run, run_all, noop } from './utils.js';\nimport {\n\tchildren,\n\tdetach,\n\tstart_hydrating,\n\tend_hydrating,\n\tget_custom_elements_slots,\n\tinsert,\n\telement,\n\tattr\n} from './dom.js';\nimport { transition_in } from './transitions.js';\n\n/** @returns {void} */\nexport function bind(component, name, callback) {\n\tconst index = component.$$.props[name];\n\tif (index !== undefined) {\n\t\tcomponent.$$.bound[index] = callback;\n\t\tcallback(component.$$.ctx[index]);\n\t}\n}\n\n/** @returns {void} */\nexport function create_component(block) {\n\tblock && block.c();\n}\n\n/** @returns {void} */\nexport function claim_component(block, parent_nodes) {\n\tblock && block.l(parent_nodes);\n}\n\n/** @returns {void} */\nexport function mount_component(component, target, anchor) {\n\tconst { fragment, after_update } = component.$$;\n\tfragment && fragment.m(target, anchor);\n\t// onMount happens before the initial afterUpdate\n\tadd_render_callback(() => {\n\t\tconst new_on_destroy = component.$$.on_mount.map(run).filter(is_function);\n\t\t// if the component was destroyed immediately\n\t\t// it will update the `$$.on_destroy` reference to `null`.\n\t\t// the destructured on_destroy may still reference to the old array\n\t\tif (component.$$.on_destroy) {\n\t\t\tcomponent.$$.on_destroy.push(...new_on_destroy);\n\t\t} else {\n\t\t\t// Edge case - component was destroyed immediately,\n\t\t\t// most likely as a result of a binding initialising\n\t\t\trun_all(new_on_destroy);\n\t\t}\n\t\tcomponent.$$.on_mount = [];\n\t});\n\tafter_update.forEach(add_render_callback);\n}\n\n/** @returns {void} */\nexport function destroy_component(component, detaching) {\n\tconst $$ = component.$$;\n\tif ($$.fragment !== null) {\n\t\tflush_render_callbacks($$.after_update);\n\t\trun_all($$.on_destroy);\n\t\t$$.fragment && $$.fragment.d(detaching);\n\t\t// TODO null out other refs, including component.$$ (but need to\n\t\t// preserve final state?)\n\t\t$$.on_destroy = $$.fragment = null;\n\t\t$$.ctx = [];\n\t}\n}\n\n/** @returns {void} */\nfunction make_dirty(component, i) {\n\tif (component.$$.dirty[0] === -1) {\n\t\tdirty_components.push(component);\n\t\tschedule_update();\n\t\tcomponent.$$.dirty.fill(0);\n\t}\n\tcomponent.$$.dirty[(i / 31) | 0] |= 1 << i % 31;\n}\n\n/** @returns {void} */\nexport function init(\n\tcomponent,\n\toptions,\n\tinstance,\n\tcreate_fragment,\n\tnot_equal,\n\tprops,\n\tappend_styles,\n\tdirty = [-1]\n) {\n\tconst parent_component = current_component;\n\tset_current_component(component);\n\t/** @type {import('./private.js').T$$} */\n\tconst $$ = (component.$$ = {\n\t\tfragment: null,\n\t\tctx: [],\n\t\t// state\n\t\tprops,\n\t\tupdate: noop,\n\t\tnot_equal,\n\t\tbound: blank_object(),\n\t\t// lifecycle\n\t\ton_mount: [],\n\t\ton_destroy: [],\n\t\ton_disconnect: [],\n\t\tbefore_update: [],\n\t\tafter_update: [],\n\t\tcontext: new Map(options.context || (parent_component ? parent_component.$$.context : [])),\n\t\t// everything else\n\t\tcallbacks: blank_object(),\n\t\tdirty,\n\t\tskip_bound: false,\n\t\troot: options.target || parent_component.$$.root\n\t});\n\tappend_styles && append_styles($$.root);\n\tlet ready = false;\n\t$$.ctx = instance\n\t\t? instance(component, options.props || {}, (i, ret, ...rest) => {\n\t\t\t\tconst value = rest.length ? rest[0] : ret;\n\t\t\t\tif ($$.ctx && not_equal($$.ctx[i], ($$.ctx[i] = value))) {\n\t\t\t\t\tif (!$$.skip_bound && $$.bound[i]) $$.bound[i](value);\n\t\t\t\t\tif (ready) make_dirty(component, i);\n\t\t\t\t}\n\t\t\t\treturn ret;\n\t\t  })\n\t\t: [];\n\t$$.update();\n\tready = true;\n\trun_all($$.before_update);\n\t// `false` as a special case of no DOM component\n\t$$.fragment = create_fragment ? create_fragment($$.ctx) : false;\n\tif (options.target) {\n\t\tif (options.hydrate) {\n\t\t\tstart_hydrating();\n\t\t\tconst nodes = children(options.target);\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n\t\t\t$$.fragment && $$.fragment.l(nodes);\n\t\t\tnodes.forEach(detach);\n\t\t} else {\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n\t\t\t$$.fragment && $$.fragment.c();\n\t\t}\n\t\tif (options.intro) transition_in(component.$$.fragment);\n\t\tmount_component(component, options.target, options.anchor);\n\t\tend_hydrating();\n\t\tflush();\n\t}\n\tset_current_component(parent_component);\n}\n\nexport let SvelteElement;\n\nif (typeof HTMLElement === 'function') {\n\tSvelteElement = class extends HTMLElement {\n\t\t/** The Svelte component constructor */\n\t\t$$ctor;\n\t\t/** Slots */\n\t\t$$s;\n\t\t/** The Svelte component instance */\n\t\t$$c;\n\t\t/** Whether or not the custom element is connected */\n\t\t$$cn = false;\n\t\t/** Component props data */\n\t\t$$d = {};\n\t\t/** `true` if currently in the process of reflecting component props back to attributes */\n\t\t$$r = false;\n\t\t/** @type {Record<string, CustomElementPropDefinition>} Props definition (name, reflected, type etc) */\n\t\t$$p_d = {};\n\t\t/** @type {Record<string, Function[]>} Event listeners */\n\t\t$$l = {};\n\t\t/** @type {Map<Function, Function>} Event listener unsubscribe functions */\n\t\t$$l_u = new Map();\n\n\t\tconstructor($$componentCtor, $$slots, use_shadow_dom) {\n\t\t\tsuper();\n\t\t\tthis.$$ctor = $$componentCtor;\n\t\t\tthis.$$s = $$slots;\n\t\t\tif (use_shadow_dom) {\n\t\t\t\tthis.attachShadow({ mode: 'open' });\n\t\t\t}\n\t\t}\n\n\t\taddEventListener(type, listener, options) {\n\t\t\t// We can't determine upfront if the event is a custom event or not, so we have to\n\t\t\t// listen to both. If someone uses a custom event with the same name as a regular\n\t\t\t// browser event, this fires twice - we can't avoid that.\n\t\t\tthis.$$l[type] = this.$$l[type] || [];\n\t\t\tthis.$$l[type].push(listener);\n\t\t\tif (this.$$c) {\n\t\t\t\tconst unsub = this.$$c.$on(type, listener);\n\t\t\t\tthis.$$l_u.set(listener, unsub);\n\t\t\t}\n\t\t\tsuper.addEventListener(type, listener, options);\n\t\t}\n\n\t\tremoveEventListener(type, listener, options) {\n\t\t\tsuper.removeEventListener(type, listener, options);\n\t\t\tif (this.$$c) {\n\t\t\t\tconst unsub = this.$$l_u.get(listener);\n\t\t\t\tif (unsub) {\n\t\t\t\t\tunsub();\n\t\t\t\t\tthis.$$l_u.delete(listener);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tasync connectedCallback() {\n\t\t\tthis.$$cn = true;\n\t\t\tif (!this.$$c) {\n\t\t\t\t// We wait one tick to let possible child slot elements be created/mounted\n\t\t\t\tawait Promise.resolve();\n\t\t\t\tif (!this.$$cn) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tfunction create_slot(name) {\n\t\t\t\t\treturn () => {\n\t\t\t\t\t\tlet node;\n\t\t\t\t\t\tconst obj = {\n\t\t\t\t\t\t\tc: function create() {\n\t\t\t\t\t\t\t\tnode = element('slot');\n\t\t\t\t\t\t\t\tif (name !== 'default') {\n\t\t\t\t\t\t\t\t\tattr(node, 'name', name);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t/**\n\t\t\t\t\t\t\t * @param {HTMLElement} target\n\t\t\t\t\t\t\t * @param {HTMLElement} [anchor]\n\t\t\t\t\t\t\t */\n\t\t\t\t\t\t\tm: function mount(target, anchor) {\n\t\t\t\t\t\t\t\tinsert(target, node, anchor);\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\td: function destroy(detaching) {\n\t\t\t\t\t\t\t\tif (detaching) {\n\t\t\t\t\t\t\t\t\tdetach(node);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t};\n\t\t\t\t\t\treturn obj;\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tconst $$slots = {};\n\t\t\t\tconst existing_slots = get_custom_elements_slots(this);\n\t\t\t\tfor (const name of this.$$s) {\n\t\t\t\t\tif (name in existing_slots) {\n\t\t\t\t\t\t$$slots[name] = [create_slot(name)];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tfor (const attribute of this.attributes) {\n\t\t\t\t\t// this.$$data takes precedence over this.attributes\n\t\t\t\t\tconst name = this.$$g_p(attribute.name);\n\t\t\t\t\tif (!(name in this.$$d)) {\n\t\t\t\t\t\tthis.$$d[name] = get_custom_element_value(name, attribute.value, this.$$p_d, 'toProp');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.$$c = new this.$$ctor({\n\t\t\t\t\ttarget: this.shadowRoot || this,\n\t\t\t\t\tprops: {\n\t\t\t\t\t\t...this.$$d,\n\t\t\t\t\t\t$$slots,\n\t\t\t\t\t\t$$scope: {\n\t\t\t\t\t\t\tctx: []\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\t// Reflect component props as attributes\n\t\t\t\tconst reflect_attributes = () => {\n\t\t\t\t\tthis.$$r = true;\n\t\t\t\t\tfor (const key in this.$$p_d) {\n\t\t\t\t\t\tthis.$$d[key] = this.$$c.$$.ctx[this.$$c.$$.props[key]];\n\t\t\t\t\t\tif (this.$$p_d[key].reflect) {\n\t\t\t\t\t\t\tconst attribute_value = get_custom_element_value(\n\t\t\t\t\t\t\t\tkey,\n\t\t\t\t\t\t\t\tthis.$$d[key],\n\t\t\t\t\t\t\t\tthis.$$p_d,\n\t\t\t\t\t\t\t\t'toAttribute'\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tif (attribute_value == null) {\n\t\t\t\t\t\t\t\tthis.removeAttribute(key);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.setAttribute(this.$$p_d[key].attribute || key, attribute_value);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tthis.$$r = false;\n\t\t\t\t};\n\t\t\t\tthis.$$c.$$.after_update.push(reflect_attributes);\n\t\t\t\treflect_attributes(); // once initially because after_update is added too late for first render\n\n\t\t\t\tfor (const type in this.$$l) {\n\t\t\t\t\tfor (const listener of this.$$l[type]) {\n\t\t\t\t\t\tconst unsub = this.$$c.$on(type, listener);\n\t\t\t\t\t\tthis.$$l_u.set(listener, unsub);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.$$l = {};\n\t\t\t}\n\t\t}\n\n\t\t// We don't need this when working within Svelte code, but for compatibility of people using this outside of Svelte\n\t\t// and setting attributes through setAttribute etc, this is helpful\n\t\tattributeChangedCallback(attr, _oldValue, newValue) {\n\t\t\tif (this.$$r) return;\n\t\t\tattr = this.$$g_p(attr);\n\t\t\tthis.$$d[attr] = get_custom_element_value(attr, newValue, this.$$p_d, 'toProp');\n\t\t\tthis.$$c?.$set({ [attr]: this.$$d[attr] });\n\t\t}\n\n\t\tdisconnectedCallback() {\n\t\t\tthis.$$cn = false;\n\t\t\t// In a microtask, because this could be a move within the DOM\n\t\t\tPromise.resolve().then(() => {\n\t\t\t\tif (!this.$$cn) {\n\t\t\t\t\tthis.$$c.$destroy();\n\t\t\t\t\tthis.$$c = undefined;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\t$$g_p(attribute_name) {\n\t\t\treturn (\n\t\t\t\tObject.keys(this.$$p_d).find(\n\t\t\t\t\t(key) =>\n\t\t\t\t\t\tthis.$$p_d[key].attribute === attribute_name ||\n\t\t\t\t\t\t(!this.$$p_d[key].attribute && key.toLowerCase() === attribute_name)\n\t\t\t\t) || attribute_name\n\t\t\t);\n\t\t}\n\t};\n}\n\n/**\n * @param {string} prop\n * @param {any} value\n * @param {Record<string, CustomElementPropDefinition>} props_definition\n * @param {'toAttribute' | 'toProp'} [transform]\n */\nfunction get_custom_element_value(prop, value, props_definition, transform) {\n\tconst type = props_definition[prop]?.type;\n\tvalue = type === 'Boolean' && typeof value !== 'boolean' ? value != null : value;\n\tif (!transform || !props_definition[prop]) {\n\t\treturn value;\n\t} else if (transform === 'toAttribute') {\n\t\tswitch (type) {\n\t\t\tcase 'Object':\n\t\t\tcase 'Array':\n\t\t\t\treturn value == null ? null : JSON.stringify(value);\n\t\t\tcase 'Boolean':\n\t\t\t\treturn value ? '' : null;\n\t\t\tcase 'Number':\n\t\t\t\treturn value == null ? null : value;\n\t\t\tdefault:\n\t\t\t\treturn value;\n\t\t}\n\t} else {\n\t\tswitch (type) {\n\t\t\tcase 'Object':\n\t\t\tcase 'Array':\n\t\t\t\treturn value && JSON.parse(value);\n\t\t\tcase 'Boolean':\n\t\t\t\treturn value; // conversion already handled above\n\t\t\tcase 'Number':\n\t\t\t\treturn value != null ? +value : value;\n\t\t\tdefault:\n\t\t\t\treturn value;\n\t\t}\n\t}\n}\n\n/**\n * @internal\n *\n * Turn a Svelte component into a custom element.\n * @param {import('./public.js').ComponentType} Component  A Svelte component constructor\n * @param {Record<string, CustomElementPropDefinition>} props_definition  The props to observe\n * @param {string[]} slots  The slots to create\n * @param {string[]} accessors  Other accessors besides the ones for props the component has\n * @param {boolean} use_shadow_dom  Whether to use shadow DOM\n * @param {(ce: new () => HTMLElement) => new () => HTMLElement} [extend]\n */\nexport function create_custom_element(\n\tComponent,\n\tprops_definition,\n\tslots,\n\taccessors,\n\tuse_shadow_dom,\n\textend\n) {\n\tlet Class = class extends SvelteElement {\n\t\tconstructor() {\n\t\t\tsuper(Component, slots, use_shadow_dom);\n\t\t\tthis.$$p_d = props_definition;\n\t\t}\n\t\tstatic get observedAttributes() {\n\t\t\treturn Object.keys(props_definition).map((key) =>\n\t\t\t\t(props_definition[key].attribute || key).toLowerCase()\n\t\t\t);\n\t\t}\n\t};\n\tObject.keys(props_definition).forEach((prop) => {\n\t\tObject.defineProperty(Class.prototype, prop, {\n\t\t\tget() {\n\t\t\t\treturn this.$$c && prop in this.$$c ? this.$$c[prop] : this.$$d[prop];\n\t\t\t},\n\t\t\tset(value) {\n\t\t\t\tvalue = get_custom_element_value(prop, value, props_definition);\n\t\t\t\tthis.$$d[prop] = value;\n\t\t\t\tthis.$$c?.$set({ [prop]: value });\n\t\t\t}\n\t\t});\n\t});\n\taccessors.forEach((accessor) => {\n\t\tObject.defineProperty(Class.prototype, accessor, {\n\t\t\tget() {\n\t\t\t\treturn this.$$c?.[accessor];\n\t\t\t}\n\t\t});\n\t});\n\tif (extend) {\n\t\t// @ts-expect-error - assigning here is fine\n\t\tClass = extend(Class);\n\t}\n\tComponent.element = /** @type {any} */ (Class);\n\treturn Class;\n}\n\n/**\n * Base class for Svelte components. Used when dev=false.\n *\n * @template {Record<string, any>} [Props=any]\n * @template {Record<string, any>} [Events=any]\n */\nexport class SvelteComponent {\n\t/**\n\t * ### PRIVATE API\n\t *\n\t * Do not use, may change at any time\n\t *\n\t * @type {any}\n\t */\n\t$$ = undefined;\n\t/**\n\t * ### PRIVATE API\n\t *\n\t * Do not use, may change at any time\n\t *\n\t * @type {any}\n\t */\n\t$$set = undefined;\n\n\t/** @returns {void} */\n\t$destroy() {\n\t\tdestroy_component(this, 1);\n\t\tthis.$destroy = noop;\n\t}\n\n\t/**\n\t * @template {Extract<keyof Events, string>} K\n\t * @param {K} type\n\t * @param {((e: Events[K]) => void) | null | undefined} callback\n\t * @returns {() => void}\n\t */\n\t$on(type, callback) {\n\t\tif (!is_function(callback)) {\n\t\t\treturn noop;\n\t\t}\n\t\tconst callbacks = this.$$.callbacks[type] || (this.$$.callbacks[type] = []);\n\t\tcallbacks.push(callback);\n\t\treturn () => {\n\t\t\tconst index = callbacks.indexOf(callback);\n\t\t\tif (index !== -1) callbacks.splice(index, 1);\n\t\t};\n\t}\n\n\t/**\n\t * @param {Partial<Props>} props\n\t * @returns {void}\n\t */\n\t$set(props) {\n\t\tif (this.$$set && !is_empty(props)) {\n\t\t\tthis.$$.skip_bound = true;\n\t\t\tthis.$$set(props);\n\t\t\tthis.$$.skip_bound = false;\n\t\t}\n\t}\n}\n\n/**\n * @typedef {Object} CustomElementPropDefinition\n * @property {string} [attribute]\n * @property {boolean} [reflect]\n * @property {'String'|'Boolean'|'Number'|'Array'|'Object'} [type]\n */\n", "// generated during release, do not modify\n\n/**\n * The current version, as set in package.json.\n *\n * https://svelte.dev/docs/svelte-compiler#svelte-version\n * @type {string}\n */\nexport const VERSION = '4.2.0';\nexport const PUBLIC_VERSION = '4';\n", "import { PUBLIC_VERSION } from '../../../shared/version.js';\n\nif (typeof window !== 'undefined')\n\t// @ts-ignore\n\t(window.__svelte || (window.__svelte = { v: new Set() })).v.add(PUBLIC_VERSION);\n", "<h2><slot /></h2>\n\n<style>\n    h2 {\n        color: var(--body-text-color);\n        font-size: var(--fs-2);\n        font-weight: 600;\n        text-align: center;\n        margin: 0;\n        margin-block-start: var(--space-xs);\n    }\n</style>\n", "<p><slot /></p>\n\n<style>\n    p {\n        color: var(--body-text-color-variant);\n        font-size: var(--fs-1);\n        font-weight: 400;\n        text-align: center;\n        margin: 0;\n        overflow-wrap: anywhere;\n    }\n</style>\n", "const Alert = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\nclass=\"lucide lucide-alert-circle\"\n><circle cx=\"12\" cy=\"12\" r=\"10\" /><line x1=\"12\" x2=\"12\" y1=\"8\" y2=\"12\" /><line\n    x1=\"12\"\n    x2=\"12.01\"\n    y1=\"16\"\n    y2=\"16\"\n/></svg\n>\n`\n\nconst Check = `<svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    stroke-width=\"2\"\n    stroke-linecap=\"round\"\n    stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\" /></svg\n>\n`\n\nconst ChevronLeft = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"><polyline points=\"15 18 9 12 15 6\" /></svg\n>\n`\n\nconst ChevronRight = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"><polyline points=\"9 18 15 12 9 6\" /></svg\n>\n`\n\nconst Close = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\n><line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" /><line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" /></svg\n>\n`\n\nconst Copy = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\n><rect x=\"9\" y=\"9\" width=\"13\" height=\"13\" rx=\"2\" ry=\"2\" /><path\n    d=\"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1\"\n/></svg\n>\n`\n\nconst Error = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\nclass=\"lucide lucide-x-octagon\"\n><polygon\n    points=\"7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2\"\n/><line x1=\"15\" x2=\"9\" y1=\"9\" y2=\"15\" /><line x1=\"9\" x2=\"15\" y1=\"9\" y2=\"15\" /></svg\n>\n`\n\nconst ExternalLink = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\nclass=\"lucide lucide-external-link\"\n><path d=\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\" /><polyline\n    points=\"15 3 21 3 21 9\"\n/><line x1=\"10\" x2=\"21\" y1=\"14\" y2=\"3\" /></svg\n>\n`\n\nconst Expand = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\n><polyline points=\"15 3 21 3 21 9\" /><polyline points=\"9 21 3 21 3 15\" /><line\n    x1=\"21\"\n    y1=\"3\"\n    x2=\"14\"\n    y2=\"10\"\n/><line x1=\"3\" y1=\"21\" x2=\"10\" y2=\"14\" /></svg\n>\n`\n\nconst FileCode = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\n><path d=\"M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v4\" /><polyline\n    points=\"14 2 14 8 20 8\"\n/><path d=\"m9 18 3-3-3-3\" /><path d=\"m5 12-3 3 3 3\" /></svg\n>\n`\n\nconst Github = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\nclass=\"lucide lucide-github\"\n><path\n    d=\"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4\"\n/><path d=\"M9 18c-4.51 2-5-2-7-2\" /></svg\n>\n`\n\nconst Globe = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\nclass=\"lucide lucide-globe\"\n><circle cx=\"12\" cy=\"12\" r=\"10\" /><line x1=\"2\" x2=\"22\" y1=\"12\" y2=\"12\" /><path\n    d=\"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z\"\n/></svg\n>\n`\n\nconst Info = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\nclass=\"lucide lucide-info\"\n><circle cx=\"12\" cy=\"12\" r=\"10\" /><line x1=\"12\" x2=\"12\" y1=\"16\" y2=\"12\" /><line\n    x1=\"12\"\n    x2=\"12.01\"\n    y1=\"8\"\n    y2=\"8\"\n/></svg\n>\n`\n\nconst Login = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\n><path d=\"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4\" /><polyline\n    points=\"10 17 15 12 10 7\"\n/><line x1=\"15\" y1=\"12\" x2=\"3\" y2=\"12\" /></svg\n>\n`\n\nconst Settings = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\nclass=\"lucide lucide-settings-2\"\n><path d=\"M20 7h-9\" /><path d=\"M14 17H5\" /><circle cx=\"17\" cy=\"17\" r=\"3\" /><circle\n    cx=\"7\"\n    cy=\"7\"\n    r=\"3\"\n/></svg\n>\n`\n\nconst Signal = `<svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    stroke-width=\"2\"\n    stroke-linecap=\"round\"\n    stroke-linejoin=\"round\"\n    class=\"lucide lucide-radio\"\n    ><path d=\"M4.9 19.1C1 15.2 1 8.8 4.9 4.9\" /><path d=\"M7.8 16.2c-2.3-2.3-2.3-6.1 0-8.5\" /><circle\n        cx=\"12\"\n        cy=\"12\"\n        r=\"2\"\n    /><path d=\"M16.2 7.8c2.3 2.3 2.3 6.1 0 8.5\" /><path d=\"M19.1 4.9C23 8.8 23 15.1 19.1 19\" /></svg\n>\n`\n\nconst Theme = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\nclass=\"lucide lucide-sun-moon\"\n><path d=\"M12 16a4 4 0 1 0 0-8 4 4 0 0 0 0 8z\" /><path d=\"M12 8a2.828 2.828 0 1 0 4 4\" /><path\n    d=\"M12 2v2\"\n/><path d=\"M12 20v2\" /><path d=\"m4.93 4.93 1.41 1.41\" /><path d=\"m17.66 17.66 1.41 1.41\" /><path\n    d=\"M2 12h2\"\n/><path d=\"M20 12h2\" /><path d=\"m6.34 17.66-1.41 1.41\" /><path d=\"m19.07 4.93-1.41 1.41\" /></svg\n>\n`\n\nconst Wallet = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\n><path d=\"M20 12V8H6a2 2 0 0 1-2-2c0-1.1.9-2 2-2h12v4\" /><path\n    d=\"M4 6v12c0 1.1.9 2 2 2h14v-4\"\n/><path d=\"M18 12a2 2 0 0 0-2 2c0 1.1.9 2 2 2h4v-4h-4z\" /></svg\n>\n`\n\nconst Waves = `<svg\nxmlns=\"http://www.w3.org/2000/svg\"\nviewBox=\"0 0 24 24\"\nfill=\"none\"\nstroke=\"currentColor\"\nstroke-width=\"2\"\nstroke-linecap=\"round\"\nstroke-linejoin=\"round\"\nclass=\"lucide lucide-waves\"\n><path\n    d=\"M2 6c.6.5 1.2 1 2.5 1C7 7 7 5 9.5 5c2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1\"\n/><path\n    d=\"M2 12c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1\"\n/><path\n    d=\"M2 18c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1\"\n/></svg\n>\n`\n\nconst Wharf = `<svg width=\"36\" height=\"31\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"\n><path\n    d=\"M35.54 18.77c-.019.989-.673 1.676-1.319 2.048l-1.388.802c-.663.383-1.438.531-2.132.531-.695 0-1.47-.148-2.132-.531l-1.362-.802v2.722c0 1.008-.664 1.711-1.318 2.089l-1.389.801c-.663.383-1.437.531-2.132.531-.694 0-1.469-.148-2.131-.53l-2.035-1.175-2.034 1.174c-.663.383-1.438.531-2.132.531-.694 0-1.469-.148-2.131-.53l-9.722-5.613c-.645-.372-1.3-1.06-1.317-2.049v-.061 3.626c.018.989.672 1.676 1.317 2.049l9.722 5.612c.662.382 1.437.53 2.131.53.694 0 1.47-.148 2.132-.53l2.034-1.175 2.035 1.175c.662.382 1.437.53 2.131.53.695 0 1.47-.148 2.132-.53l1.389-.802c.654-.378 1.318-1.08 1.318-2.088v-2.722l1.362.801c.663.383 1.437.531 2.132.531.694 0 1.469-.148 2.132-.53l1.388-.802c.646-.373 1.3-1.06 1.318-2.05V18.77Z\"\n    fill=\"#7BE7CE\"\n/><path\n    d=\"M.874 15.362a1.5 1.5 0 0 0-.009.161v3.246c.019.99.673 1.677 1.318 2.05l9.722 5.612c.662.382 1.437.53 2.131.53.694 0 1.47-.148 2.132-.53l2.034-1.175 2.035 1.175c.662.382 1.437.53 2.131.53.695 0 1.47-.148 2.132-.53l1.389-.802c.654-.378 1.318-1.08 1.318-2.089V20.82l1.362.801c.663.383 1.437.531 2.132.531.694 0 1.469-.148 2.132-.53l1.388-.802c.646-.373 1.3-1.06 1.318-2.05v-3.564c-.018.99-.672 1.677-1.318 2.049l-1.388.802c-.663.382-1.438.53-2.132.53-.695 0-1.47-.148-2.132-.53l-1.388-.802a2.943 2.943 0 0 1-.067-.04 2.168 2.168 0 0 1 .085.834c.005.05.008.101.008.152v1.776c0 1.008-.664 1.71-1.318 2.088l-1.389.802c-.663.383-1.437.531-2.132.531-.694 0-1.469-.148-2.131-.53l-2.035-1.175-2.034 1.174c-.663.383-1.438.531-2.132.531-.694 0-1.469-.148-2.131-.53l-9.722-5.613c-.61-.352-1.229-.987-1.31-1.892ZM17.54 5.749l.004.043c.07.6.368 1.074.74 1.424a4.94 4.94 0 0 0-.75.045V5.875c0-.043.002-.085.006-.126Z\"\n    fill=\"#B2F2E1\"\n/><path\n    d=\"m3.573 10.152-1.389.801c-.618.358-1.246 1.006-1.312 1.928a1.364 1.364 0 0 0-.007.136V15.204c.019.99.673 1.677 1.318 2.05l9.722 5.611c.662.383 1.437.532 2.131.532.694 0 1.47-.149 2.132-.532l2.034-1.174 2.035 1.174c.662.383 1.437.532 2.131.532.695 0 1.47-.149 2.132-.532l1.389-.801c.654-.378 1.318-1.08 1.318-2.089v-1.775c0-.052-.003-.102-.008-.153a2.25 2.25 0 0 0-.085-.833l.067.04 1.388.801c.663.383 1.437.531 2.132.531.694 0 1.469-.148 2.132-.53l1.388-.802c.646-.373 1.3-1.06 1.318-2.05v-2.092a2.359 2.359 0 0 0 0-.142V8.29l.001-.058c0-1.009-.664-1.711-1.318-2.089L24.501.531C23.838.15 23.064 0 22.369 0c-.694 0-1.469.149-2.131.531l-1.389.802c-.654.377-1.318 1.08-1.318 2.088 0 .048.001.095.004.14v1.847a2.069 2.069 0 0 0 .01.385c.069.6.367 1.073.739 1.423h-.08c-.695 0-1.47.148-2.133.53l-1.388.802c-.654.378-1.318 1.08-1.318 2.089 0 .065.002.13.008.193a1.485 1.485 0 0 0-.002.073v1.682l-.003.041-.002.045a2.298 2.298 0 0 0 .021.405c.017.112.041.22.072.322l-5.623-3.246C7.174 9.769 6.4 9.62 5.705 9.62c-.695 0-1.47.148-2.132.53Z\"\n    fill=\"#F4FAF4\"\n/><path\n    d=\"M23.758 1.818c-.767-.442-2.01-.442-2.778 0l-1.389.802c-.766.443-.766 1.16 0 1.604l9.553 5.514c.369.213.575.501.575.802v.195c0 .3-.207.589-.575.801l-1.22.705c-.767.443-.767 1.16 0 1.603l1.389.802c.767.443 2.01.443 2.777 0l1.389-.802c.767-.443.767-1.16 0-1.603l-1.22-.705c-.369-.212-.576-.5-.576-.801v-.195c0-.3.207-.59.576-.802l1.22-.704c.767-.443.767-1.16 0-1.604l-9.721-5.612ZM7.093 11.439c-.767-.443-2.01-.443-2.777 0l-1.39.802c-.766.443-.766 1.16 0 1.603l9.722 5.612c.767.443 2.01.443 2.777 0l2.778-1.603-11.11-6.414Z\"\n    fill=\"#494E62\"\n/><path\n    d=\"M23.351 15.545c0 .3.207.589.575.801l1.22.705c.767.443.767 1.16 0 1.603l-1.388.802c-.767.443-2.01.443-2.778 0l-2.777-1.603 2.609-1.507c.368-.212.575-.5.575-.801v-.195c0-.3-.207-.589-.575-.801l-5.387-3.11c-.767-.443-.767-1.16 0-1.603l1.39-.802c.766-.443 2.01-.443 2.776 0l5.555 3.207c.767.443.767 1.16 0 1.603l-1.22.705c-.368.212-.575.5-.575.801v.195Z\"\n    fill=\"#494E62\"\n/></svg\n>\n`\n\nconst icons = {\n    copy: Copy,\n    check: Check,\n    close: Close,\n    'file-code': FileCode,\n    wharf: Wharf,\n    login: Login,\n    'chevron-right': ChevronRight,\n    'chevron-left': ChevronLeft,\n    wallet: Wallet,\n    expand: Expand,\n    signal: Signal,\n    settings: Settings,\n    globe: Globe,\n    github: Github,\n    info: Info,\n    theme: Theme,\n    waves: Waves,\n    'external-link': ExternalLink,\n    error: Error,\n    alert: Alert,\n}\n\nexport type Icon = keyof typeof icons\nexport default icons\n", "<script lang=\"ts\">\n    import icons from './icons.ts'\n    import type {Icon} from './icons.ts'\n    import type {PercentageString, Space} from '../../types'\n    export let name: Icon\n    export let size: Space | PercentageString = 'var(--space-l)'\n    export let color: string = 'currentColor'\n</script>\n\n<div style=\"width: {size}; display: grid; place-content: center; color: {color}\">\n    {@html icons[name]}\n</div>\n\n<style lang=\"scss\">\n    div :global(svg) {\n        width: 100%;\n        height: 100%;\n    }\n</style>\n", "<script lang=\"ts\">\n    import BodyTitle from './BodyTitle.svelte'\n    import BodyText from './BodyText.svelte'\n    import Icon from './Icon.svelte'\n    import type {Icon as IconType} from './icons.ts'\n    export let title: string | undefined = undefined\n    export let details: string | undefined = undefined\n    export let icon: IconType | undefined = undefined\n    export let iconColor: string = 'currentColor'\n</script>\n\n<div>\n    {#if icon}\n        <Icon name={icon} size=\"var(--space-3xl)\" color={iconColor} />\n    {/if}\n    {#if title}\n        <BodyTitle>{title}</BodyTitle>\n    {/if}\n    {#if details}\n        <BodyText>{details}</BodyText>\n    {/if}\n</div>\n\n<style lang=\"scss\">\n    div {\n        display: grid;\n        justify-items: center;\n        gap: var(--space-s);\n        text-align: center;\n        color: var(--body-text-color);\n        margin-block: var(--space-s);\n    }\n</style>\n", "<script lang=\"ts\">\n    import Message from './Message.svelte'\n    export let title: string\n    export let details: string\n</script>\n\n<Message {title} {details} icon=\"error\" iconColor=\"var(--color-error-2)\" />\n", "import {\n\trun_all,\n\tsubscribe,\n\tnoop,\n\tsafe_not_equal,\n\tis_function,\n\tget_store_value\n} from '../internal/index.js';\n\nconst subscriber_queue = [];\n\n/**\n * Creates a `Readable` store that allows reading by subscription.\n *\n * https://svelte.dev/docs/svelte-store#readable\n * @template T\n * @param {T} [value] initial value\n * @param {import('./public.js').StartStopNotifier<T>} [start]\n * @returns {import('./public.js').Readable<T>}\n */\nexport function readable(value, start) {\n\treturn {\n\t\tsubscribe: writable(value, start).subscribe\n\t};\n}\n\n/**\n * Create a `Writable` store that allows both updating and reading by subscription.\n *\n * https://svelte.dev/docs/svelte-store#writable\n * @template T\n * @param {T} [value] initial value\n * @param {import('./public.js').StartStopNotifier<T>} [start]\n * @returns {import('./public.js').Writable<T>}\n */\nexport function writable(value, start = noop) {\n\t/** @type {import('./public.js').Unsubscriber} */\n\tlet stop;\n\t/** @type {Set<import('./private.js').SubscribeInvalidateTuple<T>>} */\n\tconst subscribers = new Set();\n\t/** @param {T} new_value\n\t * @returns {void}\n\t */\n\tfunction set(new_value) {\n\t\tif (safe_not_equal(value, new_value)) {\n\t\t\tvalue = new_value;\n\t\t\tif (stop) {\n\t\t\t\t// store is ready\n\t\t\t\tconst run_queue = !subscriber_queue.length;\n\t\t\t\tfor (const subscriber of subscribers) {\n\t\t\t\t\tsubscriber[1]();\n\t\t\t\t\tsubscriber_queue.push(subscriber, value);\n\t\t\t\t}\n\t\t\t\tif (run_queue) {\n\t\t\t\t\tfor (let i = 0; i < subscriber_queue.length; i += 2) {\n\t\t\t\t\t\tsubscriber_queue[i][0](subscriber_queue[i + 1]);\n\t\t\t\t\t}\n\t\t\t\t\tsubscriber_queue.length = 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @param {import('./public.js').Updater<T>} fn\n\t * @returns {void}\n\t */\n\tfunction update(fn) {\n\t\tset(fn(value));\n\t}\n\n\t/**\n\t * @param {import('./public.js').Subscriber<T>} run\n\t * @param {import('./private.js').Invalidator<T>} [invalidate]\n\t * @returns {import('./public.js').Unsubscriber}\n\t */\n\tfunction subscribe(run, invalidate = noop) {\n\t\t/** @type {import('./private.js').SubscribeInvalidateTuple<T>} */\n\t\tconst subscriber = [run, invalidate];\n\t\tsubscribers.add(subscriber);\n\t\tif (subscribers.size === 1) {\n\t\t\tstop = start(set, update) || noop;\n\t\t}\n\t\trun(value);\n\t\treturn () => {\n\t\t\tsubscribers.delete(subscriber);\n\t\t\tif (subscribers.size === 0 && stop) {\n\t\t\t\tstop();\n\t\t\t\tstop = null;\n\t\t\t}\n\t\t};\n\t}\n\treturn { set, update, subscribe };\n}\n\n/**\n * Derived value store by synchronizing one or more readable stores and\n * applying an aggregation function over its input values.\n *\n * https://svelte.dev/docs/svelte-store#derived\n * @template {import('./private.js').Stores} S\n * @template T\n * @overload\n * @param {S} stores - input stores\n * @param {(values: import('./private.js').StoresValues<S>, set: (value: T) => void, update: (fn: import('./public.js').Updater<T>) => void) => import('./public.js').Unsubscriber | void} fn - function callback that aggregates the values\n * @param {T} [initial_value] - initial value\n * @returns {import('./public.js').Readable<T>}\n */\n\n/**\n * Derived value store by synchronizing one or more readable stores and\n * applying an aggregation function over its input values.\n *\n * https://svelte.dev/docs/svelte-store#derived\n * @template {import('./private.js').Stores} S\n * @template T\n * @overload\n * @param {S} stores - input stores\n * @param {(values: import('./private.js').StoresValues<S>) => T} fn - function callback that aggregates the values\n * @param {T} [initial_value] - initial value\n * @returns {import('./public.js').Readable<T>}\n */\n\n/**\n * @template {import('./private.js').Stores} S\n * @template T\n * @param {S} stores\n * @param {Function} fn\n * @param {T} [initial_value]\n * @returns {import('./public.js').Readable<T>}\n */\nexport function derived(stores, fn, initial_value) {\n\tconst single = !Array.isArray(stores);\n\t/** @type {Array<import('./public.js').Readable<any>>} */\n\tconst stores_array = single ? [stores] : stores;\n\tif (!stores_array.every(Boolean)) {\n\t\tthrow new Error('derived() expects stores as input, got a falsy value');\n\t}\n\tconst auto = fn.length < 2;\n\treturn readable(initial_value, (set, update) => {\n\t\tlet started = false;\n\t\tconst values = [];\n\t\tlet pending = 0;\n\t\tlet cleanup = noop;\n\t\tconst sync = () => {\n\t\t\tif (pending) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tcleanup();\n\t\t\tconst result = fn(single ? values[0] : values, set, update);\n\t\t\tif (auto) {\n\t\t\t\tset(result);\n\t\t\t} else {\n\t\t\t\tcleanup = is_function(result) ? result : noop;\n\t\t\t}\n\t\t};\n\t\tconst unsubscribers = stores_array.map((store, i) =>\n\t\t\tsubscribe(\n\t\t\t\tstore,\n\t\t\t\t(value) => {\n\t\t\t\t\tvalues[i] = value;\n\t\t\t\t\tpending &= ~(1 << i);\n\t\t\t\t\tif (started) {\n\t\t\t\t\t\tsync();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t() => {\n\t\t\t\t\tpending |= 1 << i;\n\t\t\t\t}\n\t\t\t)\n\t\t);\n\t\tstarted = true;\n\t\tsync();\n\t\treturn function stop() {\n\t\t\trun_all(unsubscribers);\n\t\t\tcleanup();\n\t\t\t// We need to set this to false because callbacks can still happen despite having unsubscribed:\n\t\t\t// Callbacks might already be placed in the queue which doesn't know it should no longer\n\t\t\t// invoke this derived store.\n\t\t\tstarted = false;\n\t\t};\n\t});\n}\n\n/**\n * Takes a store and returns a new one derived from the old one that is readable.\n *\n * https://svelte.dev/docs/svelte-store#readonly\n * @template T\n * @param {import('./public.js').Readable<T>} store  - store to make readonly\n * @returns {import('./public.js').Readable<T>}\n */\nexport function readonly(store) {\n\treturn {\n\t\tsubscribe: store.subscribe.bind(store)\n\t};\n}\n\nexport { get_store_value as get };\n", "import {\n    <PERSON>rowserL<PERSON>alStor<PERSON>,\n    Checksum256Type,\n    CreateAccountContext,\n    LoginContext,\n    PermissionLevelType,\n    PromptArgs,\n    TransactContext,\n    UserInterfaceAccountCreationResponse,\n    UserInterfaceLoginResponse,\n} from '@wharfkit/session'\nimport type {Theme, TransitionDirection} from '../types'\nimport {get, writable, Writable} from 'svelte/store'\n\n// Reset data in all stores\nexport function resetState() {\n    active.set(false)\n\n    router.set({...defaultUserInterfaceRouter})\n    props.set({...defaultUserInterfaceProps})\n    prompt.reset()\n\n    cancelablePromises.set([])\n    transactContext.set(undefined)\n\n    loginContext.set(undefined)\n    loginPromise.set(undefined)\n    loginResponse.set({...defaultLoginResponse})\n\n    accountCreationContext.set(undefined)\n    accountCreationPromise.set(undefined)\n    accountCreationResponse.set({...defaultAccountCreationResponse})\n\n    errorDetails.set(undefined)\n    backAction.set(undefined)\n    transitionDirection.set(undefined)\n}\n\n/** Whether or not the interface is active in the browser */\nexport const active = writable<boolean>(false)\n\n/** Whether or not the settings button should be visable/usable */\nexport const allowSettings = writable(false)\n\n/** Persistent settings svelte store */\nexport interface UserInterfaceSettings {\n    language: string\n    theme: Theme | undefined\n    animations: boolean\n}\n\nexport const defaultUserInterfaceSettings: UserInterfaceSettings = {\n    language: '',\n    theme: undefined,\n    animations: true,\n}\n\nexport function makeSettingsStore(data = defaultUserInterfaceSettings) {\n    const store = writable(data)\n    const {subscribe, set} = store\n\n    let storage\n    if (typeof localStorage !== 'undefined') {\n        storage = new BrowserLocalStorage('web.renderer')\n        storage.read('settings').then((existing) => {\n            if (existing) {\n                set(JSON.parse(existing))\n            }\n        })\n    }\n\n    return {\n        subscribe,\n        set: (n) => {\n            if (storage) {\n                storage.write('settings', JSON.stringify(n))\n            }\n            set(n)\n        },\n        update: (cb) => {\n            const updatedStore = cb(get(store))\n            if (storage) {\n                storage.write('settings', JSON.stringify(updatedStore))\n            }\n            set(updatedStore)\n        },\n    }\n}\n\nexport const settings: Writable<UserInterfaceSettings> = makeSettingsStore()\n\n/** The properties of the UserInterface */\nexport interface UserInterfaceProps {\n    error?: Error\n    title: string\n    subtitle?: string\n}\n\nexport const defaultUserInterfaceProps: UserInterfaceProps = {\n    title: 'Wharf',\n    subtitle: 'Status Message',\n}\n\nexport const props = writable<UserInterfaceProps>(defaultUserInterfaceProps)\n\n/** The router for the sections of the UserInterface */\nexport interface UserInterfaceRouter {\n    path: string\n    history: string[]\n}\n\nexport const defaultUserInterfaceRouter: UserInterfaceRouter = {\n    path: '',\n    history: [],\n}\n\nexport interface Router extends Writable<UserInterfaceRouter> {\n    back: () => void\n    push: (path: string) => void\n}\n\nexport const initRouter = (): Router => {\n    const {set, subscribe, update} = writable<UserInterfaceRouter>(defaultUserInterfaceRouter)\n    return {\n        // Method to go one back in history\n        back: () =>\n            update((current: UserInterfaceRouter) => ({\n                ...current,\n                path: current.history[current.history.length - 1],\n                history: current.history.slice(0, -1),\n            })),\n        // Push a new path on to history\n        push: (path: string) =>\n            update((current) => ({\n                ...current,\n                path,\n                history: [...current.history, current.path],\n            })),\n        set,\n        subscribe,\n        update,\n    }\n}\n\nexport const router = initRouter()\n\n/** Cancelable promises that the router needs to track in order to cancel on quit */\ntype CancelCallback = (reason: string, silent: boolean) => void\nexport const cancelablePromises = writable<CancelCallback[]>([])\n\nexport const transactContext = writable<TransactContext | undefined>(undefined)\n\nexport type UserInterfacePrompt = {\n    args: PromptArgs\n    reject: (error: Error) => void\n    resolve: (response: UserInterfaceLoginResponse) => void\n}\n\nexport interface Prompt extends Writable<UserInterfacePrompt | undefined> {\n    reset: () => void\n}\n\nexport const initPrompt = (): Prompt => {\n    const {set, subscribe, update} = writable<UserInterfacePrompt | undefined>(undefined)\n    return {\n        reset: () => set(undefined),\n        set,\n        subscribe,\n        update,\n    }\n}\n\nexport const prompt = initPrompt()\n\nexport interface UserInterfaceLoginData {\n    chainId?: Checksum256Type\n    permissionLevel?: PermissionLevelType\n    walletPluginIndex?: number\n}\n\nexport interface LoginPromise {\n    reject: (error: Error) => void\n    resolve: (response: UserInterfaceLoginResponse) => void\n}\n\nexport const defaultLoginResponse = {\n    chainId: undefined,\n    permissionLevel: undefined,\n    walletPluginIndex: undefined,\n}\n\nexport const loginContext = writable<LoginContext | undefined>(undefined)\nexport const loginPromise = writable<LoginPromise | undefined>(undefined)\nexport const loginResponse = writable<UserInterfaceLoginData>({...defaultLoginResponse})\n\n// Account Creation\n\nexport interface AccountCreationPromise {\n    reject: (error: Error) => void\n    resolve: (response: UserInterfaceAccountCreationResponse) => void\n}\n\nexport const defaultAccountCreationResponse: UserInterfaceAccountCreationResponse = {\n    chain: undefined,\n    pluginId: undefined,\n}\n\nexport const accountCreationContext = writable<CreateAccountContext | undefined>(undefined)\nexport const accountCreationResponse = writable<UserInterfaceAccountCreationResponse>({\n    ...defaultAccountCreationResponse,\n})\nexport const accountCreationPromise = writable<AccountCreationPromise | undefined>(undefined)\n\nexport const errorDetails = writable<string | undefined>(undefined)\n\nexport const backAction = writable<Function | undefined>(undefined)\n\nexport const transitionDirection = writable<TransitionDirection | undefined>(undefined)\n", "<script lang=\"ts\">\n    import {i18nType} from 'src/lib/translations'\n    import {getContext} from 'svelte'\n    import ErrorMessage from './components/ErrorMessage.svelte'\n    import {errorDetails} from './state'\n\n    const {t} = getContext<i18nType>('i18n')\n</script>\n\n<div class=\"error\">\n    {#if $errorDetails}\n        <ErrorMessage title={$t('error.title', {default: 'Error'})} details={$errorDetails} />\n    {/if}\n</div>\n", "<script lang=\"ts\">\n</script>\n\n<ul>\n    <slot />\n</ul>\n\n<style lang=\"scss\">\n    ul {\n        list-style: none;\n        padding: 0;\n        margin: 0;\n    }\n</style>\n", "<script lang=\"ts\">\n    import type {ComponentProps} from 'svelte'\n    import Icon from './Icon.svelte'\n\n    export let label: string | undefined = undefined\n    export let onClick: () => void = () => {}\n    export let leadingIcon: ComponentProps<Icon>['name'] | undefined = undefined\n    export let trailingIcon: ComponentProps<Icon>['name'] | undefined | null = 'chevron-right'\n    export let logo: string | undefined = undefined\n    export let value: string | undefined = undefined\n    export let link: string | undefined = undefined\n</script>\n\n<li>\n    <slot>\n        {#if !link}\n            <button on:click={onClick}>\n                <div class=\"leading\">\n                    {#if logo}\n                        <div class=\"logo\">\n                            <img src={logo} alt={`${label} logo`} />\n                        </div>\n                    {:else if leadingIcon}\n                        <div class=\"icon\">\n                            <Icon name={leadingIcon} />\n                        </div>\n                    {/if}\n                </div>\n\n                <span class=\"label\">{label}</span>\n\n                {#if value}\n                    <span class=\"value\">{value}</span>\n                {/if}\n\n                {#if trailingIcon}\n                    <div class=\"trailing\">\n                        <Icon name={trailingIcon} />\n                    </div>\n                {/if}\n            </button>\n        {:else}\n            <a href={link} target=\"_blank\" rel=\"noreferrer\">\n                <div class=\"leading\">\n                    {#if logo}\n                        <div class=\"logo\">\n                            <img src={logo} alt={`${label} logo`} />\n                        </div>\n                    {:else if leadingIcon}\n                        <div class=\"icon\">\n                            <Icon name={leadingIcon} />\n                        </div>\n                    {/if}\n                </div>\n\n                <span class=\"label\">{label}</span>\n\n                {#if value}\n                    <span class=\"value\">{value}</span>\n                {/if}\n\n                {#if trailingIcon}\n                    <div class=\"trailing\">\n                        <Icon name={trailingIcon} />\n                    </div>\n                {/if}\n            </a>\n        {/if}\n    </slot>\n</li>\n\n<style lang=\"scss\">\n    li {\n        display: flex;\n        height: calc(var(--space-l) * 2); // 48px\n        align-items: center;\n        color: var(--body-text-color);\n        font-size: var(--fs-1);\n        font-weight: 500;\n    }\n\n    li:not(:last-child) {\n        border-bottom: 1px solid var(--list-divider-color);\n    }\n\n    button,\n    a {\n        flex: 1;\n        height: 100%;\n        display: flex;\n        align-items: center;\n        cursor: pointer;\n        border: none;\n        background: none;\n        color: inherit;\n        font-size: inherit;\n        font-family: inherit;\n        font-weight: inherit;\n        margin: 0;\n        padding: 0;\n        padding-inline-start: var(--space-3xs);\n        text-decoration: none;\n    }\n\n    .leading {\n        inline-size: var(--space-xl);\n        block-size: var(--space-xl);\n        display: grid;\n        place-content: center;\n    }\n\n    .leading > * {\n        max-inline-size: 30px;\n        max-block-size: 30px;\n    }\n\n    .leading img {\n        width: 100%;\n        height: 100%;\n        object-fit: contain;\n    }\n\n    .trailing {\n        opacity: 0.2;\n        padding-inline-end: var(--space-s);\n    }\n\n    li:hover {\n        background: var(--list-item-background-color-hover);\n\n        & .trailing {\n            opacity: 1;\n        }\n    }\n\n    .label {\n        flex: 1;\n        text-align: start;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n        padding-inline-start: var(--space-xs);\n    }\n\n    .value {\n        font-weight: 400;\n        padding-inline-end: var(--space-xs);\n    }\n</style>\n", "import type {ChainDefinition, WalletPluginMetadata} from '@wharfkit/session'\nimport {settings} from '../ui/state'\nimport {get} from 'svelte/store'\nimport icons, {Icon} from '../ui/components/icons'\n\nexport function isUrlImage(str: string) {\n    return str.startsWith('http://') || str.startsWith('https://')\n}\n\nexport function isBase64Image(str: string) {\n    return str.startsWith('data:image/')\n}\n\nexport function isValidIcon(str: string): str is Icon {\n    return str in icons\n}\n\n// Returns a themed logo based on the wallet metadata and the current color scheme preference\nexport function getThemedLogo(\n    metadata: WalletPluginMetadata | ChainDefinition\n): string | undefined {\n    const {name, logo} = metadata\n    let {theme} = get(settings)\n    const oppositeTheme = theme === 'light' ? 'dark' : 'light'\n\n    if (!theme) {\n        // if no theme is set, use the system preference for logo\n        window.matchMedia('(prefers-color-scheme: dark)').matches\n            ? (theme = 'dark')\n            : (theme = 'light')\n    }\n\n    if (!logo) {\n        if ('getLogo' in metadata) {\n            return metadata.getLogo()?.[theme] ?? metadata.getLogo()?.[oppositeTheme]\n        }\n        console.warn(`${name} does not have a logo.`)\n        return\n    }\n\n    const image = logo[theme] ?? logo[oppositeTheme]\n\n    if (!isUrlImage(image.toString()) && !isBase64Image(image.toString())) {\n        console.warn(`${name} ${theme} logo is not a supported image format.`)\n        return\n    }\n\n    return image\n}\n\nexport function capitalize(str: string): string {\n    return str.charAt(0).toUpperCase() + str.slice(1)\n}\n", "<script lang=\"ts\">\n    import {createEventDispatcher, setContext} from 'svelte'\n    import {ChainDefinition, Checksum256} from '@wharfkit/session'\n    import List from '../components/List.svelte'\n    import ListItem from '../components/ListItem.svelte'\n    import BodyTitle from '../components/BodyTitle.svelte'\n    import {getThemedLogo} from '../../lib/utils'\n\n    export let chains: ChainDefinition[]\n    export let title: string\n\n    const dispatch = createEventDispatcher<{\n        select: Checksum256\n        cancel: void\n    }>()\n</script>\n\n{#if chains}\n    <section>\n        <BodyTitle>{title}</BodyTitle>\n        <List>\n            {#each chains as chain}\n                <ListItem\n                    label={chain.name}\n                    onClick={() => dispatch('select', chain.id)}\n                    leadingIcon=\"wharf\"\n                    logo={getThemedLogo(chain)}\n                />\n            {/each}\n        </List>\n    </section>\n{/if}\n\n<style lang=\"scss\">\n    section {\n        display: flex;\n        flex-direction: column;\n        gap: var(--space-s);\n    }\n</style>\n", "<script lang=\"ts\">\n    import type {ComponentProps} from 'svelte'\n    import Icon from './Icon.svelte'\n\n    type ButtonProps = {\n        label: string\n        icon?: ComponentProps<Icon>['name'] | undefined\n        onClick: () => void\n        variant: 'primary' | 'secondary' | 'outlined'\n        autofocus?: boolean\n    }\n\n    export let data: ButtonProps\n\n    const {label, icon, onClick, variant = 'primary', autofocus} = data\n</script>\n\n<button class=\"button {variant}\" on:click={onClick} {autofocus}>\n    {#if icon}\n        <Icon name={icon} />\n    {/if}\n\n    <span>{label}</span>\n</button>\n\n<style lang=\"scss\">\n    @use '../../styles/buttonStyles.css';\n</style>\n", "<script lang=\"ts\">\n    export let value: string\n    export let placeholder: string\n    export let onKeyup\n    export let autofocus: boolean = false\n    export let error: boolean = false\n</script>\n\n<input\n    class:error\n    {autofocus}\n    type=\"text\"\n    on:keyup|preventDefault={onKeyup}\n    bind:value\n    {placeholder}\n/>\n\n<style lang=\"scss\">\n    input {\n        box-sizing: border-box;\n        height: var(--space-2xl);\n        border-radius: var(--border-radius-inner);\n        border: 1px solid var(--input-border-color);\n        padding-inline: var(--space-m);\n        color: var(--body-text-color);\n        background-color: var(--body-background-color);\n        font-size: var(--fs-1);\n    }\n    input::placeholder {\n        font-size: var(--fs-1);\n        color: var(--input-placeholder-color);\n        font-style: italic;\n    }\n\n    input:hover {\n        // border: 2px solid var(--input-border-color-hover);\n        border: 1px solid transparent;\n        outline: 2px solid var(--input-border-color-hover);\n        background-color: var(--input-background-focus);\n    }\n    input:focus-within {\n        border: 1px solid transparent;\n        outline: 2px solid var(--input-border-color-focus);\n        background-color: var(--input-background-focus);\n    }\n    input.error {\n        border: 1px solid var(--error-color);\n        color: var(--error-color);\n    }\n    input.error:focus-within {\n        border: 1px solid transparent;\n        color: var(--body-text-color);\n    }\n</style>\n", "<script lang=\"ts\">\n    import Message from './Message.svelte'\n    export let title: string\n    export let details: string\n</script>\n\n<Message {title} {details} icon=\"alert\" />\n", "<script lang=\"ts\">\n    import {\n        APIClient,\n        Checksum256Type,\n        Name,\n        PermissionLevel,\n        UserInterfaceWalletPlugin,\n    } from '@wharfkit/session'\n    import {createEventDispatcher, getContext, onMount} from 'svelte'\n    import {writable} from 'svelte/store'\n\n    import {i18nType} from 'src/lib/translations'\n\n    import {GetAccountsByAuthorizers} from '../../interfaces'\n    import Button from '../components/Button.svelte'\n    import List from '../components/List.svelte'\n    import ListItem from '../components/ListItem.svelte'\n    import TextInput from '../components/TextInput.svelte'\n    import WarningMessage from '../components/WarningMessage.svelte'\n    import BodyTitle from '../components/BodyTitle.svelte'\n    import {errorDetails} from '../state'\n\n    const {t} = getContext<i18nType>('i18n')\n\n    export let chainId: Checksum256Type | undefined\n    export let client: APIClient\n    export let walletPlugin: UserInterfaceWalletPlugin\n    export let title: string\n\n    const dispatch = createEventDispatcher<{\n        select: PermissionLevel\n        cancel: void\n    }>()\n\n    let busy = writable(true)\n    let input: string = ''\n    let prevInput: string = ''\n    let accountName: Name | undefined\n    let accountNotFound: boolean = false\n    let permissions: PermissionLevel[] | undefined\n    let publicKey: string | undefined = walletPlugin.metadata.publicKey\n\n    onMount(async () => {\n        if (walletPlugin.config.requiresPermissionSelect) {\n            if (chainId && walletPlugin.retrievePublicKey) {\n                try {\n                    publicKey = String(await walletPlugin.retrievePublicKey(chainId))\n                } catch (error) {\n                    errorDetails.set(String(error))\n                    throw error\n                }\n            }\n            const response = await client.call<GetAccountsByAuthorizers>({\n                path: '/v1/chain/get_accounts_by_authorizers',\n                params: {\n                    keys: [publicKey],\n                },\n            })\n            busy.set(false)\n            permissions = response.accounts.map((account) =>\n                PermissionLevel.from(`${account.account_name}@${account.permission_name}`)\n            )\n        } else if (walletPlugin.config.requiresPermissionEntry) {\n            busy.set(false)\n            permissions = []\n        }\n    })\n\n    async function lookup() {\n        busy.set(true)\n        try {\n            const response = await client.v1.chain.get_account(input)\n            if (response.account_name.equals(input)) {\n                accountName = response.account_name\n                permissions = response.permissions.map((permission) =>\n                    PermissionLevel.from(`${response.account_name}@${permission.perm_name}`)\n                )\n            }\n            accountNotFound = false\n        } catch (error) {\n            accountNotFound = true\n        } finally {\n            prevInput = input\n            busy.set(false)\n        }\n    }\n\n    function handleKeyup(event: KeyboardEvent) {\n        if (event.code == 'Enter') {\n            event.preventDefault()\n            lookup()\n            return false\n        }\n    }\n</script>\n\n<section>\n    {#if $busy}\n        <p class=\"loading\">{$t('loading', {default: 'Loading...'})}</p>\n    {:else if permissions && permissions.length > 0}\n        <BodyTitle>{$t('login.select.account')}</BodyTitle>\n        <List>\n            {#each permissions as permission}\n                <ListItem\n                    label={String(permission)}\n                    onClick={() => dispatch('select', permission)}\n                />\n            {/each}\n        </List>\n    {:else if publicKey}\n        <BodyTitle>{$t('login.select.no_accounts')}</BodyTitle>\n        <WarningMessage\n            title=\"\"\n            details={$t('login.select.no_match', {\n                default: 'No accounts found matching {{publicKey}}',\n                publicKey: publicKey,\n            })}\n        />\n    {:else if !accountName}\n        <BodyTitle>{title}</BodyTitle>\n        <div class=\"input-group\">\n            <TextInput\n                onKeyup={handleKeyup}\n                bind:value={input}\n                placeholder=\"Account name\"\n                autofocus={!input}\n                error={accountNotFound && input === prevInput}\n            />\n            {#if accountNotFound}\n                <p class=\"error\">\n                    {$t('login.enter.not_found', {\n                        default: 'Unable to find account',\n                    })}\n                    {prevInput}\n                </p>\n            {/if}\n            <Button\n                data={{\n                    variant: 'primary',\n                    onClick: lookup,\n                    label: $t('login.enter.lookup', {\n                        default: 'Lookup Account',\n                    }),\n                }}\n            />\n        </div>\n    {/if}\n</section>\n\n<style lang=\"scss\">\n    section {\n        display: flex;\n        flex-direction: column;\n        gap: var(--space-s);\n    }\n\n    p.loading {\n        margin: 0;\n        text-align: center;\n        height: var(--space-4xl);\n    }\n\n    p.error {\n        margin: 0;\n        text-align: center;\n        color: var(--error-color);\n    }\n\n    .input-group {\n        display: flex;\n        flex-direction: column;\n        gap: var(--space-m);\n        margin-top: var(--space-s);\n    }\n</style>\n", "<script lang=\"ts\">\n    import {createEventDispatcher} from 'svelte'\n    import {UserInterfaceWalletPlugin} from '@wharfkit/session'\n    import List from '../components/List.svelte'\n    import ListItem from '../components/ListItem.svelte'\n    import BodyTitle from '../components/BodyTitle.svelte'\n    import {getThemedLogo} from '../../lib/utils'\n    export let wallets: UserInterfaceWalletPlugin[]\n    export let title: string\n\n    const dispatch = createEventDispatcher<{\n        select: number\n        cancel: void\n    }>()\n</script>\n\n{#if wallets}\n    <section>\n        <BodyTitle>{title}</BodyTitle>\n        <List>\n            {#each wallets as wallet, index}\n                <ListItem\n                    label={wallet.metadata.name}\n                    onClick={() => dispatch('select', index)}\n                    leadingIcon=\"wallet\"\n                    logo={getThemedLogo(wallet.metadata)}\n                />\n            {/each}\n        </List>\n    </section>\n{/if}\n\n<style lang=\"scss\">\n    section {\n        display: flex;\n        flex-direction: column;\n        gap: var(--space-s);\n    }\n\n    ul {\n        padding: 0;\n        margin: 0;\n        display: flex;\n        flex-direction: column;\n        gap: var(--space-m);\n    }\n\n    li {\n        flex: 1;\n        display: flex;\n    }\n\n    .logo {\n        display: grid;\n        place-content: center;\n    }\n</style>\n", "/**\n * @param {any} obj\n * @returns {boolean}\n */\nexport function is_date(obj) {\n\treturn Object.prototype.toString.call(obj) === '[object Date]';\n}\n", "/*\nAdapted from https://github.com/mattdesl\nDistributed under MIT License https://github.com/mattdesl/eases/blob/master/LICENSE.md\n*/\nexport { identity as linear } from '../internal/index.js';\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function backInOut(t) {\n\tconst s = 1.70158 * 1.525;\n\tif ((t *= 2) < 1) return 0.5 * (t * t * ((s + 1) * t - s));\n\treturn 0.5 * ((t -= 2) * t * ((s + 1) * t + s) + 2);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function backIn(t) {\n\tconst s = 1.70158;\n\treturn t * t * ((s + 1) * t - s);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function backOut(t) {\n\tconst s = 1.70158;\n\treturn --t * t * ((s + 1) * t + s) + 1;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function bounceOut(t) {\n\tconst a = 4.0 / 11.0;\n\tconst b = 8.0 / 11.0;\n\tconst c = 9.0 / 10.0;\n\tconst ca = 4356.0 / 361.0;\n\tconst cb = 35442.0 / 1805.0;\n\tconst cc = 16061.0 / 1805.0;\n\tconst t2 = t * t;\n\treturn t < a\n\t\t? 7.5625 * t2\n\t\t: t < b\n\t\t? 9.075 * t2 - 9.9 * t + 3.4\n\t\t: t < c\n\t\t? ca * t2 - cb * t + cc\n\t\t: 10.8 * t * t - 20.52 * t + 10.72;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function bounceInOut(t) {\n\treturn t < 0.5 ? 0.5 * (1.0 - bounceOut(1.0 - t * 2.0)) : 0.5 * bounceOut(t * 2.0 - 1.0) + 0.5;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function bounceIn(t) {\n\treturn 1.0 - bounceOut(1.0 - t);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function circInOut(t) {\n\tif ((t *= 2) < 1) return -0.5 * (Math.sqrt(1 - t * t) - 1);\n\treturn 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function circIn(t) {\n\treturn 1.0 - Math.sqrt(1.0 - t * t);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function circOut(t) {\n\treturn Math.sqrt(1 - --t * t);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function cubicInOut(t) {\n\treturn t < 0.5 ? 4.0 * t * t * t : 0.5 * Math.pow(2.0 * t - 2.0, 3.0) + 1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function cubicIn(t) {\n\treturn t * t * t;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function cubicOut(t) {\n\tconst f = t - 1.0;\n\treturn f * f * f + 1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function elasticInOut(t) {\n\treturn t < 0.5\n\t\t? 0.5 * Math.sin(((+13.0 * Math.PI) / 2) * 2.0 * t) * Math.pow(2.0, 10.0 * (2.0 * t - 1.0))\n\t\t: 0.5 *\n\t\t\t\tMath.sin(((-13.0 * Math.PI) / 2) * (2.0 * t - 1.0 + 1.0)) *\n\t\t\t\tMath.pow(2.0, -10.0 * (2.0 * t - 1.0)) +\n\t\t\t\t1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function elasticIn(t) {\n\treturn Math.sin((13.0 * t * Math.PI) / 2) * Math.pow(2.0, 10.0 * (t - 1.0));\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function elasticOut(t) {\n\treturn Math.sin((-13.0 * (t + 1.0) * Math.PI) / 2) * Math.pow(2.0, -10.0 * t) + 1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function expoInOut(t) {\n\treturn t === 0.0 || t === 1.0\n\t\t? t\n\t\t: t < 0.5\n\t\t? +0.5 * Math.pow(2.0, 20.0 * t - 10.0)\n\t\t: -0.5 * Math.pow(2.0, 10.0 - t * 20.0) + 1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function expoIn(t) {\n\treturn t === 0.0 ? t : Math.pow(2.0, 10.0 * (t - 1.0));\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function expoOut(t) {\n\treturn t === 1.0 ? t : 1.0 - Math.pow(2.0, -10.0 * t);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quadInOut(t) {\n\tt /= 0.5;\n\tif (t < 1) return 0.5 * t * t;\n\tt--;\n\treturn -0.5 * (t * (t - 2) - 1);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quadIn(t) {\n\treturn t * t;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quadOut(t) {\n\treturn -t * (t - 2.0);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quartInOut(t) {\n\treturn t < 0.5 ? +8.0 * Math.pow(t, 4.0) : -8.0 * Math.pow(t - 1.0, 4.0) + 1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quartIn(t) {\n\treturn Math.pow(t, 4.0);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quartOut(t) {\n\treturn Math.pow(t - 1.0, 3.0) * (1.0 - t) + 1.0;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quintInOut(t) {\n\tif ((t *= 2) < 1) return 0.5 * t * t * t * t * t;\n\treturn 0.5 * ((t -= 2) * t * t * t * t + 2);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quintIn(t) {\n\treturn t * t * t * t * t;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function quintOut(t) {\n\treturn --t * t * t * t * t + 1;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function sineInOut(t) {\n\treturn -0.5 * (Math.cos(Math.PI * t) - 1);\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function sineIn(t) {\n\tconst v = Math.cos(t * Math.PI * 0.5);\n\tif (Math.abs(v) < 1e-14) return 1;\n\telse return 1 - v;\n}\n\n/**\n * https://svelte.dev/docs/svelte-easing\n * @param {number} t\n * @returns {number}\n */\nexport function sineOut(t) {\n\treturn Math.sin((t * Math.PI) / 2);\n}\n", "import { writable } from '../store/index.js';\nimport { assign, loop, now } from '../internal/index.js';\nimport { linear } from '../easing/index.js';\nimport { is_date } from './utils.js';\n\n/** @returns {(t: any) => any} */\nfunction get_interpolator(a, b) {\n\tif (a === b || a !== a) return () => a;\n\tconst type = typeof a;\n\tif (type !== typeof b || Array.isArray(a) !== Array.isArray(b)) {\n\t\tthrow new Error('Cannot interpolate values of different type');\n\t}\n\tif (Array.isArray(a)) {\n\t\tconst arr = b.map((bi, i) => {\n\t\t\treturn get_interpolator(a[i], bi);\n\t\t});\n\t\treturn (t) => arr.map((fn) => fn(t));\n\t}\n\tif (type === 'object') {\n\t\tif (!a || !b) throw new Error('Object cannot be null');\n\t\tif (is_date(a) && is_date(b)) {\n\t\t\ta = a.getTime();\n\t\t\tb = b.getTime();\n\t\t\tconst delta = b - a;\n\t\t\treturn (t) => new Date(a + t * delta);\n\t\t}\n\t\tconst keys = Object.keys(b);\n\t\tconst interpolators = {};\n\t\tkeys.forEach((key) => {\n\t\t\tinterpolators[key] = get_interpolator(a[key], b[key]);\n\t\t});\n\t\treturn (t) => {\n\t\t\tconst result = {};\n\t\t\tkeys.forEach((key) => {\n\t\t\t\tresult[key] = interpolators[key](t);\n\t\t\t});\n\t\t\treturn result;\n\t\t};\n\t}\n\tif (type === 'number') {\n\t\tconst delta = b - a;\n\t\treturn (t) => a + t * delta;\n\t}\n\tthrow new Error(`Cannot interpolate ${type} values`);\n}\n\n/**\n * A tweened store in Svelte is a special type of store that provides smooth transitions between state values over time.\n *\n * https://svelte.dev/docs/svelte-motion#tweened\n * @template T\n * @param {T} [value]\n * @param {import('./private.js').TweenedOptions<T>} [defaults]\n * @returns {import('./public.js').Tweened<T>}\n */\nexport function tweened(value, defaults = {}) {\n\tconst store = writable(value);\n\t/** @type {import('../internal/private.js').Task} */\n\tlet task;\n\tlet target_value = value;\n\t/**\n\t * @param {T} new_value\n\t * @param {import('./private.js').TweenedOptions<T>} [opts]\n\t */\n\tfunction set(new_value, opts) {\n\t\tif (value == null) {\n\t\t\tstore.set((value = new_value));\n\t\t\treturn Promise.resolve();\n\t\t}\n\t\ttarget_value = new_value;\n\t\tlet previous_task = task;\n\t\tlet started = false;\n\t\tlet {\n\t\t\tdelay = 0,\n\t\t\tduration = 400,\n\t\t\teasing = linear,\n\t\t\tinterpolate = get_interpolator\n\t\t} = assign(assign({}, defaults), opts);\n\t\tif (duration === 0) {\n\t\t\tif (previous_task) {\n\t\t\t\tprevious_task.abort();\n\t\t\t\tprevious_task = null;\n\t\t\t}\n\t\t\tstore.set((value = target_value));\n\t\t\treturn Promise.resolve();\n\t\t}\n\t\tconst start = now() + delay;\n\t\tlet fn;\n\t\ttask = loop((now) => {\n\t\t\tif (now < start) return true;\n\t\t\tif (!started) {\n\t\t\t\tfn = interpolate(value, new_value);\n\t\t\t\tif (typeof duration === 'function') duration = duration(value, new_value);\n\t\t\t\tstarted = true;\n\t\t\t}\n\t\t\tif (previous_task) {\n\t\t\t\tprevious_task.abort();\n\t\t\t\tprevious_task = null;\n\t\t\t}\n\t\t\tconst elapsed = now - start;\n\t\t\tif (elapsed > /** @type {number} */ (duration)) {\n\t\t\t\tstore.set((value = new_value));\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t// @ts-ignore\n\t\t\tstore.set((value = fn(easing(elapsed / duration))));\n\t\t\treturn true;\n\t\t});\n\t\treturn task.promise;\n\t}\n\treturn {\n\t\tset,\n\t\tupdate: (fn, opts) => set(fn(target_value, value), opts),\n\t\tsubscribe: store.subscribe\n\t};\n}\n", "<script lang=\"ts\">\n    import {onDestroy} from 'svelte'\n    import {tweened} from 'svelte/motion'\n    import {cubicOut} from 'svelte/easing'\n    import {isBase64Image, isUrlImage, isValidIcon} from '../../lib/utils'\n    import Icon from './Icon.svelte'\n\n    export let data: {\n        label?: string\n        end?: string\n        logo?: string\n        loading?: boolean\n    } = {}\n\n    let {label, end, logo, loading = true} = data\n    let deadline: Date\n    let remaining: number\n    let timer: NodeJS.Timeout\n\n    $: animated = loading\n    let size = 100\n    let strokeWidth = 8\n    let offset = size / 2\n    let radius = offset - strokeWidth\n    let circumference = tweened(2 * Math.PI * radius, {\n        duration: 500,\n        easing: cubicOut,\n    })\n\n    $: {\n        if (timer) {\n            clearInterval(timer)\n        }\n\n        if (end) {\n            deadline = new Date(end)\n\n            timer = setInterval(() => {\n                remaining = new Date(deadline).getTime() - Date.now()\n                if (remaining <= 0) {\n                    clearInterval(timer)\n                    circumference.set(1000)\n                    loading = false\n                }\n            }, 200)\n        }\n    }\n\n    onDestroy(() => {\n        if (timer) {\n            clearInterval(timer)\n        }\n    })\n\n    function countdownFormat(date: Date) {\n        const timeLeft = date.getTime() - Date.now()\n        if (timeLeft > 0) {\n            return new Date(timeLeft).toISOString().slice(14, 19)\n        }\n        return '00:00'\n    }\n</script>\n\n<div class=\"loader\">\n    <svg class:animated width={size} height={size}>\n        <circle\n            class=\"track\"\n            cx={offset}\n            cy={offset}\n            r={radius}\n            stroke-width={strokeWidth - 1}\n            stroke-linecap=\"round\"\n            stroke-dasharray={$circumference}\n            stroke-dashoffset={0}\n            fill=\"none\"\n        />\n        <circle\n            class:animated\n            class=\"spinner\"\n            cx={offset}\n            cy={offset}\n            r={radius}\n            stroke-width={strokeWidth}\n            stroke-linecap=\"round\"\n            stroke-dasharray={$circumference}\n            fill=\"none\"\n            style=\"--radius: {radius}; --circumference: {$circumference};\"\n        />\n    </svg>\n\n    {#if logo}\n        <div class=\"logo\">\n            {#if isUrlImage(logo) || isBase64Image(logo)}\n                <img src={logo} alt={`loading logo`} />\n            {:else if isValidIcon(logo)}\n                <Icon name={logo} size=\"75%\" />\n            {/if}\n        </div>\n    {/if}\n\n    <div class=\"text\">\n        {#if label}\n            <p class=\"label\">{label}</p>\n        {/if}\n        {#if deadline}\n            {#key remaining}\n                <span class:label={!label}>{countdownFormat(deadline)}</span>\n            {/key}\n        {/if}\n    </div>\n</div>\n\n<style lang=\"scss\">\n    .loader {\n        position: relative;\n        display: grid;\n        place-items: center;\n        grid-template-areas: 'stack' 'text';\n        gap: var(--space-m);\n    }\n\n    .logo {\n        grid-area: stack;\n        place-self: center;\n        color: var(--body-text-color);\n        width: var(--space-2xl);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n\n        img {\n            width: 100%;\n            height: 100%;\n            object-fit: contain;\n        }\n\n        & > :global(svg) {\n            width: 35%;\n            height: 35%;\n        }\n    }\n\n    .text {\n        grid-area: text;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        gap: var(--space-4xs);\n\n        .label {\n            font-size: var(--fs-2);\n            font-weight: 500;\n            color: var(--body-text-color);\n            margin: 0;\n        }\n\n        span {\n            font-size: var(--fs-1);\n            font-variant-numeric: tabular-nums;\n            color: var(--body-text-color-variant);\n        }\n    }\n\n    svg {\n        grid-area: stack;\n        animation: 2.5s linear infinite svg-animation;\n    }\n\n    @keyframes svg-animation {\n        0% {\n            transform: rotateZ(0deg);\n        }\n        100% {\n            transform: rotateZ(360deg);\n        }\n    }\n\n    circle {\n        transform-origin: center;\n\n        &.spinner {\n            stroke: var(--loading-circle-color);\n        }\n\n        &.track {\n            stroke: var(--loading-circle-track-color);\n        }\n\n        &.animated {\n            animation: dash 2.1s ease-in-out both infinite;\n        }\n    }\n\n    @keyframes dash {\n        0% {\n            stroke-dashoffset: var(--circumference);\n            transform: rotate(0);\n        }\n        50%,\n        65% {\n            stroke-dashoffset: 70;\n            transform: rotate(90deg);\n        }\n        100% {\n            stroke-dashoffset: var(--circumference);\n            transform: rotate(360deg);\n        }\n    }\n</style>\n", "import { cubicOut, cubicInOut, linear } from '../easing/index.js';\nimport { assign, split_css_unit, is_function } from '../internal/index.js';\n\n/**\n * Animates a `blur` filter alongside an element's opacity.\n *\n * https://svelte.dev/docs/svelte-transition#blur\n * @param {Element} node\n * @param {import('./public').BlurParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function blur(\n\tnode,\n\t{ delay = 0, duration = 400, easing = cubicInOut, amount = 5, opacity = 0 } = {}\n) {\n\tconst style = getComputedStyle(node);\n\tconst target_opacity = +style.opacity;\n\tconst f = style.filter === 'none' ? '' : style.filter;\n\tconst od = target_opacity * (1 - opacity);\n\tconst [value, unit] = split_css_unit(amount);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (_t, u) => `opacity: ${target_opacity - od * u}; filter: ${f} blur(${u * value}${unit});`\n\t};\n}\n\n/**\n * Animates the opacity of an element from 0 to the current opacity for `in` transitions and from the current opacity to 0 for `out` transitions.\n *\n * https://svelte.dev/docs/svelte-transition#fade\n * @param {Element} node\n * @param {import('./public').FadeParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function fade(node, { delay = 0, duration = 400, easing = linear } = {}) {\n\tconst o = +getComputedStyle(node).opacity;\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (t) => `opacity: ${t * o}`\n\t};\n}\n\n/**\n * Animates the x and y positions and the opacity of an element. `in` transitions animate from the provided values, passed as parameters to the element's default values. `out` transitions animate from the element's default values to the provided values.\n *\n * https://svelte.dev/docs/svelte-transition#fly\n * @param {Element} node\n * @param {import('./public').FlyParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function fly(\n\tnode,\n\t{ delay = 0, duration = 400, easing = cubicOut, x = 0, y = 0, opacity = 0 } = {}\n) {\n\tconst style = getComputedStyle(node);\n\tconst target_opacity = +style.opacity;\n\tconst transform = style.transform === 'none' ? '' : style.transform;\n\tconst od = target_opacity * (1 - opacity);\n\tconst [xValue, xUnit] = split_css_unit(x);\n\tconst [yValue, yUnit] = split_css_unit(y);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (t, u) => `\n\t\t\ttransform: ${transform} translate(${(1 - t) * xValue}${xUnit}, ${(1 - t) * yValue}${yUnit});\n\t\t\topacity: ${target_opacity - od * u}`\n\t};\n}\n\n/**\n * Slides an element in and out.\n *\n * https://svelte.dev/docs/svelte-transition#slide\n * @param {Element} node\n * @param {import('./public').SlideParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function slide(node, { delay = 0, duration = 400, easing = cubicOut, axis = 'y' } = {}) {\n\tconst style = getComputedStyle(node);\n\tconst opacity = +style.opacity;\n\tconst primary_property = axis === 'y' ? 'height' : 'width';\n\tconst primary_property_value = parseFloat(style[primary_property]);\n\tconst secondary_properties = axis === 'y' ? ['top', 'bottom'] : ['left', 'right'];\n\tconst capitalized_secondary_properties = secondary_properties.map(\n\t\t(e) => `${e[0].toUpperCase()}${e.slice(1)}`\n\t);\n\tconst padding_start_value = parseFloat(style[`padding${capitalized_secondary_properties[0]}`]);\n\tconst padding_end_value = parseFloat(style[`padding${capitalized_secondary_properties[1]}`]);\n\tconst margin_start_value = parseFloat(style[`margin${capitalized_secondary_properties[0]}`]);\n\tconst margin_end_value = parseFloat(style[`margin${capitalized_secondary_properties[1]}`]);\n\tconst border_width_start_value = parseFloat(\n\t\tstyle[`border${capitalized_secondary_properties[0]}Width`]\n\t);\n\tconst border_width_end_value = parseFloat(\n\t\tstyle[`border${capitalized_secondary_properties[1]}Width`]\n\t);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (t) =>\n\t\t\t'overflow: hidden;' +\n\t\t\t`opacity: ${Math.min(t * 20, 1) * opacity};` +\n\t\t\t`${primary_property}: ${t * primary_property_value}px;` +\n\t\t\t`padding-${secondary_properties[0]}: ${t * padding_start_value}px;` +\n\t\t\t`padding-${secondary_properties[1]}: ${t * padding_end_value}px;` +\n\t\t\t`margin-${secondary_properties[0]}: ${t * margin_start_value}px;` +\n\t\t\t`margin-${secondary_properties[1]}: ${t * margin_end_value}px;` +\n\t\t\t`border-${secondary_properties[0]}-width: ${t * border_width_start_value}px;` +\n\t\t\t`border-${secondary_properties[1]}-width: ${t * border_width_end_value}px;`\n\t};\n}\n\n/**\n * Animates the opacity and scale of an element. `in` transitions animate from an element's current (default) values to the provided values, passed as parameters. `out` transitions animate from the provided values to an element's default values.\n *\n * https://svelte.dev/docs/svelte-transition#scale\n * @param {Element} node\n * @param {import('./public').ScaleParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function scale(\n\tnode,\n\t{ delay = 0, duration = 400, easing = cubicOut, start = 0, opacity = 0 } = {}\n) {\n\tconst style = getComputedStyle(node);\n\tconst target_opacity = +style.opacity;\n\tconst transform = style.transform === 'none' ? '' : style.transform;\n\tconst sd = 1 - start;\n\tconst od = target_opacity * (1 - opacity);\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (_t, u) => `\n\t\t\ttransform: ${transform} scale(${1 - sd * u});\n\t\t\topacity: ${target_opacity - od * u}\n\t\t`\n\t};\n}\n\n/**\n * Animates the stroke of an SVG element, like a snake in a tube. `in` transitions begin with the path invisible and draw the path to the screen over time. `out` transitions start in a visible state and gradually erase the path. `draw` only works with elements that have a `getTotalLength` method, like `<path>` and `<polyline>`.\n *\n * https://svelte.dev/docs/svelte-transition#draw\n * @param {SVGElement & { getTotalLength(): number }} node\n * @param {import('./public').DrawParams} [params]\n * @returns {import('./public').TransitionConfig}\n */\nexport function draw(node, { delay = 0, speed, duration, easing = cubicInOut } = {}) {\n\tlet len = node.getTotalLength();\n\tconst style = getComputedStyle(node);\n\tif (style.strokeLinecap !== 'butt') {\n\t\tlen += parseInt(style.strokeWidth);\n\t}\n\tif (duration === undefined) {\n\t\tif (speed === undefined) {\n\t\t\tduration = 800;\n\t\t} else {\n\t\t\tduration = len / speed;\n\t\t}\n\t} else if (typeof duration === 'function') {\n\t\tduration = duration(len);\n\t}\n\treturn {\n\t\tdelay,\n\t\tduration,\n\t\teasing,\n\t\tcss: (_, u) => `\n\t\t\tstroke-dasharray: ${len};\n\t\t\tstroke-dashoffset: ${u * len};\n\t\t`\n\t};\n}\n\n/**\n * The `crossfade` function creates a pair of [transitions](/docs#template-syntax-element-directives-transition-fn) called `send` and `receive`. When an element is 'sent', it looks for a corresponding element being 'received', and generates a transition that transforms the element to its counterpart's position and fades it out. When an element is 'received', the reverse happens. If there is no counterpart, the `fallback` transition is used.\n *\n * https://svelte.dev/docs/svelte-transition#crossfade\n * @param {import('./public').CrossfadeParams & {\n * \tfallback?: (node: Element, params: import('./public').CrossfadeParams, intro: boolean) => import('./public').TransitionConfig;\n * }} params\n * @returns {[(node: any, params: import('./public').CrossfadeParams & { key: any; }) => () => import('./public').TransitionConfig, (node: any, params: import('./public').CrossfadeParams & { key: any; }) => () => import('./public').TransitionConfig]}\n */\nexport function crossfade({ fallback, ...defaults }) {\n\t/** @type {Map<any, Element>} */\n\tconst to_receive = new Map();\n\t/** @type {Map<any, Element>} */\n\tconst to_send = new Map();\n\t/**\n\t * @param {Element} from_node\n\t * @param {Element} node\n\t * @param {import('./public').CrossfadeParams} params\n\t * @returns {import('./public').TransitionConfig}\n\t */\n\tfunction crossfade(from_node, node, params) {\n\t\tconst {\n\t\t\tdelay = 0,\n\t\t\tduration = (d) => Math.sqrt(d) * 30,\n\t\t\teasing = cubicOut\n\t\t} = assign(assign({}, defaults), params);\n\t\tconst from = from_node.getBoundingClientRect();\n\t\tconst to = node.getBoundingClientRect();\n\t\tconst dx = from.left - to.left;\n\t\tconst dy = from.top - to.top;\n\t\tconst dw = from.width / to.width;\n\t\tconst dh = from.height / to.height;\n\t\tconst d = Math.sqrt(dx * dx + dy * dy);\n\t\tconst style = getComputedStyle(node);\n\t\tconst transform = style.transform === 'none' ? '' : style.transform;\n\t\tconst opacity = +style.opacity;\n\t\treturn {\n\t\t\tdelay,\n\t\t\tduration: is_function(duration) ? duration(d) : duration,\n\t\t\teasing,\n\t\t\tcss: (t, u) => `\n\t\t\t\topacity: ${t * opacity};\n\t\t\t\ttransform-origin: top left;\n\t\t\t\ttransform: ${transform} translate(${u * dx}px,${u * dy}px) scale(${t + (1 - t) * dw}, ${\n\t\t\t\tt + (1 - t) * dh\n\t\t\t});\n\t\t\t`\n\t\t};\n\t}\n\n\t/**\n\t * @param {Map<any, Element>} items\n\t * @param {Map<any, Element>} counterparts\n\t * @param {boolean} intro\n\t * @returns {(node: any, params: import('./public').CrossfadeParams & { key: any; }) => () => import('./public').TransitionConfig}\n\t */\n\tfunction transition(items, counterparts, intro) {\n\t\treturn (node, params) => {\n\t\t\titems.set(params.key, node);\n\t\t\treturn () => {\n\t\t\t\tif (counterparts.has(params.key)) {\n\t\t\t\t\tconst other_node = counterparts.get(params.key);\n\t\t\t\t\tcounterparts.delete(params.key);\n\t\t\t\t\treturn crossfade(other_node, node, params);\n\t\t\t\t}\n\t\t\t\t// if the node is disappearing altogether\n\t\t\t\t// (i.e. wasn't claimed by the other list)\n\t\t\t\t// then we need to supply an outro\n\t\t\t\titems.delete(params.key);\n\t\t\t\treturn fallback && fallback(node, params, intro);\n\t\t\t};\n\t\t};\n\t}\n\treturn [transition(to_send, to_receive, false), transition(to_receive, to_send, true)];\n}\n", "<script lang=\"ts\">\n    import {fly} from 'svelte/transition'\n    import {TransitionDirection} from '../../types'\n    import {settings} from '../state'\n\n    export let direction: TransitionDirection | undefined = undefined\n\n    const {animations} = $settings\n\n    const horizontal = ['ltr', 'rtl']\n    // const vertical = ['ttb', 'btt']\n\n    const getDistance = (direction: TransitionDirection) => {\n        return direction === 'rtl' || direction === 'btt' ? 100 : -100\n    }\n\n    $: [x, y] = direction\n        ? horizontal.includes(direction)\n            ? [getDistance(direction), 0]\n            : [0, getDistance(direction)]\n        : [0, 0]\n</script>\n\n{#if animations}\n    <div class=\"transition\" in:fly|global={{duration: 200, x, y}}>\n        <slot />\n    </div>\n{:else}\n    <slot />\n{/if}\n", "<script lang=\"ts\">\n    import {APIClient, ChainDefinition, UserInterfaceWalletPlugin} from '@wharfkit/session'\n    import {createEventDispatcher, getContext, onDestroy, onMount} from 'svelte'\n    import {derived, Readable} from 'svelte/store'\n\n    import {i18nType} from 'src/lib/translations'\n    import {\n        loginContext,\n        loginResponse,\n        props,\n        UserInterfaceLoginData,\n        backAction,\n        transitionDirection,\n        allowSettings,\n    } from './state'\n\n    import Blockchain from './login/Blockchain.svelte'\n    import Permission from './login/Permission.svelte'\n    import Wallet from './login/Wallet.svelte'\n    import Countdown from './components/Countdown.svelte'\n    import Transition from './components/Transition.svelte'\n\n    const {t} = getContext<i18nType>('i18n')\n\n    let completed = false\n\n    const dispatch = createEventDispatcher<{\n        complete: UserInterfaceLoginData\n        cancel: void\n    }>()\n\n    enum Steps {\n        done = 'done',\n        enterPermission = 'enterPermission',\n        selectChain = 'selectChain',\n        selectPermission = 'selectPermission',\n        selectWallet = 'selectWallet',\n    }\n\n    const chain: Readable<ChainDefinition | undefined> = derived(\n        [loginContext, loginResponse],\n        ([$currentContext, $currentResponse]) => {\n            if (!$currentContext || $currentResponse.chainId === undefined) {\n                return undefined\n            }\n            if ($currentContext.chain) {\n                return $currentContext.chain\n            }\n            return $currentContext.chains.find((c) => c.id === $currentResponse.chainId)\n        }\n    )\n\n    const client: Readable<APIClient | undefined> = derived(\n        [chain, loginContext],\n        ([$currentChain, $currentContext]) => {\n            if (!$currentContext || $currentChain === undefined) {\n                return undefined\n            }\n            return $currentContext.getClient($currentChain)\n        }\n    )\n\n    const walletPlugin: Readable<UserInterfaceWalletPlugin | undefined> = derived(\n        [loginContext, loginResponse],\n        ([$currentContext, $currentResponse]) => {\n            if (!$currentContext || $currentResponse.walletPluginIndex === undefined) {\n                return undefined\n            }\n            return $currentContext.walletPlugins[$currentResponse.walletPluginIndex]\n        }\n    )\n\n    let chains: Readable<ChainDefinition[]> = derived(\n        [loginContext, walletPlugin],\n        ([$currentContext, $currentWalletPlugin]) => {\n            if (!$currentContext || !$currentWalletPlugin) {\n                return []\n            }\n            // If the selected WalletPlugin has an array of supported chains, filter the list of chains\n            if ($currentWalletPlugin.config.supportedChains) {\n                return $currentContext.chains.filter((chain) => {\n                    return (\n                        !$currentWalletPlugin.config.supportedChains ||\n                        $currentWalletPlugin.config.supportedChains.includes(String(chain.id))\n                    )\n                })\n            }\n            return $currentContext.chains\n        }\n    )\n\n    const loginContextUnsubscribe = loginContext.subscribe((currentContext) => {\n        if (currentContext) {\n            // If an appName is specified, set the title to it.\n            $props.subtitle = $t('login.title-app', {\n                appName: currentContext.appName,\n                default: 'Login to {{appName}}',\n            })\n            // If a chain is specified, set it on the response\n            if (currentContext.chain) {\n                $loginResponse.chainId = currentContext.chain.id\n            }\n            // If only one chain is provided, default to it\n            if (currentContext.chains.length === 1) {\n                $loginResponse.chainId = currentContext.chains[0].id\n            }\n            // If a permissionLevel is defined, set it on the response\n            if (currentContext.permissionLevel) {\n                $loginResponse.permissionLevel = currentContext.permissionLevel\n            }\n            // If only one wallet is provided, default to it\n            if (currentContext.walletPlugins.length === 1) {\n                $loginResponse.walletPluginIndex = 0\n            }\n            // If the walletPluginIndex is defined, set it on the response\n            if (currentContext.walletPluginIndex !== undefined) {\n                $loginResponse.walletPluginIndex = currentContext.walletPluginIndex\n            }\n        }\n    })\n\n    onMount(() => {\n        $props.title = $t('login.title', {default: 'Login'})\n    })\n\n    onDestroy(loginContextUnsubscribe)\n\n    const step = derived(\n        [loginResponse, walletPlugin],\n        ([$currentResponse, $currentWalletPlugin]) => {\n            if (!$currentWalletPlugin) {\n                return Steps.selectWallet\n            }\n\n            const {\n                requiresChainSelect,\n                requiresPermissionEntry,\n                requiresPermissionSelect,\n                supportedChains,\n            } = $currentWalletPlugin.config\n\n            if (!$currentResponse.chainId && supportedChains && supportedChains.length === 1) {\n                $loginResponse.chainId = supportedChains[0]\n                return Steps.selectPermission\n            } else if (!$currentResponse.chainId && $loginContext && $loginContext.chain) {\n                $loginResponse.chainId = $loginContext?.chain.id\n                return Steps.selectPermission\n            } else if (!$currentResponse.chainId && requiresChainSelect) {\n                return Steps.selectChain\n            } else if (!$currentResponse.permissionLevel && requiresPermissionSelect) {\n                return Steps.selectPermission\n            } else if (!$currentResponse.permissionLevel && requiresPermissionEntry) {\n                return Steps.enterPermission\n            }\n\n            // We have completed, return response to kit for the WalletPlugin to trigger\n            complete()\n        }\n    )\n\n    const selectChain = (e) => {\n        $loginResponse.chainId = e.detail\n        $backAction = unselectChain\n        $transitionDirection = 'rtl'\n    }\n    const unselectChain = () => {\n        $loginResponse.chainId = undefined\n        $backAction = unselectWallet\n        $transitionDirection = 'ltr'\n    }\n\n    const selectPermission = (e) => {\n        $loginResponse.permissionLevel = e.detail\n        $backAction = undefined\n        $transitionDirection = 'rtl'\n    }\n    const unselectPermission = () => {\n        $loginResponse.permissionLevel = undefined\n        $transitionDirection = 'ltr'\n    }\n\n    const selectWallet = (e) => {\n        $backAction = unselectWallet\n        $loginResponse.walletPluginIndex = e.detail\n        $transitionDirection = 'rtl'\n    }\n    const unselectWallet = () => {\n        $loginResponse.walletPluginIndex = undefined\n        $backAction = undefined\n        $transitionDirection = 'ltr'\n    }\n\n    const complete = () => {\n        if (!completed) {\n            completed = true\n            dispatch('complete', $loginResponse)\n            backAction.set(undefined)\n            allowSettings.set(false)\n        }\n    }\n\n    const cancel = () => {\n        dispatch('cancel')\n    }\n</script>\n\n{#if $props && $loginContext}\n    {#if $step === Steps.selectWallet}\n        <Transition direction={$transitionDirection}>\n            <Wallet\n                on:select={selectWallet}\n                on:cancel={cancel}\n                wallets={$loginContext.walletPlugins}\n                title={$t('login.select.wallet', {default: 'Select a Wallet'})}\n            />\n        </Transition>\n    {:else if $step === Steps.selectChain && $chains}\n        <Transition direction={$transitionDirection}>\n            <Blockchain\n                on:select={selectChain}\n                on:cancel={unselectWallet}\n                chains={$chains}\n                title={$t('login.select.blockchain', {default: 'Select a Blockchain'})}\n            />\n        </Transition>\n    {:else if $step === Steps.enterPermission && $client && $walletPlugin}\n        <Transition direction={$transitionDirection}>\n            <Permission\n                on:select={selectPermission}\n                on:cancel={unselectChain}\n                chainId={$loginResponse.chainId}\n                client={$client}\n                walletPlugin={$walletPlugin}\n                title={$t('login.enter.account', {default: 'Enter account name'})}\n            />\n        </Transition>\n    {:else if $step === Steps.selectPermission && $client && $walletPlugin}\n        <Transition direction={$transitionDirection}>\n            <Permission\n                on:select={selectPermission}\n                on:cancel={unselectChain}\n                chainId={$loginResponse.chainId}\n                client={$client}\n                walletPlugin={$walletPlugin}\n                title={$t('login.select.account', {default: 'Select an Account'})}\n            />\n        </Transition>\n    {:else}\n        <Countdown\n            data={{\n                label: $t('login.complete', {\n                    default: 'Complete the login using your selected wallet.',\n                }),\n            }}\n        />\n    {/if}\n{:else}\n    <p>{$t('loading', {default: 'Loading...'})}</p>\n{/if}\n", "<div>\n    <slot />\n</div>\n\n<style>\n    div {\n        flex: 1;\n        display: flex;\n        justify-content: space-between;\n        gap: var(--space-xs);\n    }\n</style>\n", "<script lang=\"ts\">\n    import {createEventDispatcher, getContext} from 'svelte'\n    import type {i18nType} from 'src/lib/translations'\n    import Button from './Button.svelte'\n    import ButtonGroup from './ButtonGroup.svelte'\n\n    const {t} = getContext<i18nType>('i18n')\n\n    const dispatch = createEventDispatcher<{\n        complete: void\n        cancel: void\n    }>()\n</script>\n\n<ButtonGroup>\n    <Button\n        data={{\n            variant: 'outlined',\n            label: $t('decline', {default: 'Decline'}),\n            onClick: () => dispatch('cancel'),\n            icon: 'close',\n        }}\n    />\n\n    <Button\n        data={{\n            variant: 'primary',\n            label: $t('accept', {default: 'Accept'}),\n            onClick: () => dispatch('complete'),\n            icon: 'check',\n            autofocus: true,\n        }}\n    />\n</ButtonGroup>\n", "<script>\n    export let data = {\n        label: '[[Unknown Label]]',\n        value: '[[Unknown Value]]',\n    }\n</script>\n\n{#if data}\n    <div class=\"asset\">\n        <p class=\"value\">{data.value}</p>\n        <p class=\"label\">{data.label}</p>\n    </div>\n{/if}\n\n<style lang=\"scss\">\n    .asset {\n        text-align: center;\n\n        > * {\n            margin: 0;\n            line-height: 1.5;\n        }\n    }\n\n    .value {\n        font-size: var(--fs-2);\n        font-weight: 600;\n        color: var(--body-text-color);\n    }\n\n    .label {\n        font-size: var(--fs-0);\n        font-weight: 400;\n        color: var(--body-text-color-variant);\n    }\n</style>\n", "<script lang=\"ts\">\n    import {createEventDispatcher, getContext} from 'svelte'\n\n    import {i18nType} from 'src/lib/translations'\n\n    const {t} = getContext<i18nType>('i18n')\n    const dispatch = createEventDispatcher<{\n        complete: void\n    }>()\n</script>\n\n<button on:click={() => dispatch('complete')}>\n    {$t('close', {default: 'Close'})}\n</button>\n\n<style lang=\"scss\">\n    button {\n        cursor: pointer;\n        display: block;\n        width: 300px;\n        height: 65px;\n        border-radius: 12px;\n        font-size: 16px;\n        font-weight: 600;\n        color: var(--button-text-color);\n        background-color: var(--button-tertiary-color);\n        border: none;\n        box-shadow: none;\n        margin: 27px auto 0;\n    }\n</style>\n", "<script lang=\"ts\">\n    import type {ComponentProps} from 'svelte'\n    import Icon from './Icon.svelte'\n    export let data: {\n        button?: boolean\n        href: string\n        label: string\n        icon?: ComponentProps<Icon>['name']\n        variant?: 'primary' | 'secondary' | 'outlined'\n        target?: string\n    }\n\n    let {button = true, href, label, icon, target, variant = 'outlined'} = data\n</script>\n\n<a class:button class={variant} {href} {target} rel=\"noreferrer\">\n    {#if icon}\n        <Icon name={icon} />\n    {/if}\n    <span>{label}</span>\n</a>\n\n<style lang=\"scss\">\n    @use '../../styles/buttonStyles.css';\n\n    a {\n        align-self: stretch;\n    }\n</style>\n", "export default {\n    L: 1,\n    M: 0,\n    Q: 3,\n    H: 2,\n}\n", "export default {\n    MODE_NUMBER: 1 << 0,\n    MODE_ALPHA_NUM: 1 << 1,\n    MODE_8BIT_BYTE: 1 << 2,\n    MODE_KANJI: 1 << 3,\n}\n", "import mode from './mode'\n\nexport default class QR8bitByte {\n    data\n    mode\n    constructor(data: any) {\n        this.mode = mode.MODE_8BIT_BYTE\n        this.data = data\n    }\n    getLength() {\n        return this.data.length\n    }\n    write(buffer: {put: (arg0: any, arg1: number) => void}) {\n        for (let i = 0; i < this.data.length; i++) {\n            // not JIS ...\n            buffer.put(this.data.charCodeAt(i), 8)\n        }\n    }\n}\n", "export default class QRBitBuffer {\n    buffer: number[]\n    length: number\n    constructor() {\n        this.buffer = []\n        this.length = 0\n    }\n    get(index) {\n        const bufIndex = Math.floor(index / 8)\n        return ((this.buffer[bufIndex] >>> (7 - (index % 8))) & 1) == 1\n    }\n\n    put(num, length) {\n        for (let i = 0; i < length; i++) {\n            this.putBit(((num >>> (length - i - 1)) & 1) == 1)\n        }\n    }\n\n    getLengthInBits() {\n        return this.length\n    }\n\n    putBit(bit) {\n        const bufIndex = Math.floor(this.length / 8)\n        if (this.buffer.length <= bufIndex) {\n            this.buffer.push(0)\n        }\n\n        if (bit) {\n            this.buffer[bufIndex] |= 0x80 >>> this.length % 8\n        }\n\n        this.length++\n    }\n}\n", "const QRMath = {\n    glog: function (n) {\n        if (n < 1) {\n            throw new Error('glog(' + n + ')')\n        }\n\n        return QRMath.LOG_TABLE[n]\n    },\n\n    gexp: function (n) {\n        while (n < 0) {\n            n += 255\n        }\n\n        while (n >= 256) {\n            n -= 255\n        }\n\n        return QRMath.EXP_TABLE[n]\n    },\n\n    EXP_TABLE: new Array(256),\n\n    LOG_TABLE: new Array(256),\n}\n\nfor (let i = 0; i < 8; i++) {\n    QRMath.EXP_TABLE[i] = 1 << i\n}\nfor (let i = 8; i < 256; i++) {\n    QRMath.EXP_TABLE[i] =\n        QRMath.EXP_TABLE[i - 4] ^\n        QRMath.EXP_TABLE[i - 5] ^\n        QRMath.EXP_TABLE[i - 6] ^\n        QRMath.EXP_TABLE[i - 8]\n}\nfor (let i = 0; i < 255; i++) {\n    QRMath.LOG_TABLE[QRMath.EXP_TABLE[i]] = i\n}\n\nexport default QRMath\n", "import math from './math'\n\nexport default class QRPolynomial {\n    num: number[]\n    constructor(num, shift) {\n        if (num.length == undefined) {\n            throw new Error(num.length + '/' + shift)\n        }\n\n        let offset = 0\n\n        while (offset < num.length && num[offset] == 0) {\n            offset++\n        }\n\n        this.num = new Array(num.length - offset + shift)\n        for (let i = 0; i < num.length - offset; i++) {\n            this.num[i] = num[i + offset]\n        }\n    }\n\n    get(index: string | number) {\n        return this.num[index]\n    }\n\n    getLength() {\n        return this.num.length\n    }\n\n    multiply(e: {getLength: () => number; get: (arg0: number) => any}) {\n        const num = new Array(this.getLength() + e.getLength() - 1)\n\n        for (let i = 0; i < this.getLength(); i++) {\n            for (let j = 0; j < e.getLength(); j++) {\n                num[i + j] ^= math.gexp(math.glog(this.get(i)) + math.glog(e.get(j)))\n            }\n        }\n\n        return new QRPolynomial(num, 0)\n    }\n\n    mod(e: {getLength: () => number; get: (arg0: number) => any}) {\n        if (this.getLength() - e.getLength() < 0) {\n            return this\n        }\n\n        const ratio = math.glog(this.get(0)) - math.glog(e.get(0))\n\n        const num = new Array(this.getLength())\n\n        for (let i = 0; i < this.getLength(); i++) {\n            num[i] = this.get(i)\n        }\n\n        for (let i = 0; i < e.getLength(); i++) {\n            num[i] ^= math.gexp(math.glog(e.get(i)) + ratio)\n        }\n\n        // recursive call\n        return new QRPolynomial(num, 0).mod(e)\n    }\n}\n", "// ErrorCorrectLevel\nimport ECL from './ErrorCorrectLevel'\n\nexport default class QRRSBlock {\n    totalCount: number\n    dataCount: number\n\n    constructor(totalCount, dataCount) {\n        this.totalCount = totalCount\n        this.dataCount = dataCount\n    }\n\n    static RS_BLOCK_TABLE = [\n        // L\n        // M\n        // Q\n        // H\n\n        // 1\n        [1, 26, 19],\n        [1, 26, 16],\n        [1, 26, 13],\n        [1, 26, 9],\n\n        // 2\n        [1, 44, 34],\n        [1, 44, 28],\n        [1, 44, 22],\n        [1, 44, 16],\n\n        // 3\n        [1, 70, 55],\n        [1, 70, 44],\n        [2, 35, 17],\n        [2, 35, 13],\n\n        // 4\n        [1, 100, 80],\n        [2, 50, 32],\n        [2, 50, 24],\n        [4, 25, 9],\n\n        // 5\n        [1, 134, 108],\n        [2, 67, 43],\n        [2, 33, 15, 2, 34, 16],\n        [2, 33, 11, 2, 34, 12],\n\n        // 6\n        [2, 86, 68],\n        [4, 43, 27],\n        [4, 43, 19],\n        [4, 43, 15],\n\n        // 7\n        [2, 98, 78],\n        [4, 49, 31],\n        [2, 32, 14, 4, 33, 15],\n        [4, 39, 13, 1, 40, 14],\n\n        // 8\n        [2, 121, 97],\n        [2, 60, 38, 2, 61, 39],\n        [4, 40, 18, 2, 41, 19],\n        [4, 40, 14, 2, 41, 15],\n\n        // 9\n        [2, 146, 116],\n        [3, 58, 36, 2, 59, 37],\n        [4, 36, 16, 4, 37, 17],\n        [4, 36, 12, 4, 37, 13],\n\n        // 10\n        [2, 86, 68, 2, 87, 69],\n        [4, 69, 43, 1, 70, 44],\n        [6, 43, 19, 2, 44, 20],\n        [6, 43, 15, 2, 44, 16],\n\n        // 11\n        [4, 101, 81],\n        [1, 80, 50, 4, 81, 51],\n        [4, 50, 22, 4, 51, 23],\n        [3, 36, 12, 8, 37, 13],\n\n        // 12\n        [2, 116, 92, 2, 117, 93],\n        [6, 58, 36, 2, 59, 37],\n        [4, 46, 20, 6, 47, 21],\n        [7, 42, 14, 4, 43, 15],\n\n        // 13\n        [4, 133, 107],\n        [8, 59, 37, 1, 60, 38],\n        [8, 44, 20, 4, 45, 21],\n        [12, 33, 11, 4, 34, 12],\n\n        // 14\n        [3, 145, 115, 1, 146, 116],\n        [4, 64, 40, 5, 65, 41],\n        [11, 36, 16, 5, 37, 17],\n        [11, 36, 12, 5, 37, 13],\n\n        // 15\n        [5, 109, 87, 1, 110, 88],\n        [5, 65, 41, 5, 66, 42],\n        [5, 54, 24, 7, 55, 25],\n        [11, 36, 12],\n\n        // 16\n        [5, 122, 98, 1, 123, 99],\n        [7, 73, 45, 3, 74, 46],\n        [15, 43, 19, 2, 44, 20],\n        [3, 45, 15, 13, 46, 16],\n\n        // 17\n        [1, 135, 107, 5, 136, 108],\n        [10, 74, 46, 1, 75, 47],\n        [1, 50, 22, 15, 51, 23],\n        [2, 42, 14, 17, 43, 15],\n\n        // 18\n        [5, 150, 120, 1, 151, 121],\n        [9, 69, 43, 4, 70, 44],\n        [17, 50, 22, 1, 51, 23],\n        [2, 42, 14, 19, 43, 15],\n\n        // 19\n        [3, 141, 113, 4, 142, 114],\n        [3, 70, 44, 11, 71, 45],\n        [17, 47, 21, 4, 48, 22],\n        [9, 39, 13, 16, 40, 14],\n\n        // 20\n        [3, 135, 107, 5, 136, 108],\n        [3, 67, 41, 13, 68, 42],\n        [15, 54, 24, 5, 55, 25],\n        [15, 43, 15, 10, 44, 16],\n\n        // 21\n        [4, 144, 116, 4, 145, 117],\n        [17, 68, 42],\n        [17, 50, 22, 6, 51, 23],\n        [19, 46, 16, 6, 47, 17],\n\n        // 22\n        [2, 139, 111, 7, 140, 112],\n        [17, 74, 46],\n        [7, 54, 24, 16, 55, 25],\n        [34, 37, 13],\n\n        // 23\n        [4, 151, 121, 5, 152, 122],\n        [4, 75, 47, 14, 76, 48],\n        [11, 54, 24, 14, 55, 25],\n        [16, 45, 15, 14, 46, 16],\n\n        // 24\n        [6, 147, 117, 4, 148, 118],\n        [6, 73, 45, 14, 74, 46],\n        [11, 54, 24, 16, 55, 25],\n        [30, 46, 16, 2, 47, 17],\n\n        // 25\n        [8, 132, 106, 4, 133, 107],\n        [8, 75, 47, 13, 76, 48],\n        [7, 54, 24, 22, 55, 25],\n        [22, 45, 15, 13, 46, 16],\n\n        // 26\n        [10, 142, 114, 2, 143, 115],\n        [19, 74, 46, 4, 75, 47],\n        [28, 50, 22, 6, 51, 23],\n        [33, 46, 16, 4, 47, 17],\n\n        // 27\n        [8, 152, 122, 4, 153, 123],\n        [22, 73, 45, 3, 74, 46],\n        [8, 53, 23, 26, 54, 24],\n        [12, 45, 15, 28, 46, 16],\n\n        // 28\n        [3, 147, 117, 10, 148, 118],\n        [3, 73, 45, 23, 74, 46],\n        [4, 54, 24, 31, 55, 25],\n        [11, 45, 15, 31, 46, 16],\n\n        // 29\n        [7, 146, 116, 7, 147, 117],\n        [21, 73, 45, 7, 74, 46],\n        [1, 53, 23, 37, 54, 24],\n        [19, 45, 15, 26, 46, 16],\n\n        // 30\n        [5, 145, 115, 10, 146, 116],\n        [19, 75, 47, 10, 76, 48],\n        [15, 54, 24, 25, 55, 25],\n        [23, 45, 15, 25, 46, 16],\n\n        // 31\n        [13, 145, 115, 3, 146, 116],\n        [2, 74, 46, 29, 75, 47],\n        [42, 54, 24, 1, 55, 25],\n        [23, 45, 15, 28, 46, 16],\n\n        // 32\n        [17, 145, 115],\n        [10, 74, 46, 23, 75, 47],\n        [10, 54, 24, 35, 55, 25],\n        [19, 45, 15, 35, 46, 16],\n\n        // 33\n        [17, 145, 115, 1, 146, 116],\n        [14, 74, 46, 21, 75, 47],\n        [29, 54, 24, 19, 55, 25],\n        [11, 45, 15, 46, 46, 16],\n\n        // 34\n        [13, 145, 115, 6, 146, 116],\n        [14, 74, 46, 23, 75, 47],\n        [44, 54, 24, 7, 55, 25],\n        [59, 46, 16, 1, 47, 17],\n\n        // 35\n        [12, 151, 121, 7, 152, 122],\n        [12, 75, 47, 26, 76, 48],\n        [39, 54, 24, 14, 55, 25],\n        [22, 45, 15, 41, 46, 16],\n\n        // 36\n        [6, 151, 121, 14, 152, 122],\n        [6, 75, 47, 34, 76, 48],\n        [46, 54, 24, 10, 55, 25],\n        [2, 45, 15, 64, 46, 16],\n\n        // 37\n        [17, 152, 122, 4, 153, 123],\n        [29, 74, 46, 14, 75, 47],\n        [49, 54, 24, 10, 55, 25],\n        [24, 45, 15, 46, 46, 16],\n\n        // 38\n        [4, 152, 122, 18, 153, 123],\n        [13, 74, 46, 32, 75, 47],\n        [48, 54, 24, 14, 55, 25],\n        [42, 45, 15, 32, 46, 16],\n\n        // 39\n        [20, 147, 117, 4, 148, 118],\n        [40, 75, 47, 7, 76, 48],\n        [43, 54, 24, 22, 55, 25],\n        [10, 45, 15, 67, 46, 16],\n\n        // 40\n        [19, 148, 118, 6, 149, 119],\n        [18, 75, 47, 31, 76, 48],\n        [34, 54, 24, 34, 55, 25],\n        [20, 45, 15, 61, 46, 16],\n    ]\n\n    static getRSBlocks(typeNumber, errorCorrectLevel) {\n        const rsBlock = QRRSBlock.getRsBlockTable(typeNumber, errorCorrectLevel)\n\n        if (rsBlock == undefined) {\n            throw new Error(\n                'bad rs block @ typeNumber:' +\n                    typeNumber +\n                    '/errorCorrectLevel:' +\n                    errorCorrectLevel\n            )\n        }\n\n        const length = rsBlock.length / 3\n\n        const list: any[] = []\n\n        for (let i = 0; i < length; i++) {\n            const count = rsBlock[i * 3 + 0]\n            const totalCount = rsBlock[i * 3 + 1]\n            const dataCount = rsBlock[i * 3 + 2]\n\n            for (let j = 0; j < count; j++) {\n                list.push(new QRRSBlock(totalCount, dataCount))\n            }\n        }\n\n        return list\n    }\n\n    static getRsBlockTable(typeNumber, errorCorrectLevel) {\n        switch (errorCorrectLevel) {\n            case ECL.L:\n                return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 0]\n            case ECL.M:\n                return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 1]\n            case ECL.Q:\n                return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 2]\n            case ECL.H:\n                return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 3]\n            default:\n                return undefined\n        }\n    }\n}\n", "import math from './math'\nimport Mode from './mode'\nimport Polynomial from './Polynomial'\n\nexport const QRMaskPattern = {\n    PATTERN000: 0,\n    PATTERN001: 1,\n    PATTERN010: 2,\n    PATTERN011: 3,\n    PATTERN100: 4,\n    PATTERN101: 5,\n    PATTERN110: 6,\n    PATTERN111: 7,\n}\n\nconst QRUtil = {\n    PATTERN_POSITION_TABLE: [\n        [],\n        [6, 18],\n        [6, 22],\n        [6, 26],\n        [6, 30],\n        [6, 34],\n        [6, 22, 38],\n        [6, 24, 42],\n        [6, 26, 46],\n        [6, 28, 50],\n        [6, 30, 54],\n        [6, 32, 58],\n        [6, 34, 62],\n        [6, 26, 46, 66],\n        [6, 26, 48, 70],\n        [6, 26, 50, 74],\n        [6, 30, 54, 78],\n        [6, 30, 56, 82],\n        [6, 30, 58, 86],\n        [6, 34, 62, 90],\n        [6, 28, 50, 72, 94],\n        [6, 26, 50, 74, 98],\n        [6, 30, 54, 78, 102],\n        [6, 28, 54, 80, 106],\n        [6, 32, 58, 84, 110],\n        [6, 30, 58, 86, 114],\n        [6, 34, 62, 90, 118],\n        [6, 26, 50, 74, 98, 122],\n        [6, 30, 54, 78, 102, 126],\n        [6, 26, 52, 78, 104, 130],\n        [6, 30, 56, 82, 108, 134],\n        [6, 34, 60, 86, 112, 138],\n        [6, 30, 58, 86, 114, 142],\n        [6, 34, 62, 90, 118, 146],\n        [6, 30, 54, 78, 102, 126, 150],\n        [6, 24, 50, 76, 102, 128, 154],\n        [6, 28, 54, 80, 106, 132, 158],\n        [6, 32, 58, 84, 110, 136, 162],\n        [6, 26, 54, 82, 110, 138, 166],\n        [6, 30, 58, 86, 114, 142, 170],\n    ],\n\n    G15: (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0),\n    G18: (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0),\n    G15_MASK: (1 << 14) | (1 << 12) | (1 << 10) | (1 << 4) | (1 << 1),\n\n    getBCHTypeInfo: function (data) {\n        let d = data << 10\n        while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15) >= 0) {\n            d ^= QRUtil.G15 << (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15))\n        }\n        return ((data << 10) | d) ^ QRUtil.G15_MASK\n    },\n\n    getBCHTypeNumber: function (data) {\n        let d = data << 12\n        while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18) >= 0) {\n            d ^= QRUtil.G18 << (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18))\n        }\n        return (data << 12) | d\n    },\n\n    getBCHDigit: function (data) {\n        let digit = 0\n\n        while (data != 0) {\n            digit++\n            data >>>= 1\n        }\n\n        return digit\n    },\n\n    getPatternPosition: function (typeNumber) {\n        return QRUtil.PATTERN_POSITION_TABLE[typeNumber - 1]\n    },\n\n    getMask: function (maskPattern, i, j) {\n        switch (maskPattern) {\n            case QRMaskPattern.PATTERN000:\n                return (i + j) % 2 == 0\n            case QRMaskPattern.PATTERN001:\n                return i % 2 == 0\n            case QRMaskPattern.PATTERN010:\n                return j % 3 == 0\n            case QRMaskPattern.PATTERN011:\n                return (i + j) % 3 == 0\n            case QRMaskPattern.PATTERN100:\n                return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 == 0\n            case QRMaskPattern.PATTERN101:\n                return ((i * j) % 2) + ((i * j) % 3) == 0\n            case QRMaskPattern.PATTERN110:\n                return (((i * j) % 2) + ((i * j) % 3)) % 2 == 0\n            case QRMaskPattern.PATTERN111:\n                return (((i * j) % 3) + ((i + j) % 2)) % 2 == 0\n\n            default:\n                throw new Error('bad maskPattern:' + maskPattern)\n        }\n    },\n\n    getErrorCorrectPolynomial: function (errorCorrectLength) {\n        let a = new Polynomial([1], 0)\n\n        for (let i = 0; i < errorCorrectLength; i++) {\n            a = a.multiply(new Polynomial([1, math.gexp(i)], 0))\n        }\n\n        return a\n    },\n\n    getLengthInBits: function (mode, type) {\n        if (1 <= type && type < 10) {\n            // 1 - 9\n\n            switch (mode) {\n                case Mode.MODE_NUMBER:\n                    return 10\n                case Mode.MODE_ALPHA_NUM:\n                    return 9\n                case Mode.MODE_8BIT_BYTE:\n                    return 8\n                case Mode.MODE_KANJI:\n                    return 8\n                default:\n                    throw new Error('mode:' + mode)\n            }\n        } else if (type < 27) {\n            // 10 - 26\n\n            switch (mode) {\n                case Mode.MODE_NUMBER:\n                    return 12\n                case Mode.MODE_ALPHA_NUM:\n                    return 11\n                case Mode.MODE_8BIT_BYTE:\n                    return 16\n                case Mode.MODE_KANJI:\n                    return 10\n                default:\n                    throw new Error('mode:' + mode)\n            }\n        } else if (type < 41) {\n            // 27 - 40\n\n            switch (mode) {\n                case Mode.MODE_NUMBER:\n                    return 14\n                case Mode.MODE_ALPHA_NUM:\n                    return 13\n                case Mode.MODE_8BIT_BYTE:\n                    return 16\n                case Mode.MODE_KANJI:\n                    return 12\n                default:\n                    throw new Error('mode:' + mode)\n            }\n        } else {\n            throw new Error('type:' + type)\n        }\n    },\n\n    getLostPoint: function (qrCode) {\n        const moduleCount = qrCode.getModuleCount()\n\n        let lostPoint = 0\n\n        // LEVEL1\n\n        for (let row = 0; row < moduleCount; row++) {\n            for (let col = 0; col < moduleCount; col++) {\n                let sameCount = 0\n                const dark = qrCode.isDark(row, col)\n\n                for (let r = -1; r <= 1; r++) {\n                    if (row + r < 0 || moduleCount <= row + r) {\n                        continue\n                    }\n\n                    for (let c = -1; c <= 1; c++) {\n                        if (col + c < 0 || moduleCount <= col + c) {\n                            continue\n                        }\n\n                        if (r == 0 && c == 0) {\n                            continue\n                        }\n\n                        if (dark == qrCode.isDark(row + r, col + c)) {\n                            sameCount++\n                        }\n                    }\n                }\n\n                if (sameCount > 5) {\n                    lostPoint += 3 + sameCount - 5\n                }\n            }\n        }\n\n        // LEVEL2\n\n        for (let row = 0; row < moduleCount - 1; row++) {\n            for (let col = 0; col < moduleCount - 1; col++) {\n                let count = 0\n                if (qrCode.isDark(row, col)) count++\n                if (qrCode.isDark(row + 1, col)) count++\n                if (qrCode.isDark(row, col + 1)) count++\n                if (qrCode.isDark(row + 1, col + 1)) count++\n                if (count == 0 || count == 4) {\n                    lostPoint += 3\n                }\n            }\n        }\n\n        // LEVEL3\n\n        for (let row = 0; row < moduleCount; row++) {\n            for (let col = 0; col < moduleCount - 6; col++) {\n                if (\n                    qrCode.isDark(row, col) &&\n                    !qrCode.isDark(row, col + 1) &&\n                    qrCode.isDark(row, col + 2) &&\n                    qrCode.isDark(row, col + 3) &&\n                    qrCode.isDark(row, col + 4) &&\n                    !qrCode.isDark(row, col + 5) &&\n                    qrCode.isDark(row, col + 6)\n                ) {\n                    lostPoint += 40\n                }\n            }\n        }\n\n        for (let col = 0; col < moduleCount; col++) {\n            for (let row = 0; row < moduleCount - 6; row++) {\n                if (\n                    qrCode.isDark(row, col) &&\n                    !qrCode.isDark(row + 1, col) &&\n                    qrCode.isDark(row + 2, col) &&\n                    qrCode.isDark(row + 3, col) &&\n                    qrCode.isDark(row + 4, col) &&\n                    !qrCode.isDark(row + 5, col) &&\n                    qrCode.isDark(row + 6, col)\n                ) {\n                    lostPoint += 40\n                }\n            }\n        }\n\n        // LEVEL4\n\n        let darkCount = 0\n\n        for (let col = 0; col < moduleCount; col++) {\n            for (let row = 0; row < moduleCount; row++) {\n                if (qrCode.isDark(row, col)) {\n                    darkCount++\n                }\n            }\n        }\n\n        const ratio = Math.abs((100 * darkCount) / moduleCount / moduleCount - 50) / 5\n        lostPoint += ratio * 10\n\n        return lostPoint\n    },\n}\n\nexport default QRUtil\n", "import BitByte from './8BitByte'\nimport BitBuffer from './BitBuffer'\nimport Polynomial from './Polynomial'\nimport RSBlock from './RSBlock'\nimport util from './util'\n\nexport default class QRCode {\n    typeNumber: number\n    errorCorrectLevel: any\n    modules: any\n    moduleCount: number\n    dataCache: any\n    dataList: any\n\n    constructor(typeNumber, errorCorrectLevel) {\n        this.typeNumber = typeNumber\n        this.errorCorrectLevel = errorCorrectLevel\n        this.modules = null\n        this.moduleCount = 0\n        this.dataCache = null\n        this.dataList = []\n    }\n\n    addData(data: any) {\n        const newData = new BitByte(data)\n        this.dataList.push(newData)\n        this.dataCache = null\n    }\n\n    isDark(row: number, col: number) {\n        if (row < 0 || this.moduleCount <= row || col < 0 || this.moduleCount <= col) {\n            throw new Error(row + ',' + col)\n        }\n        return this.modules[row][col]\n    }\n\n    getModuleCount() {\n        return this.moduleCount\n    }\n\n    make() {\n        // Calculate automatically typeNumber if provided is < 1\n        if (this.typeNumber < 1) {\n            let typeNumber = 1\n            for (typeNumber = 1; typeNumber < 40; typeNumber++) {\n                const rsBlocks = RSBlock.getRSBlocks(typeNumber, this.errorCorrectLevel)\n\n                const buffer = new BitBuffer()\n                let totalDataCount = 0\n                for (let i = 0; i < rsBlocks.length; i++) {\n                    totalDataCount += rsBlocks[i].dataCount\n                }\n\n                for (let i = 0; i < this.dataList.length; i++) {\n                    const data = this.dataList[i]\n                    buffer.put(data.mode, 4)\n                    buffer.put(data.getLength(), util.getLengthInBits(data.mode, typeNumber))\n                    data.write(buffer)\n                }\n                if (buffer.getLengthInBits() <= totalDataCount * 8) break\n            }\n            this.typeNumber = typeNumber\n        }\n        this.makeImpl(false, this.getBestMaskPattern())\n    }\n\n    makeImpl(test: any, maskPattern: any) {\n        this.moduleCount = this.typeNumber * 4 + 17\n        this.modules = new Array(this.moduleCount)\n\n        for (let row = 0; row < this.moduleCount; row++) {\n            this.modules[row] = new Array(this.moduleCount)\n\n            for (let col = 0; col < this.moduleCount; col++) {\n                this.modules[row][col] = null //(col + row) % 3;\n            }\n        }\n\n        this.setupPositionProbePattern(0, 0)\n        this.setupPositionProbePattern(this.moduleCount - 7, 0)\n        this.setupPositionProbePattern(0, this.moduleCount - 7)\n        this.setupPositionAdjustPattern()\n        this.setupTimingPattern()\n        this.setupTypeInfo(test, maskPattern)\n\n        if (this.typeNumber >= 7) {\n            this.setupTypeNumber(test)\n        }\n\n        if (this.dataCache == null) {\n            this.dataCache = QRCode.createData(\n                this.typeNumber,\n                this.errorCorrectLevel,\n                this.dataList\n            )\n        }\n\n        this.mapData(this.dataCache, maskPattern)\n    }\n\n    setupPositionProbePattern(row: number, col: number) {\n        for (let r = -1; r <= 7; r++) {\n            if (row + r <= -1 || this.moduleCount <= row + r) continue\n\n            for (let c = -1; c <= 7; c++) {\n                if (col + c <= -1 || this.moduleCount <= col + c) continue\n\n                if (\n                    (0 <= r && r <= 6 && (c == 0 || c == 6)) ||\n                    (0 <= c && c <= 6 && (r == 0 || r == 6)) ||\n                    (2 <= r && r <= 4 && 2 <= c && c <= 4)\n                ) {\n                    this.modules[row + r][col + c] = true\n                } else {\n                    this.modules[row + r][col + c] = false\n                }\n            }\n        }\n    }\n\n    getBestMaskPattern() {\n        let minLostPoint = 0\n        let pattern = 0\n\n        for (let i = 0; i < 8; i++) {\n            this.makeImpl(true, i)\n\n            const lostPoint = util.getLostPoint(this)\n\n            if (i == 0 || minLostPoint > lostPoint) {\n                minLostPoint = lostPoint\n                pattern = i\n            }\n        }\n\n        return pattern\n    }\n\n    setupTimingPattern() {\n        for (let r = 8; r < this.moduleCount - 8; r++) {\n            if (this.modules[r][6] != null) {\n                continue\n            }\n            this.modules[r][6] = r % 2 == 0\n        }\n\n        for (let c = 8; c < this.moduleCount - 8; c++) {\n            if (this.modules[6][c] != null) {\n                continue\n            }\n            this.modules[6][c] = c % 2 == 0\n        }\n    }\n\n    setupPositionAdjustPattern() {\n        const pos = util.getPatternPosition(this.typeNumber)\n\n        for (let i = 0; i < pos.length; i++) {\n            for (let j = 0; j < pos.length; j++) {\n                const row = pos[i]\n                const col = pos[j]\n\n                if (this.modules[row][col] != null) {\n                    continue\n                }\n\n                for (let r = -2; r <= 2; r++) {\n                    for (let c = -2; c <= 2; c++) {\n                        if (r == -2 || r == 2 || c == -2 || c == 2 || (r == 0 && c == 0)) {\n                            this.modules[row + r][col + c] = true\n                        } else {\n                            this.modules[row + r][col + c] = false\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    setupTypeNumber(test: any) {\n        const bits = util.getBCHTypeNumber(this.typeNumber)\n\n        for (let i = 0; i < 18; i++) {\n            const mod = !test && ((bits >> i) & 1) == 1\n            this.modules[Math.floor(i / 3)][(i % 3) + this.moduleCount - 8 - 3] = mod\n        }\n\n        for (let i = 0; i < 18; i++) {\n            const mod = !test && ((bits >> i) & 1) == 1\n            this.modules[(i % 3) + this.moduleCount - 8 - 3][Math.floor(i / 3)] = mod\n        }\n    }\n\n    setupTypeInfo(test: any, maskPattern: number) {\n        const data = (this.errorCorrectLevel << 3) | maskPattern\n        const bits = util.getBCHTypeInfo(data)\n\n        // vertical\n        for (let i = 0; i < 15; i++) {\n            const mod = !test && ((bits >> i) & 1) == 1\n\n            if (i < 6) {\n                this.modules[i][8] = mod\n            } else if (i < 8) {\n                this.modules[i + 1][8] = mod\n            } else {\n                this.modules[this.moduleCount - 15 + i][8] = mod\n            }\n        }\n\n        // horizontal\n        for (let i = 0; i < 15; i++) {\n            const mod = !test && ((bits >> i) & 1) == 1\n\n            if (i < 8) {\n                this.modules[8][this.moduleCount - i - 1] = mod\n            } else if (i < 9) {\n                this.modules[8][15 - i - 1 + 1] = mod\n            } else {\n                this.modules[8][15 - i - 1] = mod\n            }\n        }\n\n        // fixed module\n        this.modules[this.moduleCount - 8][8] = !test\n    }\n\n    mapData(data: string | any[], maskPattern: any) {\n        let inc = -1\n        let row = this.moduleCount - 1\n        let bitIndex = 7\n        let byteIndex = 0\n\n        for (let col = this.moduleCount - 1; col > 0; col -= 2) {\n            if (col == 6) col--\n\n            for (;;) {\n                for (let c = 0; c < 2; c++) {\n                    if (this.modules[row][col - c] == null) {\n                        let dark = false\n\n                        if (byteIndex < data.length) {\n                            dark = ((data[byteIndex] >>> bitIndex) & 1) == 1\n                        }\n\n                        const mask = util.getMask(maskPattern, row, col - c)\n\n                        if (mask) {\n                            dark = !dark\n                        }\n\n                        this.modules[row][col - c] = dark\n                        bitIndex--\n\n                        if (bitIndex == -1) {\n                            byteIndex++\n                            bitIndex = 7\n                        }\n                    }\n                }\n\n                row += inc\n\n                if (row < 0 || this.moduleCount <= row) {\n                    row -= inc\n                    inc = -inc\n                    break\n                }\n            }\n        }\n    }\n\n    static PAD0 = 0xec\n    static PAD1 = 0x11\n\n    static createData(typeNumber: any, errorCorrectLevel: any, dataList: string | any[]) {\n        const rsBlocks = RSBlock.getRSBlocks(typeNumber, errorCorrectLevel)\n\n        const buffer = new BitBuffer()\n\n        for (let i = 0; i < dataList.length; i++) {\n            const data = dataList[i]\n            buffer.put(data.mode, 4)\n            buffer.put(data.getLength(), util.getLengthInBits(data.mode, typeNumber))\n            data.write(buffer)\n        }\n\n        // calc num max data.\n        let totalDataCount = 0\n        for (let i = 0; i < rsBlocks.length; i++) {\n            totalDataCount += rsBlocks[i].dataCount\n        }\n\n        if (buffer.getLengthInBits() > totalDataCount * 8) {\n            throw new Error(\n                'code length overflow. (' +\n                    buffer.getLengthInBits() +\n                    '>' +\n                    totalDataCount * 8 +\n                    ')'\n            )\n        }\n\n        // end code\n        if (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {\n            buffer.put(0, 4)\n        }\n\n        // padding\n        while (buffer.getLengthInBits() % 8 != 0) {\n            buffer.putBit(false)\n        }\n\n        // padding\n        for (;;) {\n            if (buffer.getLengthInBits() >= totalDataCount * 8) {\n                break\n            }\n            buffer.put(QRCode.PAD0, 8)\n\n            if (buffer.getLengthInBits() >= totalDataCount * 8) {\n                break\n            }\n            buffer.put(QRCode.PAD1, 8)\n        }\n\n        return QRCode.createBytes(buffer, rsBlocks)\n    }\n\n    static createBytes(buffer: {buffer: number[]}, rsBlocks: string | any[]) {\n        let offset = 0\n\n        let maxDcCount = 0\n        let maxEcCount = 0\n\n        const dcdata = new Array(rsBlocks.length)\n        const ecdata = new Array(rsBlocks.length)\n\n        for (let r = 0; r < rsBlocks.length; r++) {\n            const dcCount = rsBlocks[r].dataCount\n            const ecCount = rsBlocks[r].totalCount - dcCount\n\n            maxDcCount = Math.max(maxDcCount, dcCount)\n            maxEcCount = Math.max(maxEcCount, ecCount)\n\n            dcdata[r] = new Array(dcCount)\n\n            for (let i = 0; i < dcdata[r].length; i++) {\n                dcdata[r][i] = 0xff & buffer.buffer[i + offset]\n            }\n            offset += dcCount\n\n            const rsPoly = util.getErrorCorrectPolynomial(ecCount)\n            const rawPoly = new Polynomial(dcdata[r], rsPoly.getLength() - 1)\n\n            const modPoly = rawPoly.mod(rsPoly)\n            ecdata[r] = new Array(rsPoly.getLength() - 1)\n            for (let i = 0; i < ecdata[r].length; i++) {\n                const modIndex = i + modPoly.getLength() - ecdata[r].length\n                ecdata[r][i] = modIndex >= 0 ? modPoly.get(modIndex) : 0\n            }\n        }\n\n        let totalCodeCount = 0\n        for (let i = 0; i < rsBlocks.length; i++) {\n            totalCodeCount += rsBlocks[i].totalCount\n        }\n\n        const data = new Array(totalCodeCount)\n        let index = 0\n\n        for (let i = 0; i < maxDcCount; i++) {\n            for (let r = 0; r < rsBlocks.length; r++) {\n                if (i < dcdata[r].length) {\n                    data[index++] = dcdata[r][i]\n                }\n            }\n        }\n\n        for (let i = 0; i < maxEcCount; i++) {\n            for (let r = 0; r < rsBlocks.length; r++) {\n                if (i < ecdata[r].length) {\n                    data[index++] = ecdata[r][i]\n                }\n            }\n        }\n\n        return data\n    }\n}\n", "import ErrorCorrectLevel from './ErrorCorrectLevel'\nimport QRCode from './QRCode'\n\ninterface Rect {\n    x: number\n    y: number\n    width: number\n    height: number\n}\n\n/**\n * Generate QR SVG\n * <AUTHOR> <<EMAIL>>\n */\nexport default function generate(text: string, level: 'L' | 'M' | 'Q' | 'H' = 'L', version = -1) {\n    try {\n        const qr = new QRCode(version, ErrorCorrectLevel[level])\n        const rects: Rect[] = []\n\n        qr.addData(text)\n        qr.make()\n\n        const rows = qr.modules\n        const size = rows.length\n\n        for (const [y, row] of rows.entries()) {\n            let rect: Rect | undefined\n            for (const [x, on] of row.entries()) {\n                if (on) {\n                    if (!rect) rect = {x, y, width: 0, height: 1}\n                    rect.width++\n                } else {\n                    if (rect && rect.width > 0) {\n                        rects.push(rect)\n                    }\n                    rect = undefined\n                }\n            }\n            if (rect && rect.width > 0) {\n                rects.push(rect)\n            }\n        }\n\n        const svg: string[] = [\n            `<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"300\" viewBox=\"0 0 ${size} ${size}\">`,\n        ]\n        for (const {x, y, width, height} of rects) {\n            svg.push(`<rect x=\"${x}\" y=\"${y}\" width=\"${width}\" height=\"${height}\" />`)\n        }\n        svg.push('</svg>')\n\n        return svg.join('')\n    } catch (e) {\n        console.log('Could not render QR code: ', e)\n    }\n}\n", "<script lang=\"ts\">\n    import {fade} from 'svelte/transition'\n    import generateQr from '../../lib/qrcode'\n    import Icon from './Icon.svelte'\n    import {cubicInOut} from 'svelte/easing'\n    import {onMount} from 'svelte'\n    import {writable} from 'svelte/store'\n    export let data = ''\n\n    let dialog: HTMLDialogElement\n    let expanded = false\n    let copied = false\n\n    const qrcode = writable()\n    onMount(() => {\n        try {\n            qrcode.set(generateQr(data))\n        } catch (e) {\n            console.error('Error rendering QR code', e)\n        }\n    })\n\n    const toggleExpanded = () => {\n        if (expanded) {\n            collapse()\n        } else {\n            expanded = true\n            dialog.showModal()\n        }\n    }\n\n    const collapse = () => {\n        dialog.close()\n        expanded = false\n    }\n\n    // When background is clicked outside of modal, close\n    function backgroundClose(event) {\n        var rect = dialog.getBoundingClientRect()\n        var isInDialog =\n            rect.top <= event.clientY &&\n            event.clientY <= rect.top + rect.height &&\n            rect.left <= event.clientX &&\n            event.clientX <= rect.left + rect.width\n        if (!isInDialog) {\n            collapse()\n        }\n    }\n\n    // When escape keypress is captured, close\n    function escapeClose(event) {\n        if (event.key === 'Escape') {\n            collapse()\n        }\n    }\n\n    // Copy data to clipboard if supported. Requires a secure context e.g. https\n    function copyToClipboard(data: string) {\n        if (!navigator.clipboard) return\n        navigator.clipboard.writeText(data)\n        copied = true\n        setTimeout(() => (copied = false), 1200)\n    }\n</script>\n\n{#if data}\n    <div class=\"wrapper\">\n        {#if $qrcode}\n            <div class=\"main qr\">\n                {@html $qrcode}\n            </div>\n            <dialog\n                bind:this={dialog}\n                on:click|self={backgroundClose}\n                on:keydown|stopPropagation|preventDefault|capture={escapeClose}\n            >\n                <button class=\"qr\" on:click={collapse}>\n                    {@html $qrcode}\n                </button>\n            </dialog>\n        {/if}\n\n        <div class=\"button-group\">\n            {#if $qrcode}\n                <button class=\"expand\" on:click={toggleExpanded}>\n                    <Icon name=\"expand\" size=\"var(--space-m)\" />\n                    <div>\n                        <span>Expand</span> <span>QR code</span>\n                    </div>\n                </button>\n            {/if}\n            <button class=\"copy\" on:click={() => copyToClipboard(data)}>\n                <div class=\"icon\">\n                    <div>\n                        <Icon name=\"copy\" size=\"var(--space-m)\" />\n                    </div>\n                    {#if copied}\n                        <div class=\"check\" transition:fade={{duration: 180, easing: cubicInOut}}>\n                            <Icon name=\"check\" size=\"var(--space-m)\" />\n                        </div>\n                    {/if}\n                </div>\n                <div>\n                    <span>Copy</span> <span>to clipboard</span>\n                </div>\n            </button>\n        </div>\n    </div>\n{/if}\n\n<style>\n    .wrapper {\n        position: relative;\n        display: grid;\n        background: var(--body-background-color);\n        border-radius: var(--space-s);\n        padding: var(--space-m);\n        box-shadow: var(--qr-border-color);\n        margin-bottom: var(--space-xs);\n    }\n\n    .qr {\n        display: flex;\n    }\n\n    .qr :global(svg) {\n        border-radius: var(--space-2xs);\n        padding: var(--space-xs);\n        background: white;\n        flex: 1;\n        width: 100%;\n    }\n\n    dialog {\n        padding: 0;\n        border-radius: var(--space-2xs);\n        border: none;\n    }\n\n    dialog .qr {\n        background-color: white;\n        width: min(800px, 80vmin);\n        border: none;\n    }\n\n    .button-group {\n        display: grid;\n        grid-template-columns: 1fr 1fr;\n        justify-items: center;\n        gap: var(--space-s);\n        position: absolute;\n        top: 100%;\n        width: 100%;\n        transform: translateY(-50%);\n    }\n\n    .button-group button {\n        display: flex;\n        align-items: center;\n        gap: var(--space-xs);\n        border: none;\n        cursor: pointer;\n        background: var(--body-background-color);\n        color: var(--body-text-color);\n        font-size: var(--fs-0);\n    }\n\n    @media (max-width: 340px) {\n        .button-group button span:last-of-type {\n            display: none;\n        }\n    }\n\n    .icon {\n        display: grid;\n        place-content: center;\n        grid-template-areas: 'stack';\n    }\n\n    .icon > * {\n        grid-area: stack;\n    }\n\n    .check {\n        background: var(--body-background-color);\n    }\n</style>\n", "<script lang=\"ts\">\n    import {onMount} from 'svelte'\n\n    type TextareaProps = {\n        content?: string\n    }\n\n    export let data: TextareaProps = {}\n\n    let wrapper\n    let textarea\n    let maxOpacity = 0.2\n\n    function handleScroll(event) {\n        let scrollMax = event.target.scrollHeight - event.target.clientHeight\n        let currentScroll = event.target.scrollTop / scrollMax\n        let topShadowOpacity = currentScroll * maxOpacity\n        let bottomShadowOpacity = (1 - currentScroll) * maxOpacity\n        wrapper.style.setProperty('--top-shadow-opacity', topShadowOpacity)\n        wrapper.style.setProperty('--bottom-shadow-opacity', bottomShadowOpacity)\n    }\n\n    onMount(() => {\n        let startingOpacity =\n            (1 - textarea.scrollTop / (textarea.scrollHeight - textarea.clientHeight)) * maxOpacity\n        wrapper.style.setProperty('--bottom-shadow-opacity', startingOpacity)\n    })\n</script>\n\n<div class=\"wrapper\" bind:this={wrapper}>\n    <textarea bind:this={textarea} on:scroll={handleScroll} readOnly>{data.content}</textarea>\n</div>\n\n<style lang=\"scss\">\n    .wrapper {\n        position: relative;\n        display: flex;\n        display: grid;\n        background-color: var(--text-area-background);\n\n        // padding: var(--space-m);\n        border-radius: var(--border-radius-inner);\n        overflow: hidden;\n    }\n    textarea {\n        --rows: 9;\n        flex: 1;\n        color: var(--text-area-text-color);\n        background-color: var(--text-area-background);\n        border: none;\n        font-size: var(--fs-0);\n        font-weight: 400;\n        line-height: 1.5;\n        resize: none;\n        opacity: 0.75;\n        height: calc(var(--fs-0) * 1.5 * var(--rows) - var(--fs-0) * 1.5 * 0.5);\n        border-radius: inherit;\n        // width: 100%;\n        padding-inline-start: var(--space-m);\n        padding-block-start: var(--space-m);\n    }\n\n    .wrapper::before,\n    .wrapper::after {\n        content: '';\n        display: block;\n        position: absolute;\n        z-index: 2;\n        width: 100%;\n        height: var(--space-l);\n        background: linear-gradient(var(--deg), transparent, black 100%);\n    }\n    .wrapper::before {\n        --deg: 0;\n        top: 0;\n        opacity: var(--top-shadow-opacity, 0);\n    }\n    .wrapper::after {\n        --deg: 180deg;\n        bottom: 0;\n        opacity: var(--bottom-shadow-opacity, 0);\n    }\n</style>\n", "<script lang=\"ts\">\n    import {ComponentType, createE<PERSON><PERSON><PERSON><PERSON>tch<PERSON>, SvelteComponent} from 'svelte'\n    import {derived} from 'svelte/store'\n\n    import Accept from './components/Accept.svelte'\n    import Asset from './components/Asset.svelte'\n    import Button from './components/Button.svelte'\n    import Close from './components/Close.svelte'\n    import Link from './components/Link.svelte'\n    import Countdown from './components/Countdown.svelte'\n    import Qr from './components/Qr.svelte'\n    import Textarea from './components/Textarea.svelte'\n\n    import {prompt} from './state'\n    import BodyTitle from './components/BodyTitle.svelte'\n    import BodyText from './components/BodyText.svelte'\n    import Message from './components/Message.svelte'\n\n    interface UIComponent {\n        component: ComponentType<SvelteComponent>\n        props: Record<string, unknown>\n    }\n\n    const elements = derived(prompt, ($prompt) => {\n        const components: UIComponent[] = []\n        if ($prompt) {\n            $prompt.args.elements.forEach((element) => {\n                switch (element.type) {\n                    case 'accept': {\n                        components.push({\n                            component: Accept,\n                            props: {\n                                data: element.data,\n                            },\n                        })\n                        break\n                    }\n                    case 'asset': {\n                        components.push({\n                            component: Asset,\n                            props: {\n                                data: element.data,\n                            },\n                        })\n                        break\n                    }\n                    case 'close': {\n                        components.push({\n                            component: Close,\n                            props: {\n                                label: element.label,\n                            },\n                        })\n                        break\n                    }\n                    case 'link': {\n                        components.push({\n                            component: Link,\n                            props: {\n                                data: element.data,\n                            },\n                        })\n                        break\n                    }\n                    case 'qr': {\n                        components.push({\n                            component: Qr,\n                            props: {\n                                data: element.data,\n                            },\n                        })\n                        break\n                    }\n                    case 'countdown': {\n                        components.push({\n                            component: Countdown,\n                            props: {\n                                data: element.data,\n                            },\n                        })\n                        break\n                    }\n                    case 'textarea': {\n                        components.push({\n                            component: Textarea,\n                            props: {\n                                data: element.data,\n                            },\n                        })\n                        break\n                    }\n                    case 'button': {\n                        components.push({\n                            component: Button,\n                            props: {\n                                data: element.data,\n                            },\n                        })\n                        break\n                    }\n                    default: {\n                        throw new Error(`Unknown element type: ${element.type}`)\n                    }\n                }\n            })\n        }\n        return components\n    })\n\n    const dispatch = createEventDispatcher<{\n        complete: void\n        cancel: void\n    }>()\n</script>\n\n<div class=\"prompt\">\n    <div class=\"text\">\n        <BodyTitle>{$prompt?.args.title}</BodyTitle>\n        <BodyText>{$prompt?.args.body}</BodyText>\n    </div>\n    {#each $elements as component}\n        <svelte:component\n            this={component.component}\n            on:complete={() => dispatch('complete')}\n            on:cancel={() => dispatch('cancel')}\n            {...component.props}\n        />\n    {/each}\n</div>\n\n<style>\n    .prompt {\n        display: flex;\n        flex-direction: column;\n        gap: var(--space-m);\n        gap: var(--space-l);\n    }\n\n    .text {\n        display: flex;\n        flex-direction: column;\n        gap: var(--space-s);\n        text-align: center;\n    }\n</style>\n", "<script lang=\"ts\">\n    import {getContext} from 'svelte'\n    import {i18nType} from 'src/lib/translations'\n    import {version} from '../../../package.json'\n    import {get} from 'svelte/store'\n    import {settings} from '../state'\n    import BodyText from '../components/BodyText.svelte'\n    import BodyTitle from '../components/BodyTitle.svelte'\n    import Link from '../components/Link.svelte'\n\n    const {t} = getContext<i18nType>('i18n')\n\n    $: ({theme} = get(settings))\n</script>\n\n<div>\n    {#if theme === 'dark'}\n        <!-- Dark logo  -->\n        <!-- prettier-ignore -->\n        <svg width=\"513\" height=\"206\" viewBox=\"0 0 513 206\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M273.61 84.1001C273.61 83.4342 273.058 82.8944 272.378 82.8944H265.214C264.534 82.8944 263.982 83.4342 263.982 84.1001V106.924C263.982 110.157 263.289 112.358 262.115 113.737C260.978 115.074 259.221 115.831 256.607 115.831C253.93 115.831 252.13 115.069 250.967 113.728C249.771 112.35 249.066 110.153 249.066 106.924V96.1574C249.066 95.4915 248.514 94.9517 247.834 94.9517H240.615C239.934 94.9517 239.382 95.4915 239.382 96.1574V106.924C239.382 110.154 238.681 112.352 237.493 113.731C236.338 115.07 234.552 115.831 231.897 115.831C229.338 115.831 227.567 115.104 226.379 113.807C225.225 112.507 224.522 110.299 224.522 106.924V84.1001C224.522 83.4342 223.97 82.8944 223.289 82.8944H216.071C215.39 82.8944 214.838 83.4342 214.838 84.1001V107.145C214.838 112.609 216.261 116.971 219.295 120.031C222.329 123.091 226.593 124.542 231.897 124.542C234.764 124.542 237.338 124.099 239.591 123.178L239.601 123.174C241.392 122.423 242.936 121.451 244.214 120.248C245.47 121.452 247.003 122.424 248.792 123.174L248.816 123.184C251.134 124.098 253.738 124.542 256.607 124.542C261.906 124.542 266.154 123.113 269.153 120.086C272.189 117.025 273.61 112.643 273.61 107.145V84.1001Z\" fill=\"white\"/>\n            <path d=\"M326.664 84.1001C326.664 83.4342 326.112 82.8944 325.431 82.8944H318.212C317.532 82.8944 316.98 83.4342 316.98 84.1001V98.8652H299.343V84.1001C299.343 83.4342 298.791 82.8944 298.111 82.8944H290.892C290.211 82.8944 289.659 83.4342 289.659 84.1001V122.784C289.659 123.45 290.211 123.99 290.892 123.99H298.111C298.791 123.99 299.343 123.45 299.343 122.784V107.411H316.98V122.784C316.98 123.45 317.532 123.99 318.212 123.99H325.431C326.112 123.99 326.664 123.45 326.664 122.784V84.1001Z\" fill=\"white\"/>\n            <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M378.383 123.99C379.064 123.99 379.615 123.45 379.615 122.784V101.342C379.615 97.4046 378.867 93.9786 377.31 91.1134C375.795 88.2217 373.624 86.0139 370.804 84.5303C368.003 83.0569 364.759 82.3418 361.113 82.3418C357.467 82.3418 354.223 83.0569 351.422 84.5303C348.602 86.0138 346.413 88.2207 344.862 91.1105L344.859 91.1159C343.34 93.9801 342.611 97.4049 342.611 101.342V122.784C342.611 123.45 343.162 123.99 343.843 123.99H350.951C351.632 123.99 352.184 123.45 352.184 122.784V114.042H369.931V122.784C369.931 123.45 370.483 123.99 371.164 123.99H378.383ZM367.602 93.4381L367.611 93.4468C369.087 94.947 369.931 97.284 369.931 100.679V105.607H352.184V100.679C352.184 97.284 353.028 94.947 354.504 93.4469L354.513 93.438C356.005 91.891 358.13 91.0532 361.057 91.0532C363.985 91.0532 366.11 91.891 367.602 93.4381Z\" fill=\"white\"/>\n            <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M421.226 123.466C421.456 123.794 421.837 123.99 422.243 123.99H430.017C430.475 123.99 430.895 123.741 431.108 123.345C431.321 122.949 431.292 122.469 431.033 122.1L422.93 110.581C425.14 109.488 426.914 107.979 428.212 106.043C429.81 103.717 430.583 100.971 430.583 97.8605C430.583 94.8334 429.87 92.152 428.394 89.8665C426.919 87.5829 424.797 85.8508 422.084 84.6593C419.411 83.4688 416.306 82.8944 412.803 82.8944H396.81C396.13 82.8944 395.578 83.4342 395.578 84.1001V122.784C395.578 123.45 396.13 123.99 396.81 123.99H404.029C404.71 123.99 405.262 123.45 405.262 122.784V112.716H412.803C413.124 112.716 413.419 112.712 413.682 112.703L421.226 123.466ZM418.845 93.0951C420.138 94.1481 420.844 95.6746 420.844 97.8605C420.844 100.042 420.14 101.594 418.838 102.686C417.516 103.76 415.459 104.391 412.47 104.391H405.262V91.3847H412.47C415.463 91.3847 417.522 92.0186 418.845 93.0951Z\" fill=\"white\"/>\n            <path d=\"M453.875 122.784V109.179H469.579C470.26 109.179 470.812 108.639 470.812 107.973V101.895C470.812 101.229 470.26 100.689 469.579 100.689H453.875V98.5789C453.875 95.9432 454.648 94.1431 456.014 92.961C457.406 91.7559 459.581 91.0532 462.749 91.0532C466.036 91.0532 468.659 91.7993 470.701 93.2089C471.012 93.4237 471.408 93.4857 471.772 93.3768C472.136 93.268 472.428 93.0005 472.564 92.6522L474.841 86.7944C475.046 86.2644 474.847 85.6657 474.362 85.356C472.789 84.3525 470.976 83.6041 468.939 83.0974C466.904 82.5911 464.728 82.3418 462.416 82.3418C456.908 82.3418 452.417 83.7445 449.094 86.688C445.79 89.6112 444.191 93.6277 444.191 98.5789V122.784C444.191 123.45 444.743 123.99 445.424 123.99H452.643C453.323 123.99 453.875 123.45 453.875 122.784Z\" fill=\"white\"/>\n            <path d=\"M177.787 117.503C177.714 121.492 175.076 124.262 172.475 125.763L166.877 128.995C164.206 130.537 161.082 131.136 158.284 131.136C155.485 131.136 152.362 130.537 149.692 128.995L144.201 125.763V136.735C144.201 140.799 141.523 143.631 138.887 145.154L133.29 148.385C130.618 149.927 127.495 150.526 124.696 150.526C121.897 150.526 118.774 149.927 116.104 148.385L107.902 143.651L99.702 148.385C97.0305 149.927 93.9072 150.526 91.1085 150.526C88.3098 150.526 85.1879 149.927 82.5164 148.385L43.3313 125.763C40.73 124.262 38.0922 121.492 38.019 117.503L38.0176 117.257V131.625L38.019 131.871C38.0922 135.86 40.73 138.63 43.3313 140.131L82.5164 162.753C85.1879 164.295 88.3098 164.894 91.1085 164.894C93.9072 164.894 97.0305 164.295 99.702 162.753L107.902 158.018L116.104 162.753C118.774 164.295 121.897 164.894 124.696 164.894C127.495 164.894 130.618 164.295 133.29 162.753L138.887 159.521C141.523 157.999 144.201 155.167 144.201 151.103V140.131L149.692 143.363C152.362 144.905 155.485 145.504 158.284 145.504C161.082 145.504 164.206 144.905 166.877 143.363L172.475 140.131C175.076 138.63 177.714 135.86 177.787 131.871L177.787 117.503Z\" fill=\"#7BE7CE\"/>\n            <path d=\"M38.0526 103.767C38.0295 103.981 38.0176 104.198 38.0176 104.418V117.257L38.019 117.503C38.0922 121.492 40.73 124.262 43.3313 125.763L82.5164 148.385C85.1879 149.927 88.3097 150.526 91.1085 150.526C93.9072 150.526 97.0305 149.927 99.702 148.385L107.902 143.65L116.104 148.385C118.774 149.927 121.897 150.526 124.696 150.526C127.495 150.526 130.618 149.927 133.29 148.385L138.887 145.153C141.523 143.631 144.201 140.799 144.201 136.735V125.763L149.692 128.995C152.362 130.537 155.485 131.136 158.284 131.136C161.082 131.136 164.206 130.537 166.877 128.995L172.475 125.763C175.076 124.262 177.714 121.492 177.787 117.503V103.136C177.714 107.124 175.076 109.893 172.475 111.395L166.877 114.627C164.206 116.168 161.082 116.768 158.284 116.768C155.485 116.768 152.362 116.168 149.692 114.627L144.094 111.395C144.004 111.343 143.914 111.289 143.824 111.234C144.067 112.027 144.204 112.884 144.204 113.804C144.204 114.073 144.192 114.337 144.169 114.595C144.19 114.797 144.201 115.002 144.201 115.209V122.366C144.201 126.43 141.523 129.263 138.887 130.785L133.29 134.017C130.618 135.558 127.495 136.158 124.696 136.158C121.897 136.158 118.774 135.558 116.104 134.017L107.902 129.282L99.702 134.017C97.0305 135.558 93.9072 136.158 91.1085 136.158C88.3097 136.158 85.1879 135.558 82.5164 134.017L43.3313 111.395C40.8698 109.974 38.3755 107.417 38.0526 103.767Z\" fill=\"#B2F2E1\"/>\n            <path d=\"M105.231 65.0186C105.237 65.0767 105.243 65.1348 105.25 65.1928C105.531 67.6131 106.734 69.5192 108.231 70.9303C108.122 70.9285 108.014 70.9276 107.906 70.9276C107.031 70.9276 106.125 70.9862 105.21 71.1138V65.5255C105.21 65.3548 105.217 65.1857 105.231 65.0186Z\" fill=\"#B2F2E1\"/>\n            <path d=\"M48.9324 82.7639L43.3344 85.9955C40.8405 87.4353 38.3108 90.0472 38.044 93.7645C38.0265 93.939 38.0176 94.1214 38.0176 94.312V102.889L38.019 103.135C38.0922 107.124 40.73 109.894 43.3313 111.395L82.5164 134.017C85.1879 135.559 88.3097 136.158 91.1085 136.158C93.9072 136.158 97.0305 135.559 99.702 134.017L107.902 129.282L116.104 134.017C118.774 135.559 121.897 136.158 124.696 136.158C127.495 136.158 130.618 135.559 133.29 134.017L138.887 130.785C141.523 129.263 144.201 126.431 144.201 122.367V115.209C144.201 115.003 144.19 114.798 144.169 114.595C144.192 114.337 144.204 114.073 144.204 113.804C144.204 112.884 144.067 112.027 143.824 111.235C143.914 111.29 144.004 111.343 144.094 111.395L149.692 114.627C152.362 116.169 155.485 116.768 158.284 116.768C161.082 116.768 164.206 116.169 166.877 114.627L172.475 111.395C175.076 109.894 177.714 107.124 177.787 103.135V94.6988C177.79 94.6046 177.791 94.5098 177.791 94.4142C177.791 94.3187 177.79 94.2239 177.787 94.1297L177.788 75.2591C177.79 75.1813 177.791 75.103 177.791 75.0242C177.791 70.9603 175.115 68.1277 172.478 66.6055L133.292 43.9837C130.622 42.442 127.499 41.8428 124.7 41.8428C121.901 41.8428 118.778 42.442 116.107 43.9837L110.51 47.2154C107.873 48.7376 105.196 51.5702 105.196 55.6341C105.196 55.8256 105.202 56.0144 105.214 56.2005C105.211 56.2658 105.21 56.3317 105.21 56.3981V63.6424C105.191 63.9299 105.188 64.218 105.199 64.5058C105.206 64.7352 105.223 64.9644 105.25 65.1931C105.531 67.6133 106.734 69.5194 108.231 70.9306C108.122 70.9288 108.014 70.9279 107.906 70.9279C105.107 70.9279 101.984 71.5271 99.3137 73.0688L93.7158 76.3005C91.0789 77.8227 88.4022 80.6553 88.4022 84.7192C88.4022 84.9835 88.4135 85.2426 88.4354 85.4964C88.4306 85.5945 88.4282 85.693 88.4282 85.7922V92.5738L88.4179 92.7394L88.4077 92.9207C88.4004 93.0971 88.3989 93.2737 88.4019 93.4503C88.4077 93.8184 88.4384 94.1863 88.4925 94.552C88.5595 95.0033 88.6583 95.4358 88.7848 95.8499L66.1171 82.7639C63.4466 81.2222 60.3234 80.623 57.5247 80.623C54.726 80.623 51.6029 81.2222 48.9324 82.7639Z\" fill=\"#F4FAF4\"/>\n            <path d=\"M130.295 49.1697C127.204 47.3849 122.191 47.3849 119.1 49.1697L113.502 52.4013C110.41 54.1862 110.41 57.0799 113.502 58.8647L152.008 81.0944C153.493 81.9515 154.327 83.114 154.327 84.3261V85.1102C154.327 86.3223 153.493 87.4848 152.008 88.3419L147.089 91.1815C143.998 92.9663 143.998 95.8601 147.089 97.6449L152.687 100.877C155.779 102.661 160.791 102.661 163.883 100.877L169.481 97.6449C172.573 95.8601 172.573 92.9663 169.481 91.1815L164.562 88.3419C163.077 87.4848 162.243 86.3224 162.243 85.1102V84.3261C162.243 83.114 163.077 81.9515 164.562 81.0944L169.481 78.2548C172.573 76.47 172.573 73.5762 169.481 71.7914L130.295 49.1697Z\" fill=\"#494E62\"/>\n            <path d=\"M63.1203 87.9498C60.0287 86.165 55.0161 86.165 51.9245 87.9498L46.3265 91.1815C43.2349 92.9663 43.2349 95.8601 46.3265 97.6449L85.512 120.267C88.6037 122.051 93.6162 122.051 96.7079 120.267L107.904 113.803L63.1203 87.9498Z\" fill=\"#494E62\"/>\n            <path d=\"M128.656 104.5C128.656 105.712 129.49 106.875 130.975 107.732L135.893 110.572C138.985 112.356 138.985 115.25 135.893 117.035L130.295 120.267C127.204 122.051 122.191 122.051 119.1 120.267L107.904 113.803L118.42 107.732C119.905 106.875 120.739 105.712 120.739 104.5L120.739 103.716C120.739 102.504 119.905 101.342 118.42 100.485L96.7079 87.9498C93.6162 86.165 93.6162 83.2713 96.7079 81.4865L102.306 78.2548C105.397 76.47 110.41 76.47 113.502 78.2548L135.893 91.1815C138.985 92.9663 138.985 95.8601 135.893 97.6449L130.975 100.485C129.49 101.342 128.656 102.504 128.656 103.716L128.656 104.5Z\" fill=\"#494E62\"/>\n            </svg>\n    {:else}\n        <!-- Light logo  -->\n        <!-- prettier-ignore -->\n        <svg width=\"512\" height=\"206\" viewBox=\"0 0 512 206\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M273.403 84.085C273.403 83.4185 272.851 82.8783 272.171 82.8783H265.007C264.327 82.8783 263.775 83.4185 263.775 84.085V106.927C263.775 110.163 263.082 112.365 261.908 113.745C260.77 115.083 259.014 115.841 256.4 115.841C253.723 115.841 251.923 115.078 250.76 113.737C249.564 112.357 248.859 110.158 248.859 106.927V96.1519C248.859 95.4854 248.307 94.9452 247.627 94.9452H240.408C239.727 94.9452 239.175 95.4854 239.175 96.1519V106.927C239.175 110.16 238.474 112.36 237.286 113.74C236.131 115.08 234.345 115.841 231.689 115.841C229.131 115.841 227.36 115.114 226.172 113.816C225.018 112.514 224.315 110.304 224.315 106.927V84.085C224.315 83.4185 223.763 82.8783 223.082 82.8783H215.863C215.183 82.8783 214.631 83.4185 214.631 84.085V107.148C214.631 112.616 216.054 116.982 219.088 120.045C222.122 123.107 226.385 124.559 231.689 124.559C234.557 124.559 237.131 124.116 239.384 123.194L239.394 123.19C241.185 122.439 242.729 121.465 244.007 120.261C245.263 121.467 246.796 122.44 248.585 123.19L248.609 123.2C250.927 124.115 253.531 124.559 256.4 124.559C261.699 124.559 265.947 123.129 268.946 120.1C271.982 117.036 273.403 112.65 273.403 107.148V84.085Z\" fill=\"#494E62\"/>\n            <path d=\"M326.457 84.085C326.457 83.4185 325.905 82.8783 325.224 82.8783H318.005C317.325 82.8783 316.773 83.4185 316.773 84.085V98.8619H299.136V84.085C299.136 83.4185 298.584 82.8783 297.904 82.8783H290.685C290.004 82.8783 289.452 83.4185 289.452 84.085V122.8C289.452 123.466 290.004 124.006 290.685 124.006H297.904C298.584 124.006 299.136 123.466 299.136 122.8V107.414H316.773V122.8C316.773 123.466 317.325 124.006 318.005 124.006H325.224C325.905 124.006 326.457 123.466 326.457 122.8V84.085Z\" fill=\"#494E62\"/>\n            <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M378.176 124.006C378.857 124.006 379.408 123.466 379.408 122.8V101.341C379.408 97.4001 378.66 93.9713 377.103 91.1039C375.588 88.2098 373.417 86.0002 370.597 84.5155C367.796 83.0409 364.552 82.3252 360.906 82.3252C357.26 82.3252 354.016 83.0409 351.215 84.5155C348.395 86.0002 346.206 88.2088 344.655 91.1009L344.652 91.1063C343.133 93.9728 342.404 97.4004 342.404 101.341V122.8C342.404 123.466 342.955 124.006 343.636 124.006H350.744C351.425 124.006 351.977 123.466 351.977 122.8V114.051H369.724V122.8C369.724 123.466 370.276 124.006 370.957 124.006H378.176ZM367.395 93.4304L367.404 93.4391C368.88 94.9405 369.724 97.2793 369.724 100.677V105.609H351.977V100.677C351.977 97.2793 352.821 94.9405 354.297 93.4392L354.306 93.4303C355.798 91.882 357.923 91.0435 360.85 91.0435C363.778 91.0435 365.903 91.882 367.395 93.4304Z\" fill=\"#494E62\"/>\n            <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M421.019 123.482C421.249 123.81 421.63 124.006 422.036 124.006H429.81C430.268 124.006 430.688 123.758 430.901 123.361C431.114 122.964 431.085 122.485 430.826 122.116L422.723 110.587C424.933 109.494 426.707 107.983 428.005 106.045C429.603 103.717 430.376 100.969 430.376 97.8563C430.376 94.8268 429.663 92.1432 428.187 89.8559C426.712 87.5705 424.59 85.837 421.877 84.6445C419.204 83.4531 416.099 82.8783 412.596 82.8783H396.603C395.923 82.8783 395.371 83.4185 395.371 84.085V122.8C395.371 123.466 395.923 124.006 396.603 124.006H403.822C404.503 124.006 405.055 123.466 405.055 122.8V112.724H412.596C412.917 112.724 413.212 112.72 413.475 112.711L421.019 123.482ZM418.638 93.0871C419.931 94.1409 420.637 95.6687 420.637 97.8563C420.637 100.039 419.933 101.592 418.631 102.686C417.309 103.76 415.252 104.393 412.263 104.393H405.055V91.3754H412.263C415.256 91.3754 417.315 92.0098 418.638 93.0871Z\" fill=\"#494E62\"/>\n            <path d=\"M453.668 122.8V109.184H469.372C470.053 109.184 470.605 108.644 470.605 107.977V101.894C470.605 101.227 470.053 100.687 469.372 100.687H453.668V98.5753C453.668 95.9375 454.441 94.1359 455.807 92.9529C457.199 91.7469 459.374 91.0435 462.542 91.0435C465.829 91.0435 468.452 91.7903 470.494 93.201C470.805 93.416 471.2 93.478 471.565 93.3691C471.929 93.2601 472.221 92.9924 472.357 92.6438L474.634 86.7813C474.839 86.251 474.64 85.6517 474.155 85.3418C472.582 84.3375 470.769 83.5885 468.732 83.0814C466.697 82.5746 464.521 82.3252 462.209 82.3252C456.701 82.3252 452.209 83.729 448.887 86.6749C445.583 89.6004 443.984 93.6202 443.984 98.5753V122.8C443.984 123.466 444.536 124.006 445.217 124.006H452.436C453.116 124.006 453.668 123.466 453.668 122.8Z\" fill=\"#494E62\"/>\n            <path d=\"M177.58 117.515C177.507 121.507 174.869 124.279 172.268 125.782L166.67 129.016C163.999 130.559 160.875 131.159 158.077 131.159C155.278 131.159 152.155 130.559 149.485 129.016L143.994 125.782V136.762C143.994 140.829 141.316 143.664 138.68 145.188L133.083 148.422C130.411 149.965 127.288 150.565 124.489 150.565C121.69 150.565 118.567 149.965 115.897 148.422L107.695 143.683L99.495 148.422C96.8235 149.965 93.7002 150.565 90.9014 150.565C88.1027 150.565 84.9808 149.965 82.3093 148.422L43.1243 125.782C40.523 124.279 37.8851 121.507 37.812 117.515L37.8105 117.269V131.648L37.812 131.895C37.8851 135.886 40.523 138.659 43.1243 140.161L82.3093 162.801C84.9808 164.344 88.1027 164.944 90.9014 164.944C93.7002 164.944 96.8235 164.344 99.495 162.801L107.695 158.063L115.897 162.801C118.567 164.344 121.69 164.944 124.489 164.944C127.288 164.944 130.411 164.344 133.083 162.801L138.68 159.567C141.316 158.044 143.994 155.209 143.994 151.142V140.161L149.485 143.396C152.155 144.939 155.278 145.538 158.077 145.538C160.875 145.538 163.999 144.939 166.67 143.396L172.268 140.161C174.869 138.659 177.507 135.886 177.58 131.895L177.58 117.515Z\" fill=\"#F4FAF4\"/>\n            <path d=\"M37.8456 103.768C37.8224 103.982 37.8105 104.199 37.8105 104.419V117.268L37.812 117.515C37.8851 121.507 40.523 124.279 43.1243 125.782L82.3093 148.422C84.9808 149.964 88.1027 150.564 90.9014 150.564C93.7002 150.564 96.8235 149.964 99.495 148.422L107.695 143.683L115.897 148.422C118.567 149.964 121.69 150.564 124.489 150.564C127.288 150.564 130.411 149.964 133.083 148.422L138.68 145.187C141.316 143.664 143.994 140.829 143.994 136.762V125.782L149.484 129.016C152.155 130.559 155.278 131.159 158.077 131.159C160.875 131.159 163.999 130.559 166.67 129.016L172.268 125.782C174.869 124.279 177.507 121.507 177.58 117.515V103.136C177.506 107.127 174.869 109.899 172.268 111.402L166.67 114.636C163.999 116.179 160.875 116.779 158.077 116.779C155.278 116.779 152.155 116.179 149.484 114.636L143.887 111.402C143.797 111.35 143.707 111.296 143.617 111.241C143.86 112.035 143.997 112.892 143.997 113.813C143.997 114.082 143.985 114.346 143.962 114.604C143.983 114.807 143.994 115.012 143.994 115.219V122.382C143.994 126.449 141.316 129.284 138.68 130.808L133.083 134.042C130.411 135.585 127.288 136.184 124.489 136.184C121.69 136.184 118.567 135.585 115.897 134.042L107.695 129.303L99.495 134.042C96.8235 135.585 93.7002 136.184 90.9014 136.184C88.1027 136.184 84.9808 135.585 82.3093 134.042L43.1243 111.402C40.6627 109.98 38.1684 107.421 37.8456 103.768Z\" fill=\"#B2F2E1\"/>\n            <path d=\"M105.024 64.9883C105.03 65.0464 105.036 65.1046 105.043 65.1627C105.324 67.5849 106.527 69.4925 108.024 70.9048C107.915 70.903 107.807 70.9021 107.699 70.9021C106.824 70.9021 105.918 70.9607 105.003 71.0884V65.4957C105.003 65.3248 105.01 65.1556 105.024 64.9883Z\" fill=\"#B2F2E1\"/>\n            <path d=\"M48.7253 82.7478L43.1274 85.9821C40.6334 87.423 38.1038 90.037 37.837 93.7573C37.8195 93.932 37.8105 94.1145 37.8105 94.3053V102.889L37.812 103.135C37.8851 107.127 40.523 109.899 43.1243 111.402L82.3093 134.042C84.9808 135.585 88.1027 136.185 90.9014 136.185C93.7001 136.185 96.8235 135.585 99.495 134.042L107.695 129.304L115.897 134.042C118.567 135.585 121.69 136.185 124.489 136.185C127.288 136.185 130.411 135.585 133.083 134.042L138.68 130.808C141.316 129.284 143.994 126.45 143.994 122.382V115.219C143.994 115.012 143.983 114.807 143.962 114.605C143.985 114.346 143.997 114.082 143.997 113.813C143.997 112.892 143.86 112.035 143.617 111.241C143.707 111.296 143.797 111.35 143.887 111.402L149.484 114.636C152.155 116.179 155.278 116.779 158.077 116.779C160.875 116.779 163.999 116.179 166.67 114.636L172.268 111.402C174.869 109.899 177.507 107.127 177.58 103.135V94.6923C177.583 94.5981 177.584 94.5031 177.584 94.4075C177.584 94.3119 177.583 94.217 177.58 94.1228L177.581 75.2371C177.583 75.1592 177.584 75.0808 177.584 75.0019C177.584 70.9348 174.908 68.0999 172.271 66.5765L133.085 43.9366C130.415 42.3936 127.292 41.7939 124.493 41.7939C121.694 41.7939 118.571 42.3936 115.9 43.9366L110.303 47.1708C107.666 48.6943 104.989 51.5291 104.989 55.5963C104.989 55.788 104.995 55.9769 105.006 56.1631C105.004 56.2285 105.003 56.2944 105.003 56.3609V63.611C104.984 63.8988 104.981 64.1871 104.992 64.4752C104.999 64.7047 105.016 64.9341 105.043 65.163C105.324 67.5851 106.527 69.4928 108.024 70.9051C107.915 70.9033 107.807 70.9024 107.699 70.9024C104.9 70.9024 101.777 71.5021 99.1067 73.045L93.5087 76.2793C90.8719 77.8027 88.1951 80.6376 88.1951 84.7047C88.1951 84.9692 88.2065 85.2285 88.2284 85.4826C88.2236 85.5807 88.2212 85.6793 88.2212 85.7785V92.5656L88.2109 92.7314L88.2007 92.9128C88.1934 93.0894 88.1919 93.2661 88.1948 93.4428C88.2007 93.8112 88.2314 94.1794 88.2855 94.5454C88.3525 94.997 88.4513 95.4299 88.5777 95.8443L65.9101 82.7478C63.2396 81.2049 60.1164 80.6052 57.3177 80.6052C54.519 80.6052 51.3958 81.2049 48.7253 82.7478Z\" fill=\"#7BE7CE\"/>\n            <path d=\"M130.088 49.1268C126.997 47.3406 121.984 47.3405 118.893 49.1268L113.295 52.3611C110.203 54.1473 110.203 57.0434 113.295 58.8296L151.801 81.0771C153.286 81.9349 154.12 83.0983 154.12 84.3114V85.0962C154.12 86.3092 153.286 87.4726 151.801 88.3304L146.882 91.1723C143.791 92.9586 143.791 95.8546 146.882 97.6409L152.48 100.875C155.572 102.661 160.584 102.661 163.676 100.875L169.274 97.6409C172.366 95.8546 172.366 92.9586 169.274 91.1723L164.355 88.3304C162.87 87.4726 162.036 86.3092 162.036 85.0962V84.3114C162.036 83.0983 162.87 81.9349 164.355 81.0771L169.274 78.2352C172.366 76.449 172.366 73.5529 169.274 71.7667L130.088 49.1268Z\" fill=\"#494E62\"/>\n            <path d=\"M62.9133 87.938C59.8216 86.1518 54.8091 86.1518 51.7174 87.938L46.1195 91.1723C43.0279 92.9585 43.0279 95.8546 46.1195 97.6408L85.305 120.281C88.3967 122.067 93.4092 122.067 96.5009 120.281L107.697 113.812L62.9133 87.938Z\" fill=\"#494E62\"/>\n            <path d=\"M128.449 104.502C128.449 105.715 129.283 106.878 130.768 107.736L135.686 110.578C138.778 112.364 138.778 115.26 135.686 117.046L130.088 120.281C126.997 122.067 121.984 122.067 118.893 120.281L107.697 113.812L118.213 107.736C119.698 106.878 120.532 105.715 120.532 104.502L120.532 103.717C120.532 102.504 119.698 101.341 118.213 100.483L96.5009 87.938C93.4092 86.1518 93.4092 83.2557 96.5009 81.4695L102.099 78.2352C105.19 76.449 110.203 76.449 113.295 78.2352L135.686 91.1723C138.778 92.9585 138.778 95.8546 135.686 97.6409L130.768 100.483C129.283 101.341 128.449 102.504 128.449 103.717L128.449 104.502Z\" fill=\"#494E62\"/>\n            </svg>\n    {/if}\n    <BodyTitle>{$t('settings.about.version', {version})}</BodyTitle>\n    <BodyText>\n        {$t('settings.about.author')}\n    </BodyText>\n    <Link\n        data={{\n            button: true,\n            variant: 'primary',\n            label: $t('settings.about.link'),\n            href: 'https://wharfkit.com',\n            target: '_blank',\n        }}\n    />\n</div>\n\n<style>\n    div {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        gap: var(--space-m);\n    }\n\n    svg {\n        height: auto;\n        width: 100%;\n        max-width: 200px;\n    }\n</style>\n", "<script lang=\"ts\">\n    import Icon from '../components/Icon.svelte'\n    import type {SelectorOptions} from 'src/types'\n\n    export let name: string\n    export let value: SelectorOptions<any>['value']\n    export let checked: boolean\n    export let group: string | undefined\n    export let onChange: () => void\n    export let hidden: boolean\n    export let label\n</script>\n\n<label>\n    <slot>\n        <input type=\"radio\" {name} {value} {checked} {hidden} {group} on:change={onChange} />\n\n        {label}\n\n        {#if checked}\n            <div class=\"trailing\">\n                <Icon name={'check'} />\n            </div>\n        {/if}\n    </slot>\n</label>\n\n<style>\n    label {\n        flex: 1;\n        height: 100%;\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        cursor: pointer;\n        padding-inline: var(--space-s);\n    }\n</style>\n", "<script lang=\"ts\">\n    import List from '../components/List.svelte'\n    import ListItem from '../components/ListItem.svelte'\n    import ListOption from '../components/ListOption.svelte'\n    import type {SelectorOptions} from 'src/types'\n    import {settings} from '../state'\n\n    export let setting: string\n    export let options: SelectorOptions<any>[]\n\n    // Allows for custom onChange handlers\n    export let onChange: (value: SelectorOptions<any>['value']) => void = (value) => {\n        $settings[setting] = value\n    }\n</script>\n\n<List>\n    {#each options as option}\n        <ListItem>\n            <ListOption\n                label={option.label}\n                name={setting}\n                value={option.value}\n                checked={$settings[setting] === option.value}\n                bind:group={$settings[setting]}\n                onChange={() => onChange(option.value)}\n                hidden\n            />\n        </ListItem>\n    {/each}\n</List>\n", "<script lang=\"ts\">\n    import {onMount, getContext} from 'svelte'\n    import {backAction, props, router, transitionDirection, initRouter, settings} from './state'\n    import {i18nType} from '../lib/translations'\n    import List from './components/List.svelte'\n    import ListItem from './components/ListItem.svelte'\n    import Transition from './components/Transition.svelte'\n    import About from './settings/About.svelte'\n    import languages from '../lib/translations/lang.json'\n    import Selector from './settings/Selector.svelte'\n    import {get} from 'svelte/store'\n\n    const settingsRouter = initRouter()\n\n    const {t, setLocale} = getContext<i18nType>('i18n')\n\n    function closeSettings() {\n        $transitionDirection = 'ltr'\n        router.back()\n        backAction.set(undefined)\n    }\n\n    function navigateTo(path: string) {\n        $transitionDirection = 'rtl'\n        settingsRouter.push(path)\n        $props.subtitle = $t(`settings.${path}.title`)\n        backAction.set(() => {\n            $transitionDirection = 'ltr'\n            settingsRouter.back()\n            backAction.set(closeSettings)\n            $props.subtitle = undefined\n        })\n    }\n\n    onMount(() => {\n        backAction.set(closeSettings)\n        $props.title = $t('settings.title')\n        $props.subtitle = undefined\n        $transitionDirection = 'rtl'\n    })\n\n    $: animationOptions = [\n        {\n            label: $t('settings.animations.enabled'),\n            value: true,\n        },\n        {\n            label: $t('settings.animations.disabled'),\n            value: false,\n        },\n    ]\n\n    $: languageOptions = Object.keys(languages).map((lang) => ({\n        label: languages[lang],\n        value: lang,\n    }))\n\n    $: themeOptions = [\n        {\n            label: $t('settings.theme.automatic'),\n            value: undefined,\n        },\n        {\n            label: $t('settings.theme.light'),\n            value: 'light',\n        },\n        {\n            label: $t('settings.theme.dark'),\n            value: 'dark',\n        },\n    ]\n\n    async function changeLanguage(locale: string) {\n        const success = await setLocale(locale)\n        if (success) {\n            settings.set({\n                ...get(settings),\n                language: locale,\n            })\n            // Update the header immediately\n            $props.title = $t('settings.title')\n            $props.subtitle = $t('settings.language.title')\n        }\n    }\n</script>\n\n<div class=\"settings-menu\">\n    {#if !$settingsRouter.path}\n        <Transition direction={$transitionDirection}>\n            <List>\n                <ListItem\n                    label={$t(`settings.theme.title`)}\n                    onClick={() => navigateTo('theme')}\n                    leadingIcon=\"theme\"\n                    value={$settings.theme\n                        ? $t(`settings.theme.${$settings.theme}`)\n                        : $t('settings.theme.automatic')}\n                />\n                <ListItem\n                    label={$t(`settings.language.title`)}\n                    onClick={() => navigateTo('language')}\n                    leadingIcon=\"globe\"\n                    value={languages[$settings.language]}\n                />\n                <ListItem\n                    label={$t(`settings.animations.title`)}\n                    onClick={() => navigateTo('animations')}\n                    leadingIcon=\"waves\"\n                    value={$settings.animations\n                        ? $t(`settings.animations.enabled`)\n                        : $t('settings.animations.disabled')}\n                />\n                <ListItem\n                    label={$t('settings.about.title')}\n                    onClick={() => navigateTo('about')}\n                    leadingIcon=\"info\"\n                />\n                <ListItem\n                    label={$t('settings.github')}\n                    link=\"https://www.github.com/wharfkit\"\n                    leadingIcon=\"github\"\n                    trailingIcon=\"external-link\"\n                />\n            </List>\n        </Transition>\n    {/if}\n    {#if $settingsRouter.path === 'about'}\n        <Transition direction={$transitionDirection}>\n            <About />\n        </Transition>\n    {:else if $settingsRouter.path === 'theme'}\n        <Transition direction={$transitionDirection}>\n            <Selector setting=\"theme\" options={themeOptions} />\n        </Transition>\n    {:else if $settingsRouter.path === 'language'}\n        <Transition direction={$transitionDirection}>\n            <Selector\n                setting=\"language\"\n                options={languageOptions}\n                onChange={(locale) => changeLanguage(locale)}\n            />\n        </Transition>\n    {:else if $settingsRouter.path === 'animations'}\n        <Transition direction={$transitionDirection}>\n            <Selector setting=\"animations\" options={animationOptions} />\n        </Transition>\n    {/if}\n</div>\n", "<script lang=\"ts\">\n    import {createEventDispatcher, getContext} from 'svelte'\n\n    import {i18nType} from 'src/lib/translations'\n    import Countdown from './components/Countdown.svelte'\n\n    const {t} = getContext<i18nType>('i18n')\n\n    const dispatch = createEventDispatcher<{\n        complete: void\n        cancel: void\n    }>()\n\n    const complete = () => {\n        dispatch('complete')\n    }\n\n    const cancel = () => {\n        dispatch('cancel')\n    }\n</script>\n\n<Countdown\n    data={{\n        label: $t('transact.processing', {default: 'Performing transaction...'}),\n    }}\n/>\n", "<script lang=\"ts\">\n    import {createEventDispatcher, setContext} from 'svelte'\n    import {AbstractAccountCreationPlugin, Checksum256} from '@wharfkit/session'\n    import List from '../components/List.svelte'\n    import ListItem from '../components/ListItem.svelte'\n    import BodyTitle from '../components/BodyTitle.svelte'\n    import {getThemedLogo} from '../../lib/utils'\n\n    export let plugins: AbstractAccountCreationPlugin[]\n    export let title: string\n\n    const dispatch = createEventDispatcher<{\n        select: string\n        cancel: void\n    }>()\n</script>\n\n{#if plugins}\n    <section>\n        <BodyTitle>{title}</BodyTitle>\n        <List>\n            {#each plugins as plugin}\n                <ListItem\n                    label={plugin.name}\n                    onClick={() => dispatch('select', plugin.id)}\n                    leadingIcon=\"wharf\"\n                    logo={getThemedLogo(plugin.metadata)}\n                />\n            {/each}\n        </List>\n    </section>\n{/if}\n\n<style lang=\"scss\">\n    section {\n        display: flex;\n        flex-direction: column;\n        gap: var(--space-s);\n    }\n</style>\n", "<script lang=\"ts\">\n    import {createEventDispatcher, getContext, onDestroy, onMount} from 'svelte'\n    import {\n        ChainDefinition,\n        AccountCreationPlugin,\n        type UserInterfaceAccountCreationResponse,\n    } from '@wharfkit/session'\n    import {\n        backAction,\n        accountCreationContext,\n        accountCreationResponse,\n        props,\n        transitionDirection,\n    } from './state'\n\n    import {i18nType} from 'src/lib/translations'\n    import Transition from './components/Transition.svelte'\n    import AccountPlugin from './createAccount/AccountPlugin.svelte'\n    import Blockchain from './login/Blockchain.svelte'\n    import Countdown from './components/Countdown.svelte'\n    import {derived, Readable} from 'svelte/store'\n    const {t} = getContext<i18nType>('i18n')\n\n    let completed = false\n\n    const dispatch = createEventDispatcher<{\n        complete: UserInterfaceAccountCreationResponse\n        cancel: void\n    }>()\n\n    enum Steps {\n        done = 'done',\n        selectPlugin = 'selectPlugin',\n        selectChain = 'selectChain',\n    }\n\n    const accountPlugin: Readable<AccountCreationPlugin | undefined> = derived(\n        [accountCreationContext, accountCreationResponse],\n        ([$currentContext, $currentResponse]) => {\n            if (!$currentContext || !$currentResponse) {\n                return undefined\n            }\n            const plugin = $currentContext.accountCreationPlugins.find(\n                (plugin) => plugin.id === $currentResponse.pluginId\n            )\n            // If the new plugin only supports one chain, set it as the current\n            if (!$currentResponse.chain && plugin?.config.supportedChains?.length === 1) {\n                $currentResponse.chain = plugin.config.supportedChains[0].id\n            }\n            return plugin\n        }\n    )\n\n    let chains: Readable<ChainDefinition[]> = derived(\n        [accountCreationContext, accountPlugin],\n        ([$currentContext, $currentAccountPlugin]) => {\n            if ($currentContext && $currentAccountPlugin) {\n                // If the selected plugin has an array of supported chains, filter the list of chains\n                if ($currentAccountPlugin.config.supportedChains) {\n                    if ($currentContext.chains) {\n                        return $currentContext.chains.filter((chain) => {\n                            return (\n                                // If the chain is in the list of supported chains\n                                $currentAccountPlugin.config.supportedChains?.find((c) =>\n                                    c.id.equals(chain.id)\n                                )\n                            )\n                        })\n                    }\n                }\n            } else if ($currentContext) {\n                return $currentContext.chains\n            }\n            return []\n        }\n    )\n\n    const accountCreationContextUnsubscribe = accountCreationContext.subscribe((currentContext) => {\n        if (currentContext) {\n            // If an appName is specified, use it\n            $props.subtitle = $t('login.title-app', {\n                appName: currentContext.appName,\n                default: 'Login to {{appName}}',\n            })\n\n            // If only one account creation plugin is available, set it on the response\n            if (currentContext.accountCreationPlugins.length === 1) {\n                $accountCreationResponse.pluginId = currentContext.accountCreationPlugins[0].id\n            }\n\n            // If only one chain is available, set it on the response\n            if (currentContext.chain) {\n                $accountCreationResponse.chain = currentContext.chain.id\n            } else if (currentContext.chains && currentContext.chains.length === 1) {\n                $accountCreationResponse.chain = currentContext.chains[0].id\n            }\n        }\n    })\n\n    onMount(() => {\n        // TODO: add translation strings\n        $props.title = $t('accountCreation.title', {default: 'Create Account'})\n    })\n\n    onDestroy(accountCreationContextUnsubscribe)\n\n    const complete = () => {\n        if (!completed) {\n            completed = true\n\n            // For cases, where no UI interactions are needed,we are giving the UI a chance to set the state before completing\n            setTimeout(() => {\n                dispatch('complete', $accountCreationResponse)\n                backAction.set(undefined)\n            }, 100)\n        }\n    }\n\n    const step = derived(\n        [accountCreationContext, accountCreationResponse, accountPlugin, chains],\n        ([$context, $currentResponse, $currentAccountPlugin, $chains]) => {\n            if (!$currentAccountPlugin && $context?.uiRequirements.requiresPluginSelect) {\n                return Steps.selectPlugin\n            }\n\n            let requiresChainSelect = $currentAccountPlugin?.config.requiresChainSelect\n\n            // If requiresChainSelect is specified as false, never present the chain selection UI, in all other cases, use the context\n            if (requiresChainSelect !== false) {\n                requiresChainSelect = $context?.uiRequirements.requiresChainSelect\n            }\n\n            if (\n                !$currentResponse.chain &&\n                requiresChainSelect\n            ) {\n                return Steps.selectChain\n            }\n\n            // Return response to kit for the account creation\n            complete()\n        }\n    )\n\n    // TODO: Define the type for this event prop\n    const selectPlugin = (e) => {\n        $accountCreationResponse.pluginId = e.detail\n        $backAction = unselectPlugin\n        $transitionDirection = 'rtl'\n    }\n\n    const unselectPlugin = (e) => {\n        $accountCreationResponse.pluginId = undefined\n        $backAction = undefined\n        $transitionDirection = 'ltr'\n    }\n\n    const selectChain = (e) => {\n        $accountCreationResponse.chain = e.detail\n        $backAction = unselectChain\n        $transitionDirection = 'rtl'\n    }\n\n    const unselectChain = (e) => {\n        $accountCreationResponse.chain = undefined\n        $backAction = unselectPlugin\n        $transitionDirection = 'ltr'\n    }\n\n    const cancel = () => {\n        dispatch('cancel')\n    }\n</script>\n\n{#if $props && $accountCreationContext}\n    {#if $step === Steps.selectPlugin}\n        <Transition direction={$transitionDirection}>\n            <!-- TODO: Finalize the translation strings here. -->\n            <AccountPlugin\n                on:select={selectPlugin}\n                on:cancel={cancel}\n                plugins={$accountCreationContext.accountCreationPlugins}\n                title={$t('accountCreation.select.plugin', {default: 'Select a Service Provider'})}\n            />\n        </Transition>\n    {:else if $step === Steps.selectChain && $chains}\n        <Transition direction={$transitionDirection}>\n            <!-- TODO: Finalize the translation strings here. -->\n            <Blockchain\n                on:select={selectChain}\n                on:cancel={unselectChain}\n                chains={$chains}\n                title={$t('accountCreation.select.chain', {default: 'Select a Blockchain'})}\n            />\n        </Transition>\n    {:else}\n        <!-- TODO: add translation string here  -->\n        <Countdown\n            data={{\n                label: $t('accountCreation.countdown', {default: 'Creating Account'}),\n            }}\n        />\n    {/if}\n{:else}\n    <p>{$t('loading', {default: 'Loading...'})}</p>\n{/if}\n", "<script lang=\"ts\">\n    import type {ComponentProps} from 'svelte'\n    import Icon from './Icon.svelte'\n    export let onClick\n    export let icon: ComponentProps<Icon>['name']\n</script>\n\n<button on:click={onClick}>\n    <span class=\"background\" />\n    <Icon name={icon} />\n    <span class=\"label visually-hidden\">{icon}</span>\n</button>\n\n<style lang=\"scss\">\n    button {\n        --button-size: 46px;\n        --button-size: var(--space-2xl);\n        position: relative;\n        isolation: isolate;\n        background: var(--header-button-background);\n        border: 1px solid var(--header-button-outline);\n        border: none;\n        box-shadow: inset 0 0 0 1px var(--header-button-outline);\n        border-radius: var(--border-radius-inner);\n        cursor: pointer;\n        width: var(--button-size);\n        height: var(--button-size);\n        display: grid;\n        place-content: center;\n        color: var(--header-text-color);\n        transition: transform 80ms ease;\n\n        &:active {\n            transform: scale(95%);\n            transform-origin: center;\n        }\n    }\n\n    @media (hover: hover) {\n        button:hover .background {\n            opacity: 1;\n        }\n    }\n\n    .background {\n        position: absolute;\n        border-radius: var(--border-radius-inner);\n        inset: 0;\n        opacity: 0;\n        z-index: -1;\n        transition: opacity 80ms ease;\n        background: var(--header-button-outline);\n    }\n\n    .visually-hidden {\n        border: 0;\n        clip: rect(0 0 0 0);\n        height: auto;\n        margin: 0;\n        overflow: hidden;\n        padding: 0;\n        position: absolute;\n        width: 1px;\n        white-space: nowrap;\n    }\n</style>\n", "<script lang=\"ts\">\n    // export let animate: boolean = true\n\n    let motion = 'linear'\n    let frequency = 7000\n\n    let fgFrequency = 10000\n    let mgFrequency = 9500\n    let bgFrequency = 8600\n\n    let containerHeight = 25\n\n    let fgHeight = 50\n    let mgHeight = 75\n    let bgHeight = 100\n\n    let fgSwell = 1.2\n    let mgSwell = 1.4\n    let bgSwell = 1.3\n\n    let fgSwellSpeed = 3100\n    let mgSwellSpeed = 2300\n    let bgSwellSpeed = 1000\n\n    let fgSwellDelay = 9000\n    let mgSwellDelay = 7900\n    let bgSwellDelay = 9100\n\n    const foregroundFill = 'var(--wave-foreground-color)'\n    const midgroundFill = 'var(--wave-midground-color)'\n    const backgroundFill = 'var(--wave-background-color)'\n</script>\n\n<div\n    class=\"wrapper\"\n    style=\"\n    --frequency: {frequency}ms;\n    --foreground-speed: {fgFrequency}ms;\n    --midground-speed: {mgFrequency}ms; \n    --background-speed: {bgFrequency}ms; \n    --container-height: {containerHeight}px;\n    --motion: {motion};\n    --foreground-swell: {fgSwell};\n    --midground-swell: {mgSwell};\n    --background-swell: {bgSwell};\n    --foreground-swell-speed: {fgSwellSpeed}ms;\n    --midground-swell-speed: {mgSwellSpeed}ms;\n    --background-swell-speed: {bgSwellSpeed}ms;\n    --foreground-delay: {fgSwellDelay}ms;\n    --midground-delay: {mgSwellDelay}ms;\n    --background-delay: {bgSwellDelay}ms;\n    \"\n>\n    <svg height=\"0\" width=\"0\">\n        <defs>\n            <clipPath id=\"wave-clip\">\n                <path\n                    d=\"M 0 300 V 100 Q 100 0 200 100 Q 300 200 400 100 Q 500 0 600 100 Q 700 200 800 100 V 300\"\n                />\n            </clipPath>\n        </defs>\n    </svg>\n\n    <div class=\"container background\">\n        <svg\n            class=\"wave background\"\n            width=\"100%\"\n            height=\"{bgHeight}%\"\n            viewBox=\"0 0 800 300\"\n            preserveAspectRatio=\"none\"\n        >\n            <rect class=\"clipped\" height=\"100%\" width=\"100%\" fill={backgroundFill} />\n        </svg>\n    </div>\n\n    <div class=\"container midground\">\n        <svg\n            class=\"wave midground\"\n            width=\"100%\"\n            height=\"{mgHeight}%\"\n            viewBox=\"0 0 800 300\"\n            preserveAspectRatio=\"none\"\n        >\n            <rect class=\"clipped\" width=\"100%\" height=\"100%\" fill={midgroundFill} />\n        </svg>\n    </div>\n\n    <div class=\"container foreground\">\n        <svg\n            class=\"wave foreground\"\n            width=\"100%\"\n            height=\"{fgHeight}%\"\n            viewBox=\"0 0 800 300\"\n            preserveAspectRatio=\"none\"\n        >\n            <rect class=\"clipped\" width=\"100%\" height=\"100%\" fill={foregroundFill} />\n        </svg>\n    </div>\n</div>\n\n<style>\n    .wrapper {\n        transform-origin: top;\n        overflow: hidden;\n        position: relative;\n        height: var(--container-height);\n        background-color: var(--header-background-color);\n        /* background: transparent; */\n    }\n\n    .clipped {\n        clip-path: url(#wave-clip);\n    }\n\n    .container {\n        position: absolute;\n        left: 0;\n        bottom: 0;\n        width: 200%;\n        height: 100%;\n        transform-origin: bottom;\n    }\n\n    .container.foreground {\n        /* animation: wave-slide var(--motion) infinite var(--foreground-speed); */\n        /* transform: scaleY(var(--foreground-scale)); */\n    }\n\n    .container.midground {\n        /* animation: wave-slide var(--motion) infinite var(--midground-speed); */\n        /* transform: scaleY(var(--midground-scale)); */\n    }\n\n    .container.background {\n        /* animation: wave-slide var(--motion) infinite var(--background-speed); */\n        /* transform: scaleY(var(--background-scale)); */\n    }\n\n    @keyframes wave-slide {\n        from {\n            transform: translate(0);\n        }\n        to {\n            transform: translate(-50%);\n        }\n    }\n\n    .wave {\n        position: absolute;\n        /* left: 0; */\n        bottom: -2px;\n        opacity: 0.9999;\n        /* transition: all 200ms ease; */\n        transform-origin: bottom;\n        /* animation: wave-swell ease-in-out infinite alternate var(--swell-speed) var(--swell-delay); */\n    }\n\n    .wave.foreground {\n        --swell: var(--foreground-swell);\n        --swell-speed: var(--foreground-swell-speed);\n        --swell-delay: var(--foreground-delay);\n    }\n    .wave.midground {\n        --swell: var(--midground-swell);\n        --swell-speed: var(--midground-swell-speed);\n        --swell-delay: var(--midground-delay);\n    }\n    .wave.background {\n        --swell: var(--background-swell);\n        --swell-speed: var(--background-swell-speed);\n        --swell-delay: var(--background-delay);\n    }\n\n    @keyframes wave-swell {\n        from {\n            transform: scaleY(1);\n        }\n        to {\n            transform: scaleY(var(--swell));\n        }\n    }\n</style>\n", "<script lang=\"ts\">\n    import {createEventDispatcher} from 'svelte'\n    import HeaderButton from './HeaderButton.svelte'\n    import {allowSettings, backAction, router, transitionDirection} from '../state'\n    import HeaderWaves from './HeaderWaves.svelte'\n\n    export let title: string\n    export let subtitle: string | undefined\n\n    const dispatch = createEventDispatcher<{\n        cancel: void\n    }>()\n</script>\n\n<div class=\"modal-header\">\n    <div class=\"slot left\">\n        <slot name=\"left\">\n            {#if $backAction}\n                <HeaderButton icon=\"chevron-left\" onClick={$backAction} />\n            {:else if $allowSettings}\n                <HeaderButton\n                    icon=\"settings\"\n                    onClick={() => {\n                        router.push('settings')\n                        $transitionDirection = 'rtl'\n                    }}\n                />\n            {/if}\n        </slot>\n    </div>\n    <div class=\"slot center\">\n        <slot name=\"center\">\n            <h2>{title}</h2>\n            {#if subtitle}\n                <p>{subtitle}</p>\n            {/if}\n        </slot>\n    </div>\n    <div class=\"slot right\">\n        <slot name=\"right\">\n            <HeaderButton icon=\"close\" onClick={() => dispatch('cancel')} />\n        </slot>\n    </div>\n</div>\n\n<HeaderWaves />\n\n<style lang=\"scss\">\n    .modal-header {\n        box-sizing: border-box;\n        min-height: var(--header-height);\n        color: var(--header-text-color);\n        background: var(--header-background-color);\n        display: grid;\n        grid-template-columns: 1fr auto 1fr;\n        // grid-template-columns: 1fr 2fr 1fr;\n        gap: var(--space-s);\n        padding: var(--space-m);\n\n        .slot {\n            display: flex;\n            align-items: center;\n        }\n\n        .center {\n            flex-direction: column;\n            justify-content: space-around;\n            text-align: center;\n        }\n\n        .right {\n            justify-content: flex-end;\n        }\n\n        :is(h2, p) {\n            color: var(--header-text-color);\n            margin: 0;\n            line-height: 1.1em;\n        }\n\n        h2 {\n            font-size: var(--fs-3);\n            font-weight: 700;\n        }\n        p {\n            font-size: var(--fs-0);\n        }\n    }\n</style>\n", "<script lang=\"ts\">\n    import Header from './Header.svelte'\n    import {active, cancelablePromises, resetState, props, settings} from '../state'\n    import {onDestroy} from 'svelte'\n    import {Writable, writable} from 'svelte/store'\n\n    let dialog: HTMLDialogElement\n\n    // Control the dialog element display based on state.active\n    const unsubscribe = active.subscribe((current) => {\n        if (dialog) {\n            if (current && !dialog.open) {\n                dialog.showModal()\n            } else if (!current && dialog.open) {\n                dialog.close()\n                resetState()\n            }\n        }\n    })\n\n    onDestroy(unsubscribe)\n\n    // Perform work required to cancel request\n    function cancelRequest() {\n        // Cancel any pending promises\n        $cancelablePromises.map((f) => f('Modal closed', true))\n        // Update state to close the modal\n        active.set(false)\n    }\n\n    // When background is clicked outside of modal, close\n    function backgroundClose(event) {\n        var rect = dialog.getBoundingClientRect()\n        var isInDialog =\n            rect.top <= event.clientY &&\n            event.clientY <= rect.top + rect.height &&\n            rect.left <= event.clientX &&\n            event.clientX <= rect.left + rect.width\n        if (!isInDialog) {\n            cancelRequest()\n        }\n    }\n\n    // When escape keypress is captured, close\n    document.addEventListener('keydown', (event) => {\n        if (event.key === 'Escape' && dialog.open) {\n            cancelRequest()\n        }\n    })\n</script>\n\n<dialog\n    bind:this={dialog}\n    on:mousedown|capture|nonpassive|self|preventDefault={backgroundClose}\n    data-theme={$settings.theme}\n>\n    <Header title={$props.title} subtitle={$props.subtitle} on:cancel={cancelRequest} />\n    <div class=\"modal-content\">\n        <slot />\n    </div>\n</dialog>\n\n<style lang=\"scss\">\n    @import '../../styles/variables';\n\n    dialog {\n        --margin-top: var(--space-xl);\n        font-family:\n            system-ui,\n            -apple-system,\n            BlinkMacSystemFont,\n            'Segoe UI',\n            Roboto,\n            Oxygen,\n            Ubuntu,\n            Cantarell,\n            'Open Sans',\n            'Helvetica Neue',\n            sans-serif;\n        margin-bottom: 0;\n        margin-top: var(--margin-top);\n        margin-inline: auto;\n        border: none !important;\n        border-radius: var(--border-radius-outer);\n        padding: 0;\n        width: min(var(--space-7xl), 100vw - var(--space-m));\n        box-shadow: 0px 4px 154px rgba(0, 0, 0, 0.35);\n        background: none;\n    }\n    dialog::backdrop {\n        background: rgba(0, 0, 0, 0.75);\n    }\n    .modal-content {\n        --max-modal-content-height: calc(\n            100svh - var(--header-height) - var(--margin-top) - var(--margin-top)\n        );\n        padding: var(--space-m);\n        padding-bottom: var(--space-l);\n        background-color: var(--body-background-color);\n        overflow: hidden;\n        overflow-y: scroll;\n        max-height: var(--max-modal-content-height);\n\n        // Give Chrome some nicer scrollbars\n        scrollbar-gutter: stable both-edges;\n        scrollbar-color: var(--header-background-color);\n    }\n\n    .modal-content::-webkit-scrollbar {\n        width: 2px;\n        background-color: var(--body-background-color);\n    }\n    .modal-content::-webkit-scrollbar-thumb {\n        background: var(--header-background-color);\n    }\n</style>\n", "<script lang=\"ts\" context=\"module\">\n</script>\n\n<script lang=\"ts\">\n    import {onDestroy, setContext} from 'svelte'\n\n    import Error from './Error.svelte'\n    import Login from './Login.svelte'\n    import Prompt from './Prompt.svelte'\n    import Settings from './Settings.svelte'\n    import Transact from './Transact.svelte'\n    import CreateAccount from './CreateAccount.svelte'\n\n    import Countdown from './components/Countdown.svelte'\n    import Modal from './components/Modal.svelte'\n\n    import {active, errorDetails, prompt, router, loginPromise, accountCreationPromise, allowSettings} from './state'\n    import {i18nType} from 'src/lib/translations'\n\n    // Set the i18n context for all child components\n    export let i18n\n    setContext<i18nType>('i18n', i18n)\n\n    function cancel({detail}) {\n        // Reject any promises that are waiting for a response\n        if ($loginPromise) {\n            $loginPromise.reject(detail)\n        }\n        if ($prompt) {\n            $prompt.reject(detail)\n            prompt.reset()\n        }\n        router.back()\n    }\n\n    function complete({detail}) {\n        // Reject any promises that are waiting for a response\n        if ($loginPromise) {\n            $loginPromise.resolve(detail)\n        }\n        if ($accountCreationPromise) {\n            $accountCreationPromise.resolve(detail)\n        }\n        if ($prompt) {\n            $prompt.resolve(detail)\n            prompt.reset()\n            router.back()\n        }\n    }\n\n    const unsubscribe = router.subscribe((current) => {\n        if (current && current.path === 'login') {\n            allowSettings.set(true)\n        } else {\n            allowSettings.set(false)\n        }\n    })\n\n    onDestroy(unsubscribe)\n</script>\n\n<Modal>\n    {#if $active}\n        {#if $errorDetails}\n            <Error on:cancel={cancel} on:complete={complete} />\n        {:else if $prompt}\n            <Prompt on:cancel={cancel} on:complete={complete} />\n        {:else if $router.path === 'login'}\n            <Login on:cancel={cancel} on:complete={complete} />\n        {:else if $router.path === 'transact'}\n            <Transact on:cancel={cancel} on:complete={complete} />\n        {:else if $router.path === 'settings'}\n            <Settings on:cancel={cancel} on:complete={complete} />\n        {:else if $router.path === 'create-account'}\n            <CreateAccount on:cancel={cancel} on:complete={complete} />\n        {:else}\n            <Countdown />\n        {/if}\n    {:else}\n        <p>Modal inactive</p>\n    {/if}\n</Modal>\n", "var H=Object.defineProperty,q=Object.defineProperties;var B=Object.getOwnPropertyDescriptors;var S=Object.getOwnPropertySymbols;var A=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var j=(o,t,e)=>t in o?H(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,n=(o,t)=>{for(var e in t||(t={}))A.call(t,e)&&j(o,e,t[e]);if(S)for(var e of S(t))N.call(t,e)&&j(o,e,t[e]);return o},u=(o,t)=>q(o,B(t));var T=(o,t)=>{var e={};for(var a in o)A.call(o,a)&&t.indexOf(a)<0&&(e[a]=o[a]);if(o!=null&&S)for(var a of S(o))t.indexOf(a)<0&&N.call(o,a)&&(e[a]=o[a]);return e};import{derived as v,get as g,writable as m}from\"svelte/store\";var C=[\"error\",\"warn\",\"debug\"],z=({logger:o=console,level:t=C[1],prefix:e=\"[i18n]: \"})=>C.reduce((a,r,s)=>u(n({},a),{[r]:i=>C.indexOf(t)>=s&&o[r](`${e}${i}`)}),{}),d=z({}),V=o=>{d=o};var F=l=>{var c=l,{parser:o,key:t,params:e,translations:a,locale:r,fallbackLocale:s}=c,i=T(c,[\"parser\",\"key\",\"params\",\"translations\",\"locale\",\"fallbackLocale\"]);if(!t)return d.warn(`No translation key provided ('${r}' locale). Skipping translation...`),\"\";if(!r)return d.warn(`No locale provided for '${t}' key. Skipping translation...`),\"\";let f=(a[r]||{})[t];return s&&f===void 0&&(f=(a[s]||{})[t]),i.hasOwnProperty(\"fallbackValue\")&&f===void 0?i.fallbackValue:o.parse(f,e,r,t)},h=(...o)=>o.length?o.filter(t=>!!t).map(t=>{let e=`${t}`.toLowerCase();try{let[a]=Intl.Collator.supportedLocalesOf(t);if(!a)throw new Error;e=a}catch(a){d.warn(`'${t}' locale is non-standard.`)}return e}):[],x=(o,t,e)=>Object.keys(o||{}).reduce((a,r)=>{let s=o[r],i=e?`${e}.${r}`:`${r}`;return t&&Array.isArray(s)?u(n({},a),{[i]:s.map(l=>x(l,t))}):s&&typeof s==\"object\"?n(n({},a),x(s,t,i)):u(n({},a),{[i]:s})},{}),G=o=>o.reduce((t,{key:e,data:a,locale:r})=>{if(!a)return t;let[s]=h(r),i=u(n({},t[s]||{}),{[e]:a});return u(n({},t),{[s]:i})},{}),E=async o=>{try{let t=await Promise.all(o.map(r=>{var s=r,{loader:e}=s,a=T(s,[\"loader\"]);return new Promise(async i=>{let l;try{l=await e()}catch(c){d.error(`Failed to load translation. Verify your '${a.locale}' > '${a.key}' Loader.`),d.error(c)}i(u(n({loader:e},a),{data:l}))})}));return G(t)}catch(t){d.error(t)}return{}},W=o=>t=>{try{if(typeof t==\"string\")return t===o;if(typeof t==\"object\")return t.test(o)}catch(e){d.error(\"Invalid route config!\")}return!1},O=(o,t)=>{let e=!0;try{e=Object.keys(o).filter(a=>o[a]!==void 0).every(a=>o[a]===t[a])}catch(a){}return e};var D=1e3*60*60*24,K=class{constructor(t){this.cachedAt=0;this.loadedKeys={};this.currentRoute=m();this.config=m();this.isLoading=m(!1);this.promises=new Set;this.loading={subscribe:this.isLoading.subscribe,toPromise:(t,e)=>{let{fallbackLocale:a}=g(this.config),r=Array.from(this.promises).filter(s=>{let i=O({locale:h(t)[0],route:e},s);return a&&(i=i||O({locale:h(a)[0],route:e},s)),i}).map(({promise:s})=>s);return Promise.all(r)},get:()=>g(this.isLoading)};this.privateRawTranslations=m({});this.rawTranslations={subscribe:this.privateRawTranslations.subscribe,get:()=>g(this.rawTranslations)};this.privateTranslations=m({});this.translations={subscribe:this.privateTranslations.subscribe,get:()=>g(this.translations)};this.locales=u(n({},v([this.config,this.privateTranslations],([t,e])=>{if(!t)return[];let{loaders:a=[]}=t,r=a.map(({locale:i})=>i),s=Object.keys(e).map(i=>i);return Array.from(new Set([...h(...r),...h(...s)]))},[])),{get:()=>g(this.locales)});this.internalLocale=m();this.loaderTrigger=v([this.internalLocale,this.currentRoute],([t,e],a)=>{var r,s;t!==void 0&&e!==void 0&&!(t===((r=g(this.loaderTrigger))==null?void 0:r[0])&&e===((s=g(this.loaderTrigger))==null?void 0:s[1]))&&(d.debug(\"Triggering translation load...\"),a([t,e]))},[]);this.localeHelper=m();this.locale={subscribe:this.localeHelper.subscribe,forceSet:this.localeHelper.set,set:this.internalLocale.set,update:this.internalLocale.update,get:()=>g(this.locale)};this.initialized=v([this.locale,this.currentRoute,this.privateTranslations],([t,e,a],r)=>{g(this.initialized)||r(t!==void 0&&e!==void 0&&!!Object.keys(a).length)});this.translation=v([this.privateTranslations,this.locale,this.isLoading],([t,e,a],r)=>{let s=t[e];s&&Object.keys(s).length&&!a&&r(s)},{});this.t=u(n({},v([this.config,this.translation],r=>{var[s]=r,i=s,{parser:t,fallbackLocale:e}=i,a=T(i,[\"parser\",\"fallbackLocale\"]);return(l,...c)=>F(n({parser:t,key:l,params:c,translations:this.translations.get(),locale:this.locale.get(),fallbackLocale:e},a.hasOwnProperty(\"fallbackValue\")?{fallbackValue:a.fallbackValue}:{}))})),{get:(t,...e)=>g(this.t)(t,...e)});this.l=u(n({},v([this.config,this.translations],s=>{var[i,...l]=s,c=i,{parser:t,fallbackLocale:e}=c,a=T(c,[\"parser\",\"fallbackLocale\"]),[r]=l;return(f,b,...R)=>F(n({parser:t,key:b,params:R,translations:r,locale:f,fallbackLocale:e},a.hasOwnProperty(\"fallbackValue\")?{fallbackValue:a.fallbackValue}:{}))})),{get:(t,e,...a)=>g(this.l)(t,e,...a)});this.getLocale=t=>{let{fallbackLocale:e}=g(this.config)||{},a=t||e;if(!a)return;let r=this.locales.get();return r.find(i=>h(a).includes(i))||r.find(i=>h(e).includes(i))};this.setLocale=t=>{if(t&&t!==g(this.internalLocale))return d.debug(`Setting '${t}' locale.`),this.internalLocale.set(t),this.loading.toPromise(t,g(this.currentRoute))};this.setRoute=t=>{if(t!==g(this.currentRoute)){d.debug(`Setting '${t}' route.`),this.currentRoute.set(t);let e=g(this.internalLocale);return this.loading.toPromise(e,t)}};this.loadConfig=async t=>{await this.configLoader(t)};this.getTranslationProps=async(t=this.locale.get(),e=g(this.currentRoute))=>{let a=g(this.config);if(!a||!t)return[];let r=this.translations.get(),{loaders:s,fallbackLocale:i=\"\",cache:l=D}=a||{},c=Number.isNaN(+l)?D:+l;this.cachedAt?Date.now()>c+this.cachedAt&&(d.debug(\"Refreshing cache.\"),this.loadedKeys={},this.cachedAt=0):(d.debug(\"Setting cache timestamp.\"),this.cachedAt=Date.now());let[f,b]=h(t,i),R=r[f],I=r[b],k=(s||[]).map($=>{var L=$,{locale:p}=L,y=T(L,[\"locale\"]);return u(n({},y),{locale:h(p)[0]})}).filter(({routes:p})=>!p||(p||[]).some(W(e))).filter(({key:p,locale:y})=>y===f&&(!R||!(this.loadedKeys[f]||[]).includes(p))||i&&y===b&&(!I||!(this.loadedKeys[b]||[]).includes(p)));if(k.length){this.isLoading.set(!0),d.debug(\"Fetching translations...\");let p=await E(k);this.isLoading.set(!1);let y=Object.keys(p).reduce((L,P)=>u(n({},L),{[P]:Object.keys(p[P])}),{}),$=k.filter(({key:L,locale:P})=>(y[P]||[]).some(w=>`${w}`.startsWith(L))).reduce((L,{key:P,locale:w})=>u(n({},L),{[w]:[...L[w]||[],P]}),{});return[p,$]}return[]};this.addTranslations=(t,e)=>{if(!t)return;let a=g(this.config),{preprocess:r}=a||{};d.debug(\"Adding translations...\");let s=Object.keys(t||{});this.privateRawTranslations.update(i=>s.reduce((l,c)=>u(n({},l),{[c]:n(n({},l[c]||{}),t[c])}),i)),this.privateTranslations.update(i=>s.reduce((l,c)=>{let f=!0,b=t[c];return typeof r==\"function\"&&(b=r(b)),(typeof r==\"function\"||r===\"none\")&&(f=!1),u(n({},l),{[c]:n(n({},l[c]||{}),f?x(b,r===\"preserveArrays\"):b)})},i)),s.forEach(i=>{let l=Object.keys(t[i]).map(c=>`${c}`.split(\".\")[0]);e&&(l=e[i]),this.loadedKeys[i]=Array.from(new Set([...this.loadedKeys[i]||[],...l||[]]))})};this.loader=async([t,e])=>{let a=this.getLocale(t)||void 0;d.debug(`Adding loader promise for '${a}' locale and '${e}' route.`);let r=(async()=>{let s=await this.getTranslationProps(a,e);s.length&&this.addTranslations(...s)})();this.promises.add({locale:a,route:e,promise:r}),r.then(()=>{a&&this.locale.get()!==a&&this.locale.forceSet(a)})};this.loadTranslations=(t,e=g(this.currentRoute)||\"\")=>{let a=this.getLocale(t);if(a)return this.setRoute(e),this.setLocale(a),this.loading.toPromise(a,e)};this.loaderTrigger.subscribe(this.loader),this.isLoading.subscribe(async e=>{e&&this.promises.size&&(await this.loading.toPromise(),this.promises.clear(),d.debug(\"Loader promises have been purged.\"))}),t&&this.loadConfig(t)}async configLoader(t){if(!t)return d.error(\"No config provided!\");let l=t,{initLocale:e,fallbackLocale:a,translations:r,log:s}=l,i=T(l,[\"initLocale\",\"fallbackLocale\",\"translations\",\"log\"]);s&&V(z(s)),[e]=h(e),[a]=h(a),d.debug(\"Setting config.\"),this.config.set(n({initLocale:e,fallbackLocale:a,translations:r},i)),r&&this.addTranslations(r),e&&await this.loadTranslations(e)}};export{K as default};\n", "var R=Object.defineProperty,A=Object.defineProperties;var E=Object.getOwnPropertyDescriptors;var x=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var p=(t,e,r)=>e in t?R(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,u=(t,e)=>{for(var r in e||(e={}))C.call(e,r)&&p(t,r,e[r]);if(x)for(var r of x(e))O.call(e,r)&&p(t,r,e[r]);return t},T=(t,e)=>A(t,E(e));var c=(t,e)=>{var r={};for(var i in t)C.call(t,i)&&e.indexOf(i)<0&&(r[i]=t[i]);if(t!=null&&x)for(var i of x(t))e.indexOf(i)<0&&O.call(t,i)&&(r[i]=t[i]);return r};var j=(t,e)=>{for(var r in e)R(t,r,{get:e[r],enumerable:!0})};var h={};j(h,{ago:()=>Q,currency:()=>W,date:()=>G,eq:()=>$,gt:()=>L,gte:()=>z,lt:()=>V,lte:()=>v,ne:()=>S,number:()=>B});var g=(t,e)=>{let{modifierDefaults:r}=e||{},{[t]:i}=r||{};return i||{}};var $=({value:t,options:e=[],defaultValue:r=\"\"})=>(e.find(({key:i})=>`${i}`.toLowerCase()===`${t}`.toLowerCase())||{}).value||r,S=({value:t,options:e=[],defaultValue:r=\"\"})=>(e.find(({key:i})=>`${i}`.toLowerCase()!==`${t}`.toLowerCase())||{}).value||r,V=({value:t,options:e=[],defaultValue:r=\"\"})=>(e.sort((o,n)=>+o.key-+n.key).find(({key:o})=>+t<+o)||{}).value||r,L=({value:t,options:e=[],defaultValue:r=\"\"})=>(e.sort((o,n)=>+n.key-+o.key).find(({key:o})=>+t>+o)||{}).value||r,v=({value:t,options:e=[],defaultValue:r=\"\"})=>$({value:t,options:e,defaultValue:V({value:t,options:e,defaultValue:r})}),z=({value:t,options:e=[],defaultValue:r=\"\"})=>$({value:t,options:e,defaultValue:L({value:t,options:e,defaultValue:r})}),B=({value:t,props:e,defaultValue:r=\"\",locale:i=\"\",parserOptions:o})=>{if(!i)return\"\";let s=g(\"number\",o),{maximumFractionDigits:n}=s,m=c(s,[\"maximumFractionDigits\"]),d=(e==null?void 0:e.number)||{},{maximumFractionDigits:f=n||2}=d,a=c(d,[\"maximumFractionDigits\"]);return new Intl.NumberFormat(i,u(T(u({},m),{maximumFractionDigits:f}),a)).format(+t||+r)},G=({value:t,props:e,defaultValue:r=\"\",locale:i=\"\",parserOptions:o})=>{if(!i)return\"\";let n=c(g(\"date\",o),[]),m=c((e==null?void 0:e.date)||{},[]);return new Intl.DateTimeFormat(i,u(u({},n),m)).format(+t||+r)},D=[{key:\"second\",multiplier:1e3},{key:\"minute\",multiplier:60},{key:\"hour\",multiplier:60},{key:\"day\",multiplier:24},{key:\"week\",multiplier:7},{key:\"month\",multiplier:13/3},{key:\"year\",multiplier:12}],N=(t=\"\",e=\"\")=>new RegExp(`^${t}s?$`).test(e),H=t=>D.indexOf(D.find(({key:e})=>N(e,t))),J=(t,e)=>D.reduce(([r,i],{key:o,multiplier:n},m)=>{if(N(i,e))return[r,i];if(!i||m===H(i)+1){let f=Math.round(r/n);if(!i||Math.abs(f)>=1||e!==\"auto\")return[f,o]}return[r,i]},[t,\"\"]),Q=({value:t,defaultValue:e=\"\",locale:r=\"\",props:i,parserOptions:o})=>{if(!r)return\"\";let k=g(\"ago\",o),{format:n,numeric:m}=k,f=c(k,[\"format\",\"numeric\"]),y=(i==null?void 0:i.ago)||{},{format:a=n||\"auto\",numeric:s=m||\"auto\"}=y,d=c(y,[\"format\",\"numeric\"]),M=+t||+e,l=J(M,a);return new Intl.RelativeTimeFormat(r,u(T(u({},f),{numeric:s}),d)).format(...l)},W=({value:t,defaultValue:e=\"\",locale:r=\"\",props:i,parserOptions:o})=>{if(!r)return\"\";let M=g(\"currency\",o),{ratio:n,currency:m}=M,f=c(M,[\"ratio\",\"currency\"]),l=(i==null?void 0:i.currency)||{},{ratio:a=n||1,currency:s=m}=l,d=c(l,[\"ratio\",\"currency\"]);return new Intl.NumberFormat(r,u(T(u({},f),{style:\"currency\",currency:s}),d)).format(a*(t||e))};var X=t=>typeof t==\"string\"&&/{{(?:(?!{{|}}).)+}}/.test(t),F=t=>typeof t==\"string\"?t.replace(/\\\\(?=:|;|{|})/g,\"\"):t,Y=({value:t,props:e,payload:r,parserOptions:i,locale:o})=>`${t}`.replace(/{{\\s*(?:(?!{{|}}).)+\\s*}}/g,n=>{let m=F(`${n.match(/(?!{|\\s).+?(?!\\\\[:;]).(?=\\s*(?:[:;]|}}$))/)}`),f=r==null?void 0:r[m],[,a=\"\"]=n.match(/.+?(?!\\\\;).;\\s*default\\s*:\\s*([^\\s:;].+?(?:\\\\[:;]|[^;}])*)(?=\\s*(?:;|}}$))/i)||[];a=a||(r==null?void 0:r.default)||\"\";let[,s=\"\"]=n.match(/{{\\s*(?:[^;]|(?:\\\\;))+\\s*(?:(?!\\\\:).[:])\\s*(?!\\s)((?:\\\\;|[^;])+?)(?=\\s*(?:[;]|}}$))/i)||[];if(f===void 0&&s!==\"ne\")return a;let d=!!s,{customModifiers:M}=i||{},l=u(u({},h),M||{});s=Object.keys(l).includes(s)?s:\"eq\";let k=l[s],y=(n.match(/[^\\s:;{](?:[^;]|\\\\[;])+[^:;}]/gi)||[]).reduce((b,I,q)=>{if(q>0){let P=F(`${I.match(/(?:(?:\\\\:)|[^:])+/)}`.trim()),w=`${I.match(/(?:(?:\\\\:)|[^:])+$/)}`.trimStart();if(P&&P!==\"default\"&&w)return[...b,{key:P,value:w}]}return b},[]);return!d&&!y.length?f:k({value:f,options:y,props:e,defaultValue:a,locale:o,parserOptions:i})}),U=({value:t,props:e,payload:r,parserOptions:i,locale:o})=>{if(X(t)){let n=Y({value:t,payload:r,props:e,parserOptions:i,locale:o});return U({value:n,payload:r,props:e,parserOptions:i,locale:o})}else return F(t)},Z=t=>({parse:(e,[r,i],o,n)=>(r!=null&&r.default&&e===void 0&&(e=r.default),e===void 0&&(e=n),U({value:e,payload:r,props:i,parserOptions:t,locale:o}))}),rt=Z;export{rt as default};\n", "var n=Object.defineProperty,M=Object.defineProperties;var u=Object.getOwnPropertyDescriptors;var s=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable;var i=(r,o,e)=>o in r?n(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,p=(r,o)=>{for(var e in o||(o={}))f.call(o,e)&&i(r,e,o[e]);if(s)for(var e of s(o))P.call(o,e)&&i(r,e,o[e]);return r},d=(r,o)=>M(r,u(o));var l=(r,o)=>{var e={};for(var a in r)f.call(r,a)&&o.indexOf(a)<0&&(e[a]=r[a]);if(r!=null&&s)for(var a of s(r))o.indexOf(a)<0&&P.call(r,a)&&(e[a]=r[a]);return e};import g from\"@sveltekit-i18n/base\";import C from\"@sveltekit-i18n/parser-default\";var m=e=>{var a=e,{parserOptions:r={}}=a,o=l(a,[\"parserOptions\"]);return d(p({},o),{parser:C(r)})},t=class extends g{constructor(e){super(e&&m(e));this.loadConfig=e=>super.configLoader(m(e))}},D=t;export{D as default};\n", "import {NameType, PublicKeyType} from '@wharfkit/antelope'\nimport i18n, {Config, Parser} from 'sveltekit-i18n'\n\nimport lang from './translations/lang.json'\n\nimport en from './translations/en/common.json'\nimport ko from './translations/ko/common.json'\nimport zh_hans from './translations/zh-hans/common.json'\nimport zh_hant from './translations/zh-hant/common.json'\nimport tr from './translations/tr/common.json'\n\nconst translations = {\n    en: {...lang, ...en},\n    ko: {...lang, ...ko},\n    zh: {...lang, ...zh_hans},\n    'zh-Hans': {...lang, ...zh_hans},\n    'zh-Hant': {...lang, ...zh_hant},\n    tr: {...lang, ...tr},\n}\n\ninterface Params {\n    appName?: NameType\n    name?: NameType\n    publicKey?: PublicKeyType\n}\n\nconst config: Config<Params> = {\n    initLocale: 'en',\n    translations,\n}\n\ninterface UserInterfaceLocalizationOptions {\n    translations?: Record<string, Record<string, string>>\n}\n\nexport type i18nType = i18n<Parser.Params<Params, object>, Params, object>\n\nexport const makeLocalization = (options: UserInterfaceLocalizationOptions = {}): i18nType => {\n    const params: Config<Params> = {\n        ...config,\n        ...options,\n    }\n    return new i18n(params)\n}\n\nexport const {t, l, locales, locale, loadTranslations, setLocale} = new i18n(config)\n", "import {\n    AbstractUserInterface,\n    Cancelable,\n    cancelable,\n    Canceled,\n    CreateAccountContext,\n    LoginContext,\n    PromptArgs,\n    PromptResponse,\n    UserInterface,\n    UserInterfaceAccountCreationResponse,\n    UserInterfaceLoginResponse,\n    UserInterfaceTranslateOptions,\n} from '@wharfkit/session'\n\nimport App from './ui/App.svelte'\nimport {makeLocalization} from './lib/translations'\n\nimport {\n    accountCreationContext,\n    accountCreationPromise,\n    active,\n    cancelablePromises,\n    errorDetails,\n    loginContext,\n    loginPromise,\n    prompt,\n    props,\n    resetState,\n    router,\n    settings,\n} from './ui/state'\nimport {get} from 'svelte/store'\n\nexport interface WebRendererOptions {\n    id?: string\n    logging?: boolean\n    minimal?: boolean\n    translations?: Record<string, Record<string, string>>\n}\n\nexport const defaultWebRendererOptions = {\n    id: 'wharfkit-web-ui',\n    minimal: false,\n}\n\nconst getNavigatorLanguage = () =>\n    (navigator.languages && navigator.languages.length\n        ? navigator.languages[0]\n        : navigator.language || 'en'\n    ).split('-')[0]\n\nexport class WebRenderer extends AbstractUserInterface implements UserInterface {\n    static version = '__ver' // replaced by build script\n\n    public elementId = 'wharfkit-web-ui'\n    public element: Element | undefined\n    public shadow: ShadowRoot | undefined\n    public options: WebRendererOptions\n\n    public i18n\n    public initialized = false\n    public logging = false\n    public minimal = false\n\n    constructor(options: WebRendererOptions = defaultWebRendererOptions) {\n        super()\n        this.options = options\n        if (typeof document !== 'undefined') {\n            this.initialize()\n        }\n    }\n\n    initialize() {\n        // Prevent multiple initializations\n        if (this.initialized) {\n            return\n        }\n        const {options} = this\n        // Create the dialog element and its shadow root\n        this.element = document.createElement('div')\n        this.elementId = options.id || defaultWebRendererOptions.id\n        this.element.id = this.elementId\n        this.shadow = this.element.attachShadow({mode: 'closed'})\n        // Load translations for the current locale\n        this.i18n = makeLocalization()\n        let lang = getNavigatorLanguage()\n        this.minimal = options.minimal || false\n        const settingsLanguage = get(settings).language\n        if (settingsLanguage) {\n            lang = settingsLanguage\n        }\n        if (options.logging !== undefined) {\n            this.logging = options.logging\n        }\n        this.log(`Setting language to ${lang}`)\n        settings.update((current) => ({...current, language: lang}))\n        this.i18n.loadTranslations(lang)\n        if (document.readyState === 'complete' || document.readyState === 'interactive') {\n            // Document is ready, append element\n            this.appendDialogElement()\n        } else {\n            // Add listener to append to body\n            document.addEventListener('DOMContentLoaded', () => this.appendDialogElement())\n        }\n        this.initialized = true\n    }\n\n    appendDialogElement() {\n        const existing = document.getElementById(this.elementId)\n        if (!this.element || !this.shadow) {\n            throw new Error('The WebRenderer is not initialized. Call the initialize method first.')\n        }\n        if (!existing) {\n            document.body.append(this.element)\n            document.removeEventListener('DOMContentLoaded', () => this.appendDialogElement())\n            new App({\n                target: this.shadow,\n                props: {\n                    i18n: this.i18n,\n                },\n            })\n        }\n    }\n\n    // Add every cancelable promise to the list of cancelable promises\n    addCancelablePromise = (promise) =>\n        cancelablePromises.update((current) => [...current, promise])\n\n    log(...args: any[]) {\n        if (this.logging) {\n            // eslint-disable-next-line no-console\n            console.log('WebRenderer, LOG:', ...args)\n        }\n    }\n\n    async login(context: LoginContext): Promise<UserInterfaceLoginResponse> {\n        this.log('login', context)\n        prompt.set(undefined)\n        router.push('login')\n        const promise = cancelable(\n            new Promise<UserInterfaceLoginResponse>((resolve, reject) =>\n                loginPromise.set({\n                    reject,\n                    resolve,\n                })\n            )\n        )\n        this.addCancelablePromise(promise.cancel)\n        loginContext.set(context)\n        await promise\n        if (this.minimal) {\n            active.set(false)\n        }\n        return promise\n    }\n\n    async onError(error: Error) {\n        // Determine if this was a silent/cancelable error\n        const isCancelable = error instanceof Canceled\n        const isSilent = isCancelable && error.silent === true\n        this.log('onError', {\n            isCancelable,\n            isSilent,\n            error,\n        })\n        // If it was, don't display the error\n        if (isSilent) {\n            return\n        }\n        if (this.minimal) {\n            active.set(false)\n        } else {\n            // Make sure the dialog is active\n            active.set(true)\n            // Set the error state\n            errorDetails.set(String(error))\n            // Push the new path to the router\n            router.push('error')\n        }\n    }\n\n    async onAccountCreate(\n        context: CreateAccountContext\n    ): Promise<UserInterfaceAccountCreationResponse> {\n        this.log('onAccountCreate', context)\n\n        // Make sure the dialog is active\n        active.set(true)\n\n        // Push the new path to the router\n        router.push('create-account')\n\n        const promise = cancelable(\n            new Promise<UserInterfaceAccountCreationResponse>((resolve, reject) =>\n                accountCreationPromise.set({\n                    reject,\n                    resolve,\n                })\n            )\n        )\n        this.addCancelablePromise(promise.cancel)\n        accountCreationContext.set(context)\n\n        return promise\n    }\n\n    async onAccountCreateComplete(): Promise<void> {\n        this.log('onAccountCreateComplete')\n\n        // Close the dialog once the login completes\n        active.set(false)\n        // Reset all data in the state\n        resetState()\n    }\n\n    async onLogin() {\n        this.log('onLogin')\n        // Make sure the dialog is active\n        active.set(true)\n        // Set the title/subtitle to match the login state\n        props.update((current) => ({\n            ...current,\n            title: this.i18n.t.get('login.title', {default: 'Login'}),\n            subtitle: '',\n        }))\n        // Push the new path to the router\n        router.push('login')\n    }\n\n    async onLoginComplete() {\n        this.log('onLoginResult')\n        // Close the dialog once the login completes\n        active.set(false)\n        // Reset all data in the state\n        resetState()\n    }\n\n    async onTransact() {\n        this.log('onTransact')\n        // Make sure the dialog is active\n        if (!this.minimal) {\n            active.set(true)\n        }\n        // Set the title/subtitle to match the transact state\n        props.update((c) => ({\n            ...c,\n            title: this.i18n.t.get('transact.title', {default: 'Transact'}),\n            subtitle: '',\n        }))\n        // Push the new path to the router\n        router.push('transact')\n    }\n\n    async onTransactComplete() {\n        this.log('onTransactResult')\n        // Reset all data in the state\n        resetState()\n        // Close the dialog once the transact completes\n        active.set(false)\n    }\n\n    async onSign(): Promise<void> {\n        this.log('onSign')\n    }\n\n    async onSignComplete(): Promise<void> {\n        this.log('onSignComplete')\n    }\n\n    async onBroadcast(): Promise<void> {\n        this.log('onBroadcast')\n    }\n\n    async onBroadcastComplete(): Promise<void> {\n        this.log('onBroadcastComplete')\n    }\n\n    prompt(args: PromptArgs): Cancelable<PromptResponse> {\n        this.log('prompt', args)\n        // Make sure the dialog is active\n        if (!this.minimal || (this.minimal && !args.optional)) {\n            active.set(true)\n            // Push the new path to the router\n            router.push('prompt')\n        }\n        // Setup the promise to return to the session kit\n        const promise = cancelable(\n            new Promise<UserInterfaceLoginResponse>((resolve, reject) => {\n                prompt.set({\n                    args,\n                    resolve,\n                    reject,\n                })\n            }),\n            (canceled) => {\n                throw canceled\n            }\n        )\n        // Save a copy of the promise to reference if canceled\n        this.addCancelablePromise(promise.cancel)\n        // Return the promise to the session kit\n        return promise\n    }\n\n    status(message: string) {\n        // Make sure the dialog is active\n        if (!this.minimal) {\n            active.set(true)\n        }\n        // Update the subtitle to match the message\n        props.update((current) => ({\n            ...current,\n            subtitle: message,\n        }))\n    }\n\n    // Map the UserInterface translate call to our i18n instance\n    translate(key: string, options?: UserInterfaceTranslateOptions, namespace?: string) {\n        if (namespace) {\n            return this.i18n.t.get(`${namespace}.${key}`, options)\n        }\n        return this.i18n.t.get(key, options)\n    }\n\n    addTranslations(translations) {\n        this.i18n.addTranslations(translations)\n    }\n}\n\nexport default WebRenderer\n"], "mappings": ";;;;;;;;;;AACO,SAAS,OAAO;AAAA;AAEhB,IAAM,WAAW,CAACA,OAAMA;AASxB,SAAS,OAAO,KAAK,KAAK;AAEhC,aAAW,KAAK;AAAK,QAAI,CAAC,IAAI,IAAI,CAAC;AACnC;;IAA6B;;AAC9B;AAuBO,SAAS,IAAI,IAAI;AACvB,SAAO,GAAE;AACV;AAEO,SAAS,eAAe;AAC9B,SAAO,uBAAO,OAAO,IAAI;AAC1B;AAMO,SAAS,QAAQ,KAAK;AAC5B,MAAI,QAAQ,GAAG;AAChB;AAMO,SAAS,YAAY,OAAO;AAClC,SAAO,OAAO,UAAU;AACzB;AAGO,SAAS,eAAe,GAAG,GAAG;AACpC,SAAO,KAAK,IAAI,KAAK,IAAI,MAAM,KAAM,KAAK,OAAO,MAAM,YAAa,OAAO,MAAM;AAClF;AAEA,IAAI;AAOG,SAAS,cAAc,aAAa,KAAK;AAC/C,MAAI,gBAAgB;AAAK,WAAO;AAChC,MAAI,CAAC,sBAAsB;AAC1B,2BAAuB,SAAS,cAAc,GAAG;EACnD;AAEC,uBAAqB,OAAO;AAC5B,SAAO,gBAAgB,qBAAqB;AAC7C;AAqCO,SAAS,SAAS,KAAK;AAC7B,SAAO,OAAO,KAAK,GAAG,EAAE,WAAW;AACpC;AASO,SAAS,UAAU,UAAU,WAAW;AAC9C,MAAI,SAAS,MAAM;AAClB,eAAW,YAAY,WAAW;AACjC,eAAS,MAAS;IACrB;AACE,WAAO;EACT;AACC,QAAM,QAAQ,MAAM,UAAU,GAAG,SAAS;AAC1C,SAAO,MAAM,cAAc,MAAM,MAAM,YAAW,IAAK;AACxD;AAUO,SAAS,gBAAgB,OAAO;AACtC,MAAI;AACJ,YAAU,OAAO,CAAC,MAAO,QAAQ,CAAE,EAAC;AACpC,SAAO;AACR;AAGO,SAAS,oBAAoB,WAAW,OAAO,UAAU;AAC/D,YAAU,GAAG,WAAW,KAAK,UAAU,OAAO,QAAQ,CAAC;AACxD;AAEO,SAAS,YAAY,YAAY,KAAK,SAAS,IAAI;AACzD,MAAI,YAAY;AACf,UAAM,WAAW,iBAAiB,YAAY,KAAK,SAAS,EAAE;AAC9D,WAAO,WAAW,CAAC,EAAE,QAAQ;EAC/B;AACA;AAEA,SAAS,iBAAiB,YAAY,KAAK,SAAS,IAAI;AACvD,SAAO,WAAW,CAAC,KAAK,KAAK,OAAO,QAAQ,IAAI,MAAK,GAAI,WAAW,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,IAAI,QAAQ;AAC5F;AAEO,SAAS,iBAAiB,YAAY,SAAS,OAAO,IAAI;AAChE,MAAI,WAAW,CAAC,KAAK,IAAI;AACxB,UAAM,OAAO,WAAW,CAAC,EAAE,GAAG,KAAK,CAAC;AACpC,QAAI,QAAQ,UAAU,QAAW;AAChC,aAAO;IACV;AACE,QAAI,OAAO,SAAS,UAAU;AAC7B,YAAM,SAAS,CAAA;AACf,YAAM,MAAM,KAAK,IAAI,QAAQ,MAAM,QAAQ,KAAK,MAAM;AACtD,eAASC,KAAI,GAAGA,KAAI,KAAKA,MAAK,GAAG;AAChC,eAAOA,EAAC,IAAI,QAAQ,MAAMA,EAAC,IAAI,KAAKA,EAAC;MACzC;AACG,aAAO;IACV;AACE,WAAO,QAAQ,QAAQ;EACzB;AACC,SAAO,QAAQ;AAChB;AAGO,SAAS,iBACf,MACA,iBACA,KACA,SACA,cACA,qBACC;AACD,MAAI,cAAc;AACjB,UAAM,eAAe,iBAAiB,iBAAiB,KAAK,SAAS,mBAAmB;AACxF,SAAK,EAAE,cAAc,YAAY;EACnC;AACA;AAiBO,SAAS,yBAAyB,SAAS;AACjD,MAAI,QAAQ,IAAI,SAAS,IAAI;AAC5B,UAAM,QAAQ,CAAA;AACd,UAAM,SAAS,QAAQ,IAAI,SAAS;AACpC,aAASA,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAChC,YAAMA,EAAC,IAAI;IACd;AACE,WAAO;EACT;AACC,SAAO;AACR;AAoCO,SAAS,cAAc,OAAO;AACpC,SAAO,SAAS,OAAO,KAAK;AAC7B;AAEO,SAAS,gBAAgB,OAAO,KAAK,OAAO;AAClD,QAAM,IAAI,KAAK;AACf,SAAO;AACR;AAWO,SAAS,eAAe,OAAO;AACrC,QAAM,QAAQ,OAAO,UAAU,YAAY,MAAM,MAAM,4BAA4B;AACnF,SAAO,QAAQ,CAAC,WAAW,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,IAAI,IAAI;;IAAwB;IAAQ;EAAI;AAC/F;AC9RO,IAAM,YAAY,OAAO,WAAW;AAGpC,IAAI,MAAM,YAAY,MAAM,OAAO,YAAY,IAAG,IAAK,MAAM,KAAK,IAAG;AAErE,IAAI,MAAM,YAAY,CAAC,OAAO,sBAAsB,EAAE,IAAI;ACLjE,IAAM,QAAQ,oBAAI,IAAG;AAMrB,SAAS,UAAUC,MAAK;AACvB,QAAM,QAAQ,CAAC,SAAS;AACvB,QAAI,CAAC,KAAK,EAAEA,IAAG,GAAG;AACjB,YAAM,OAAO,IAAI;AACjB,WAAK,EAAC;IACT;EACA,CAAE;AACD,MAAI,MAAM,SAAS;AAAG,QAAI,SAAS;AACpC;AAgBO,SAAS,KAAK,UAAU;AAE9B,MAAI;AACJ,MAAI,MAAM,SAAS;AAAG,QAAI,SAAS;AACnC,SAAO;IACN,SAAS,IAAI,QAAQ,CAAC,YAAY;AACjC,YAAM,IAAK,OAAO,EAAE,GAAG,UAAU,GAAG,QAAO,CAAE;IAChD,CAAG;IACD,QAAQ;AACP,YAAM,OAAO,IAAI;IACpB;EACA;AACA;ACgGO,SAAS,OAAO,QAAQ,MAAM;AACpC,SAAO,YAAY,IAAI;AACxB;AAQO,SAAS,cAAc,QAAQ,gBAAgB,QAAQ;AAC7D,QAAM,mBAAmB,mBAAmB,MAAM;AAClD,MAAI,CAAC,iBAAiB,eAAe,cAAc,GAAG;AACrD,UAAM,QAAQ,QAAQ,OAAO;AAC7B,UAAM,KAAK;AACX,UAAM,cAAc;AACpB,sBAAkB,kBAAkB,KAAK;EAC3C;AACA;AAMO,SAAS,mBAAmB,MAAM;AACxC,MAAI,CAAC;AAAM,WAAO;AAClB,QAAM,OAAO,KAAK,cAAc,KAAK,YAAW,IAAK,KAAK;AAC1D,MAAI;EAAmC,KAAM,MAAM;AAClD;;MAAkC;;EACpC;AACC,SAAO,KAAK;AACb;AAMO,SAAS,wBAAwB,MAAM;AAC7C,QAAM,gBAAgB,QAAQ,OAAO;AAMrC,gBAAc,cAAc;AAC5B,oBAAkB,mBAAmB,IAAI,GAAG,aAAa;AACzD,SAAO,cAAc;AACtB;AAOA,SAAS,kBAAkB,MAAM,OAAO;AACvC;;IAAgC,KAAM,QAAQ;IAAM;EAAK;AACzD,SAAO,MAAM;AACd;AAuCO,SAAS,OAAO,QAAQ,MAAM,QAAQ;AAC5C,SAAO,aAAa,MAAM,UAAU,IAAI;AACzC;AAoBO,SAAS,OAAO,MAAM;AAC5B,MAAI,KAAK,YAAY;AACpB,SAAK,WAAW,YAAY,IAAI;EAClC;AACA;AAIO,SAAS,aAAa,YAAY,WAAW;AACnD,WAASD,KAAI,GAAGA,KAAI,WAAW,QAAQA,MAAK,GAAG;AAC9C,QAAI,WAAWA,EAAC;AAAG,iBAAWA,EAAC,EAAE,EAAE,SAAS;EAC9C;AACA;AAOO,SAAS,QAAQ,MAAM;AAC7B,SAAO,SAAS,cAAc,IAAI;AACnC;AAuCO,SAAS,YAAY,MAAM;AACjC,SAAO,SAAS,gBAAgB,8BAA8B,IAAI;AACnE;AAMO,SAAS,KAAK,MAAM;AAC1B,SAAO,SAAS,eAAe,IAAI;AACpC;AAIO,SAAS,QAAQ;AACvB,SAAO,KAAK,GAAG;AAChB;AAIO,SAAS,QAAQ;AACvB,SAAO,KAAK,EAAE;AACf;AAiBO,SAAS,OAAO,MAAM,OAAO,SAAS,SAAS;AACrD,OAAK,iBAAiB,OAAO,SAAS,OAAO;AAC7C,SAAO,MAAM,KAAK,oBAAoB,OAAO,SAAS,OAAO;AAC9D;AAIO,SAAS,gBAAgB,IAAI;AACnC,SAAO,SAAU,OAAO;AACvB,UAAM,eAAc;AAEpB,WAAO,GAAG,KAAK,MAAM,KAAK;EAC5B;AACA;AAIO,SAAS,iBAAiB,IAAI;AACpC,SAAO,SAAU,OAAO;AACvB,UAAM,gBAAe;AAErB,WAAO,GAAG,KAAK,MAAM,KAAK;EAC5B;AACA;AAcO,SAAS,KAAK,IAAI;AACxB,SAAO,SAAU,OAAO;AAEvB,QAAI,MAAM,WAAW;AAAM,SAAG,KAAK,MAAM,KAAK;EAChD;AACA;AAiBO,SAAS,KAAK,MAAM,WAAW,OAAO;AAC5C,MAAI,SAAS;AAAM,SAAK,gBAAgB,SAAS;WACxC,KAAK,aAAa,SAAS,MAAM;AAAO,SAAK,aAAa,WAAW,KAAK;AACpF;AA6LO,SAAS,SAASE,UAAS;AACjC,SAAO,MAAM,KAAKA,SAAQ,UAAU;AACrC;AA8MO,SAAS,SAASC,OAAM,MAAM;AACpC,SAAO,KAAK;AACZ,MAAIA,MAAK,SAAS;AAAM;AACxB,EAAAA,MAAK;EAA8B;AACpC;AA6BO,SAAS,gBAAgB,OAAO,OAAO;AAC7C,QAAM,QAAQ,SAAS,OAAO,KAAK;AACpC;AAcO,SAAS,UAAU,MAAM,KAAK,OAAO,WAAW;AACtD,MAAI,SAAS,MAAM;AAClB,SAAK,MAAM,eAAe,GAAG;EAC/B,OAAQ;AACN,SAAK,MAAM,YAAY,KAAK,OAAO,YAAY,cAAc,EAAE;EACjE;AACA;AA0HO,SAAS,aAAaD,UAAS,MAAM,QAAQ;AAEnD,EAAAA,SAAQ,UAAU,OAAO,MAAM,CAAC,CAAC,MAAM;AACxC;AASO,SAAS,aAAa,MAAM,QAAQ,EAAE,UAAU,OAAO,YAAAE,cAAa,MAAK,IAAK,CAAA,GAAI;AACxF,SAAO,IAAI,YAAY,MAAM,EAAE,QAAQ,SAAS,YAAAA,YAAU,CAAE;AAC7D;AAqLO,SAAS,2BAA2B,WAAWC,QAAO;AAC5D,SAAO,IAAI,UAAUA,MAAK;AAC3B;AChqCA,IAAM,iBAAiB,oBAAI,IAAG;AAE9B,IAAIC,WAAS;AAOb,SAAS,KAAK,KAAK;AAClB,MAAIC,QAAO;AACX,MAAIP,KAAI,IAAI;AACZ,SAAOA;AAAK,IAAAO,SAASA,SAAQ,KAAKA,QAAQ,IAAI,WAAWP,EAAC;AAC1D,SAAOO,UAAS;AACjB;AAOA,SAAS,yBAAyB,KAAK,MAAM;AAC5C,QAAM,OAAO,EAAE,YAAY,wBAAwB,IAAI,GAAG,OAAO,CAAA,EAAE;AACnE,iBAAe,IAAI,KAAK,IAAI;AAC5B,SAAO;AACR;AAaO,SAAS,YAAY,MAAM,GAAG,GAAG,UAAU,OAAO,MAAM,IAAI,MAAM,GAAG;AAC3E,QAAM,OAAO,SAAS;AACtB,MAAI,YAAY;AAChB,WAASC,KAAI,GAAGA,MAAK,GAAGA,MAAK,MAAM;AAClC,UAAMC,KAAI,KAAK,IAAI,KAAK,KAAKD,EAAC;AAC9B,iBAAaA,KAAI,MAAM,KAAK,GAAGC,IAAG,IAAIA,EAAC,CAAC;;EAC1C;AACC,QAAM,OAAO,YAAY,SAAS,GAAG,GAAG,IAAI,CAAC,CAAC;;AAC9C,QAAM,OAAO,YAAY,KAAK,IAAI,CAAC,IAAI,GAAG;AAC1C,QAAM,MAAM,mBAAmB,IAAI;AACnC,QAAM,EAAE,YAAY,MAAK,IAAK,eAAe,IAAI,GAAG,KAAK,yBAAyB,KAAK,IAAI;AAC3F,MAAI,CAAC,MAAM,IAAI,GAAG;AACjB,UAAM,IAAI,IAAI;AACd,eAAW,WAAW,cAAc,IAAI,IAAI,IAAI,IAAI,WAAW,SAAS,MAAM;EAChF;AACC,QAAM,YAAY,KAAK,MAAM,aAAa;AAC1C,OAAK,MAAM,YAAY,GACtB,YAAY,GAAG,SAAS,OAAO,EACjC,GAAI,IAAI,IAAI,QAAQ,aAAa,KAAK;AACrCH,cAAU;AACV,SAAO;AACR;AAOO,SAAS,YAAY,MAAM,MAAM;AACvC,QAAM,YAAY,KAAK,MAAM,aAAa,IAAI,MAAM,IAAI;AACxD,QAAM,OAAO,SAAS;IACrB,OACG,CAAC,SAAS,KAAK,QAAQ,IAAI,IAAI,IAC/B,CAAC,SAAS,KAAK,QAAQ,UAAU,MAAM;;EAC5C;AACC,QAAM,UAAU,SAAS,SAAS,KAAK;AACvC,MAAI,SAAS;AACZ,SAAK,MAAM,YAAY,KAAK,KAAK,IAAI;AACrCA,gBAAU;AACV,QAAI,CAACA;AAAQ,kBAAW;EAC1B;AACA;AAGO,SAAS,cAAc;AAC7B,MAAI,MAAM;AACT,QAAIA;AAAQ;AACZ,mBAAe,QAAQ,CAAC,SAAS;AAChC,YAAM,EAAE,UAAS,IAAK,KAAK;AAE3B,UAAI;AAAW,eAAO,SAAS;IAClC,CAAG;AACD,mBAAe,MAAK;EACtB,CAAE;AACF;AChGO,IAAI;AAGJ,SAAS,sBAAsB,WAAW;AAChD,sBAAoB;AACrB;AAEO,SAAS,wBAAwB;AACvC,MAAI,CAAC;AAAmB,UAAM,IAAI,MAAM,kDAAkD;AAC1F,SAAO;AACR;AA6BO,SAAS,QAAQ,IAAI;AAC3B,wBAAqB,EAAG,GAAG,SAAS,KAAK,EAAE;AAC5C;AAyBO,SAAS,UAAU,IAAI;AAC7B,wBAAqB,EAAG,GAAG,WAAW,KAAK,EAAE;AAC9C;AAyBO,SAAS,wBAAwB;AACvC,QAAM,YAAY,sBAAqB;AACvC,SAAO,CAAC,MAAM,QAAQ,EAAE,YAAAF,cAAa,MAAK,IAAK,CAAA,MAAO;AACrD,UAAM,YAAY,UAAU,GAAG,UAAU,IAAI;AAC7C,QAAI,WAAW;AAGd,YAAM,QAAQ;;QAAoC;QAAO;QAAQ,EAAE,YAAAA,YAAU;MAAE;AAC/E,gBAAU,MAAK,EAAG,QAAQ,CAAC,OAAO;AACjC,WAAG,KAAK,WAAW,KAAK;MAC5B,CAAI;AACD,aAAO,CAAC,MAAM;IACjB;AACE,WAAO;EACT;AACA;AAeO,SAAS,WAAW,KAAK,SAAS;AACxC,wBAAqB,EAAG,GAAG,QAAQ,IAAI,KAAK,OAAO;AACnD,SAAO;AACR;AAWO,SAAS,WAAW,KAAK;AAC/B,SAAO,sBAAqB,EAAG,GAAG,QAAQ,IAAI,GAAG;AAClD;AC1IO,IAAM,mBAAmB,CAAA;AAEzB,IAAM,oBAAoB,CAAA;AAEjC,IAAI,mBAAmB,CAAA;AAEvB,IAAM,kBAAkB,CAAA;AAExB,IAAM,mBAAmC,QAAQ,QAAO;AAExD,IAAI,mBAAmB;AAGhB,SAAS,kBAAkB;AACjC,MAAI,CAAC,kBAAkB;AACtB,uBAAmB;AACnB,qBAAiB,KAAK,KAAK;EAC7B;AACA;AASO,SAAS,oBAAoB,IAAI;AACvC,mBAAiB,KAAK,EAAE;AACzB;AAGO,SAAS,mBAAmB,IAAI;AACtC,kBAAgB,KAAK,EAAE;AACxB;AAoBA,IAAM,iBAAiB,oBAAI,IAAG;AAE9B,IAAI,WAAW;AAGR,SAAS,QAAQ;AAIvB,MAAI,aAAa,GAAG;AACnB;EACF;AACC,QAAM,kBAAkB;AACxB,KAAG;AAGF,QAAI;AACH,aAAO,WAAW,iBAAiB,QAAQ;AAC1C,cAAM,YAAY,iBAAiB,QAAQ;AAC3C;AACA,8BAAsB,SAAS;AAC/B,eAAO,UAAU,EAAE;MACvB;IACA,SAAW,GAAG;AAEX,uBAAiB,SAAS;AAC1B,iBAAW;AACX,YAAM;IACT;AACE,0BAAsB,IAAI;AAC1B,qBAAiB,SAAS;AAC1B,eAAW;AACX,WAAO,kBAAkB;AAAQ,wBAAkB,IAAG,EAAE;AAIxD,aAASJ,KAAI,GAAGA,KAAI,iBAAiB,QAAQA,MAAK,GAAG;AACpD,YAAM,WAAW,iBAAiBA,EAAC;AACnC,UAAI,CAAC,eAAe,IAAI,QAAQ,GAAG;AAElC,uBAAe,IAAI,QAAQ;AAC3B,iBAAQ;MACZ;IACA;AACE,qBAAiB,SAAS;EAC5B,SAAU,iBAAiB;AAC1B,SAAO,gBAAgB,QAAQ;AAC9B,oBAAgB,IAAG,EAAE;EACvB;AACC,qBAAmB;AACnB,iBAAe,MAAK;AACpB,wBAAsB,eAAe;AACtC;AAGA,SAAS,OAAO,IAAI;AACnB,MAAI,GAAG,aAAa,MAAM;AACzB,OAAG,OAAM;AACT,YAAQ,GAAG,aAAa;AACxB,UAAM,QAAQ,GAAG;AACjB,OAAG,QAAQ,CAAC,EAAE;AACd,OAAG,YAAY,GAAG,SAAS,EAAE,GAAG,KAAK,KAAK;AAC1C,OAAG,aAAa,QAAQ,mBAAmB;EAC7C;AACA;AAOO,SAAS,uBAAuB,KAAK;AAC3C,QAAM,WAAW,CAAA;AACjB,QAAM,UAAU,CAAA;AAChB,mBAAiB,QAAQ,CAACU,OAAO,IAAI,QAAQA,EAAC,MAAM,KAAK,SAAS,KAAKA,EAAC,IAAI,QAAQ,KAAKA,EAAC,CAAE;AAC5F,UAAQ,QAAQ,CAACA,OAAMA,GAAC,CAAE;AAC1B,qBAAmB;AACpB;AC5HA,IAAI;AAKJ,SAAS,OAAO;AACf,MAAI,CAAC,SAAS;AACb,cAAU,QAAQ,QAAO;AACzB,YAAQ,KAAK,MAAM;AAClB,gBAAU;IACb,CAAG;EACH;AACC,SAAO;AACR;AAQA,SAAS,SAAS,MAAM,WAAW,MAAM;AACxC,OAAK,cAAc,aAAa,GAAG,YAAY,UAAU,OAAO,GAAG,IAAI,EAAE,CAAC;AAC3E;AAEA,IAAM,WAAW,oBAAI,IAAG;AAKxB,IAAI;AAIG,SAAS,eAAe;AAC9B,WAAS;IACR,GAAG;IACH,GAAG,CAAA;IACH,GAAG;;EACL;AACA;AAIO,SAAS,eAAe;AAC9B,MAAI,CAAC,OAAO,GAAG;AACd,YAAQ,OAAO,CAAC;EAClB;AACC,WAAS,OAAO;AACjB;AAOO,SAAS,cAAc,OAAO,OAAO;AAC3C,MAAI,SAAS,MAAM,GAAG;AACrB,aAAS,OAAO,KAAK;AACrB,UAAM,EAAE,KAAK;EACf;AACA;AASO,SAAS,eAAe,OAAO,OAAOC,SAAQ,UAAU;AAC9D,MAAI,SAAS,MAAM,GAAG;AACrB,QAAI,SAAS,IAAI,KAAK;AAAG;AACzB,aAAS,IAAI,KAAK;AAClB,WAAO,EAAE,KAAK,MAAM;AACnB,eAAS,OAAO,KAAK;AACrB,UAAI,UAAU;AACb,YAAIA;AAAQ,gBAAM,EAAE,CAAC;AACrB,iBAAQ;MACZ;IACA,CAAG;AACD,UAAM,EAAE,KAAK;EACf,WAAY,UAAU;AACpB,aAAQ;EACV;AACA;AAKA,IAAM,kBAAkB,EAAE,UAAU,EAAC;AAQ9B,SAAS,qBAAqB,MAAM,IAAI,QAAQ;AAGtD,QAAM,UAAU,EAAE,WAAW,KAAI;AACjC,MAAIC,UAAS,GAAG,MAAM,QAAQ,OAAO;AACrC,MAAI,UAAU;AACd,MAAI;AACJ,MAAI;AACJ,MAAI,MAAM;AAIV,WAAS,UAAU;AAClB,QAAI;AAAgB,kBAAY,MAAM,cAAc;EACtD;AAIC,WAAS,KAAK;AACb,UAAM;MACL,QAAQ;MACR,WAAW;MACX,SAASC;MACT,OAAO;MACP;IACH,IAAMD,WAAU;AACd,QAAI;AAAK,uBAAiB,YAAY,MAAM,GAAG,GAAG,UAAU,OAAO,QAAQ,KAAK,KAAK;AACrF,SAAK,GAAG,CAAC;AACT,UAAM,aAAa,IAAG,IAAK;AAC3B,UAAM,WAAW,aAAa;AAC9B,QAAI;AAAM,WAAK,MAAK;AACpB,cAAU;AACV,wBAAoB,MAAM,SAAS,MAAM,MAAM,OAAO,CAAC;AACvD,WAAO,KAAK,CAACX,SAAQ;AACpB,UAAI,SAAS;AACZ,YAAIA,QAAO,UAAU;AACpB,eAAK,GAAG,CAAC;AACT,mBAAS,MAAM,MAAM,KAAK;AAC1B,kBAAO;AACP,iBAAQ,UAAU;QACvB;AACI,YAAIA,QAAO,YAAY;AACtB,gBAAMQ,KAAI,QAAQR,OAAM,cAAc,QAAQ;AAC9C,eAAKQ,IAAG,IAAIA,EAAC;QAClB;MACA;AACG,aAAO;IACV,CAAG;EACH;AACC,MAAI,UAAU;AACd,SAAO;IACN,QAAQ;AACP,UAAI;AAAS;AACb,gBAAU;AACV,kBAAY,IAAI;AAChB,UAAI,YAAYG,OAAM,GAAG;AACxB,QAAAA,UAASA,QAAO,OAAO;AACvB,aAAI,EAAG,KAAK,EAAE;MAClB,OAAU;AACN,WAAE;MACN;IACA;IACE,aAAa;AACZ,gBAAU;IACb;IACE,MAAM;AACL,UAAI,SAAS;AACZ,gBAAO;AACP,kBAAU;MACd;IACA;EACA;AACA;AA+FO,SAAS,gCAAgC,MAAM,IAAI,QAAQ,OAAO;AAGxE,QAAM,UAAU,EAAE,WAAW,OAAM;AACnC,MAAIA,UAAS,GAAG,MAAM,QAAQ,OAAO;AACrC,MAAIH,KAAI,QAAQ,IAAI;AAIpB,MAAI,kBAAkB;AAItB,MAAI,kBAAkB;AACtB,MAAI,iBAAiB;AAGrB,MAAI;AAIJ,WAAS,kBAAkB;AAC1B,QAAI;AAAgB,kBAAY,MAAM,cAAc;EACtD;AAOC,WAASK,MAAK,SAAS,UAAU;AAChC,UAAMC;;MAAiC,QAAQ,IAAIN;;AACnD,gBAAY,KAAK,IAAIM,EAAC;AACtB,WAAO;MACN,GAAGN;MACH,GAAG,QAAQ;MACX,GAAAM;MACA;MACA,OAAO,QAAQ;MACf,KAAK,QAAQ,QAAQ;MACrB,OAAO,QAAQ;IAClB;EACA;AAMC,WAAS,GAAG,GAAG;AACd,UAAM;MACL,QAAQ;MACR,WAAW;MACX,SAASF;MACT,OAAO;MACP;IACH,IAAMD,WAAU;AAId,UAAM,UAAU;MACf,OAAO,IAAG,IAAK;MACf;IACH;AAEE,QAAI,CAAC,GAAG;AAEP,cAAQ,QAAQ;AAChB,aAAO,KAAK;IACf;AAEE,QAAI,WAAW,MAAM;AACpB,UAAI,GAAG;AACN,YAAI,yBAAyB,QAAW;AAEvC,eAAK,QAAQ;QAClB;MACA,OAAU;AACN;QAAmD,KAAM;AACzD,aAAK,QAAQ;MACjB;IACA;AAEE,QAAI,mBAAmB,iBAAiB;AACvC,wBAAkB;IACrB,OAAS;AAGN,UAAI,KAAK;AACR,wBAAe;AACf,yBAAiB,YAAY,MAAMH,IAAG,GAAG,UAAU,OAAO,QAAQ,GAAG;MACzE;AACG,UAAI;AAAG,aAAK,GAAG,CAAC;AAChB,wBAAkBK,MAAK,SAAS,QAAQ;AACxC,0BAAoB,MAAM,SAAS,MAAM,GAAG,OAAO,CAAC;AACpD,WAAK,CAACb,SAAQ;AACb,YAAI,mBAAmBA,OAAM,gBAAgB,OAAO;AACnD,4BAAkBa,MAAK,iBAAiB,QAAQ;AAChD,4BAAkB;AAClB,mBAAS,MAAM,gBAAgB,GAAG,OAAO;AACzC,cAAI,KAAK;AACR,4BAAe;AACf,6BAAiB;cAChB;cACAL;cACA,gBAAgB;cAChB,gBAAgB;cAChB;cACA;cACAG,QAAO;YACd;UACA;QACA;AACI,YAAI,iBAAiB;AACpB,cAAIX,QAAO,gBAAgB,KAAK;AAC/B,iBAAMQ,KAAI,gBAAgB,GAAI,IAAIA,EAAC;AACnC,qBAAS,MAAM,gBAAgB,GAAG,KAAK;AACvC,gBAAI,CAAC,iBAAiB;AAErB,kBAAI,gBAAgB,GAAG;AAEtB,gCAAe;cACvB,OAAc;AAEN,oBAAI,CAAC,EAAE,gBAAgB,MAAM;AAAG,0BAAQ,gBAAgB,MAAM,CAAC;cACvE;YACA;AACM,8BAAkB;UACxB,WAAgBR,QAAO,gBAAgB,OAAO;AACxC,kBAAMO,KAAIP,OAAM,gBAAgB;AAChC,YAAAQ,KAAI,gBAAgB,IAAI,gBAAgB,IAAI,OAAOD,KAAI,gBAAgB,QAAQ;AAC/E,iBAAKC,IAAG,IAAIA,EAAC;UACnB;QACA;AACI,eAAO,CAAC,EAAE,mBAAmB;MACjC,CAAI;IACJ;EACA;AACC,SAAO;IACN,IAAI,GAAG;AACN,UAAI,YAAYG,OAAM,GAAG;AACxB,aAAI,EAAG,KAAK,MAAM;AACjB,gBAAM,OAAO,EAAE,WAAW,IAAI,OAAO,MAAK;AAE1C,UAAAA,UAASA,QAAO,IAAI;AACpB,aAAG,CAAC;QACT,CAAK;MACL,OAAU;AACN,WAAG,CAAC;MACR;IACA;IACE,MAAM;AACL,sBAAe;AACf,wBAAkB,kBAAkB;IACvC;EACA;AACA;ACzaO,SAAS,kBAAkB,wBAAwB;AACzD,UAAO,iEAAwB,YAAW,SACvC,yBACA,MAAM,KAAK,sBAAsB;AACrC;ACRO,SAAS,kBAAkB,QAAQ,SAAS;AAClD,QAAMI,UAAS,CAAA;AACf,QAAM,cAAc,CAAA;AACpB,QAAM,gBAAgB,EAAE,SAAS,EAAC;AAClC,MAAIhB,KAAI,OAAO;AACf,SAAOA,MAAK;AACX,UAAM,IAAI,OAAOA,EAAC;AAClB,UAAMiB,KAAI,QAAQjB,EAAC;AACnB,QAAIiB,IAAG;AACN,iBAAW,OAAO,GAAG;AACpB,YAAI,EAAE,OAAOA;AAAI,sBAAY,GAAG,IAAI;MACxC;AACG,iBAAW,OAAOA,IAAG;AACpB,YAAI,CAAC,cAAc,GAAG,GAAG;AACxB,UAAAD,QAAO,GAAG,IAAIC,GAAE,GAAG;AACnB,wBAAc,GAAG,IAAI;QAC1B;MACA;AACG,aAAOjB,EAAC,IAAIiB;IACf,OAAS;AACN,iBAAW,OAAO,GAAG;AACpB,sBAAc,GAAG,IAAI;MACzB;IACA;EACA;AACC,aAAW,OAAO,aAAa;AAC9B,QAAI,EAAE,OAAOD;AAAS,MAAAA,QAAO,GAAG,IAAI;EACtC;AACC,SAAOA;AACR;AAEO,SAAS,kBAAkB,cAAc;AAC/C,SAAO,OAAO,iBAAiB,YAAY,iBAAiB,OAAO,eAAe,CAAA;AACnF;ACZO,SAAS,KAAK,WAAW,MAAM,UAAU;AAC/C,QAAM,QAAQ,UAAU,GAAG,MAAM,IAAI;AACrC,MAAI,UAAU,QAAW;AACxB,cAAU,GAAG,MAAM,KAAK,IAAI;AAC5B,aAAS,UAAU,GAAG,IAAI,KAAK,CAAC;EAClC;AACA;AAGO,SAAS,iBAAiB,OAAO;AACvC,WAAS,MAAM,EAAC;AACjB;AAQO,SAAS,gBAAgB,WAAW,QAAQ,QAAQ;AAC1D,QAAM,EAAE,UAAU,aAAY,IAAK,UAAU;AAC7C,cAAY,SAAS,EAAE,QAAQ,MAAM;AAErC,sBAAoB,MAAM;AACzB,UAAM,iBAAiB,UAAU,GAAG,SAAS,IAAI,GAAG,EAAE,OAAO,WAAW;AAIxE,QAAI,UAAU,GAAG,YAAY;AAC5B,gBAAU,GAAG,WAAW,KAAK,GAAG,cAAc;IACjD,OAAS;AAGN,cAAQ,cAAc;IACzB;AACE,cAAU,GAAG,WAAW,CAAA;EAC1B,CAAE;AACD,eAAa,QAAQ,mBAAmB;AACzC;AAGO,SAAS,kBAAkB,WAAW,WAAW;AACvD,QAAM,KAAK,UAAU;AACrB,MAAI,GAAG,aAAa,MAAM;AACzB,2BAAuB,GAAG,YAAY;AACtC,YAAQ,GAAG,UAAU;AACrB,OAAG,YAAY,GAAG,SAAS,EAAE,SAAS;AAGtC,OAAG,aAAa,GAAG,WAAW;AAC9B,OAAG,MAAM,CAAA;EACX;AACA;AAGA,SAAS,WAAW,WAAWhB,IAAG;AACjC,MAAI,UAAU,GAAG,MAAM,CAAC,MAAM,IAAI;AACjC,qBAAiB,KAAK,SAAS;AAC/B,oBAAe;AACf,cAAU,GAAG,MAAM,KAAK,CAAC;EAC3B;AACC,YAAU,GAAG,MAAOA,KAAI,KAAM,CAAC,KAAK,KAAKA,KAAI;AAC9C;AAGO,SAAS,KACf,WACA,SACAkB,WACAC,kBACA,WACAd,QACAe,gBACA,QAAQ,CAAC,EAAE,GACV;AACD,QAAM,mBAAmB;AACzB,wBAAsB,SAAS;AAE/B,QAAM,KAAM,UAAU,KAAK;IAC1B,UAAU;IACV,KAAK,CAAA;;IAEL,OAAAf;IACA,QAAQ;IACR;IACA,OAAO,aAAY;;IAEnB,UAAU,CAAA;IACV,YAAY,CAAA;IACZ,eAAe,CAAA;IACf,eAAe,CAAA;IACf,cAAc,CAAA;IACd,SAAS,IAAI,IAAI,QAAQ,YAAY,mBAAmB,iBAAiB,GAAG,UAAU,CAAA,EAAG;;IAEzF,WAAW,aAAY;IACvB;IACA,YAAY;IACZ,MAAM,QAAQ,UAAU,iBAAiB,GAAG;EAC9C;AACC,EAAAe,kBAAiBA,eAAc,GAAG,IAAI;AACtC,MAAI,QAAQ;AACZ,KAAG,MAAMF,YACNA,UAAS,WAAW,QAAQ,SAAS,CAAA,GAAI,CAAClB,IAAG,QAAQ,SAAS;AAC9D,UAAM,QAAQ,KAAK,SAAS,KAAK,CAAC,IAAI;AACtC,QAAI,GAAG,OAAO,UAAU,GAAG,IAAIA,EAAC,GAAI,GAAG,IAAIA,EAAC,IAAI,KAAK,GAAI;AACxD,UAAI,CAAC,GAAG,cAAc,GAAG,MAAMA,EAAC;AAAG,WAAG,MAAMA,EAAC,EAAE,KAAK;AACpD,UAAI;AAAO,mBAAW,WAAWA,EAAC;IACvC;AACI,WAAO;EACX,CAAK,IACD,CAAA;AACH,KAAG,OAAM;AACT,UAAQ;AACR,UAAQ,GAAG,aAAa;AAExB,KAAG,WAAWmB,mBAAkBA,iBAAgB,GAAG,GAAG,IAAI;AAC1D,MAAI,QAAQ,QAAQ;AACnB,QAAI,QAAQ,SAAS;AAEpB,YAAM,QAAQ,SAAS,QAAQ,MAAM;AAErC,SAAG,YAAY,GAAG,SAAS,EAAE,KAAK;AAClC,YAAM,QAAQ,MAAM;IACvB,OAAS;AAEN,SAAG,YAAY,GAAG,SAAS,EAAC;IAC/B;AACE,QAAI,QAAQ;AAAO,oBAAc,UAAU,GAAG,QAAQ;AACtD,oBAAgB,WAAW,QAAQ,QAAQ,QAAQ,MAAM;AAEzD,UAAK;EACP;AACC,wBAAsB,gBAAgB;AACvC;AA4RO,IAAM,kBAAN,MAAsB;EAAtB;AAQN;;;;;;;;AAQA;;;;;;;;;;EAGA,WAAW;AACV,sBAAkB,MAAM,CAAC;AACzB,SAAK,WAAW;EAClB;;;;;;;EAQC,IAAI,MAAM,UAAU;AACnB,QAAI,CAAC,YAAY,QAAQ,GAAG;AAC3B,aAAO;IACV;AACE,UAAM,YAAY,KAAK,GAAG,UAAU,IAAI,MAAM,KAAK,GAAG,UAAU,IAAI,IAAI,CAAA;AACxE,cAAU,KAAK,QAAQ;AACvB,WAAO,MAAM;AACZ,YAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,UAAI,UAAU;AAAI,kBAAU,OAAO,OAAO,CAAC;IAC9C;EACA;;;;;EAMC,KAAKd,QAAO;AACX,QAAI,KAAK,SAAS,CAAC,SAASA,MAAK,GAAG;AACnC,WAAK,GAAG,aAAa;AACrB,WAAK,MAAMA,MAAK;AAChB,WAAK,GAAG,aAAa;IACxB;EACA;AACA;ACneO,IAAM,iBAAiB;ACP9B,IAAI,OAAO,WAAW;AAErB,GAAC,OAAO,aAAa,OAAO,WAAW,EAAE,GAAG,oBAAI,IAAG,EAAE,IAAK,EAAE,IAAI,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;ACJ/E,aAAgB,QAAA,IAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAhB,aAAc,QAAAG,IAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAd,IAAM,QAAQ;;;;;;;;;;;;;;;;;AAkBd,IAAM,QAAQ;;;;;;;;;;AAWd,IAAM,cAAc;;;;;;;;;;AAWpB,IAAM,eAAe;;;;;;;;;;AAWrB,IAAMa,UAAQ;;;;;;;;;;;AAYd,IAAM,OAAO;;;;;;;;;;;;;AAcb,IAAMC,UAAQ;;;;;;;;;;;;;;AAed,IAAM,eAAe;;;;;;;;;;;;;;AAerB,IAAM,SAAS;;;;;;;;;;;;;;;;AAiBf,IAAM,WAAW;;;;;;;;;;;;;AAcjB,IAAM,SAAS;;;;;;;;;;;;;;AAef,IAAM,QAAQ;;;;;;;;;;;;;;AAed,IAAM,OAAO;;;;;;;;;;;;;;;;;AAkBb,IAAMC,UAAQ;;;;;;;;;;;;;AAcd,IAAMC,aAAW;;;;;;;;;;;;;;;;AAiBjB,IAAM,SAAS;;;;;;;;;;;;;;;;AAiBf,IAAM,QAAQ;;;;;;;;;;;;;;;;AAiBd,IAAMC,WAAS;;;;;;;;;;;;;AAcf,IAAM,QAAQ;;;;;;;;;;;;;;;;;;AAmBd,IAAM,QAAQ;;;;;;;;;;;;;;;;;;;AAoBd,IAAM,QAAQ;EACV,MAAM;EACN,OAAO;EACP,OAAOJ;EACP,aAAa;EACb,OAAO;EACP,OAAOE;EACP,iBAAiB;EACjB,gBAAgB;EAChB,QAAQE;EACR,QAAQ;EACR,QAAQ;EACR,UAAUD;EACV,OAAO;EACP,QAAQ;EACR,MAAM;EACN,OAAO;EACP,OAAO;EACP,iBAAiB;EACjB,OAAOF;EACP,OAAO;;;;;;;AC1TA,MAAA,YAAA;;IAAM,IAAI,CAAA;EAAA,IAAA;;;;;;;;QADD,IAAI,CAAA;MAAA;;;;;;;QAAiD,IAAK,CAAA;MAAA;;;;AAA9E,aAEK,QAAA,KAAA,MAAA;;;;AADM,UAAA;MAAA,KAAA,eAAA,YAAA;;QAAMI,KAAI,CAAA;MAAA,IAAA;AAAA,YAAA,YAAA;;;;;;;UADDA,KAAI,CAAA;QAAA;;;;;;;;UAAiDA,KAAK,CAAA;QAAA;;;;;;;;;;;;;QAL/D,KAAU,IAAA;AACV,MAAA,EAAA,MAAAC,QAAiC,iBAAgB,IAAA;AACjD,MAAA,EAAA,QAAgB,eAAc,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;QCOzB,IAAI,CAAA;;;;;QAAiC,IAAS,CAAA;;;;;;;;;;;;;;;;;QAA9CD,KAAI,CAAA;;;;QAAiCA,KAAS,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAG9C,IAAK,CAAA;MAAA;;;;;;;;;;;UAALA,KAAK,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAGN,IAAO,CAAA;MAAA;;;;;;;;;;;UAAPA,KAAO,CAAA;QAAA;;;;;;;;;;;;;;;;IAPjB,IAAI,CAAA,KAAAE,oBAAA,GAAA;;;;IAGJ,IAAK,CAAA,KAAAC,oBAAA,GAAA;;;;IAGL,IAAO,CAAA,KAAAC,kBAAA,GAAA;;;;;;;;;;;;;;;;AAPhB,aAUK,QAAA,KAAA,MAAA;;;;;;;;;;;;;;QATIJ,KAAI,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;QAGJA,KAAK,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;QAGLA,KAAO,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAbD,MAAA,EAAA,QAA4B,OAAS,IAAA;AACrC,MAAA,EAAA,UAA8B,OAAS,IAAA;AACvC,MAAA,EAAA,OAA6B,OAAS,IAAA;AACtC,MAAA,EAAA,YAAoB,eAAc,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QCNlC,MAAa,IAAA;QACb,QAAe,IAAA;;;;;;;;;;;;;;;ACM9B,IAAM,mBAAmB,CAAA;AAWlB,SAAS,SAAS,OAAO,OAAO;AACtC,SAAO;IACN,WAAW,SAAS,OAAO,KAAK,EAAE;EACpC;AACA;AAWO,SAAS,SAAS,OAAO,QAAQ,MAAM;AAE7C,MAAI;AAEJ,QAAM,cAAc,oBAAI,IAAG;AAI3B,WAAS,IAAI,WAAW;AACvB,QAAI,eAAe,OAAO,SAAS,GAAG;AACrC,cAAQ;AACR,UAAI,MAAM;AAET,cAAM,YAAY,CAAC,iBAAiB;AACpC,mBAAW,cAAc,aAAa;AACrC,qBAAW,CAAC,EAAC;AACb,2BAAiB,KAAK,YAAY,KAAK;QAC5C;AACI,YAAI,WAAW;AACd,mBAAS1B,KAAI,GAAGA,KAAI,iBAAiB,QAAQA,MAAK,GAAG;AACpD,6BAAiBA,EAAC,EAAE,CAAC,EAAE,iBAAiBA,KAAI,CAAC,CAAC;UACpD;AACK,2BAAiB,SAAS;QAC/B;MACA;IACA;EACA;AAMC,WAASgB,QAAO,IAAI;AACnB,QAAI,GAAG,KAAK,CAAC;EACf;AAOC,WAASe,WAAUC,MAAK,aAAa,MAAM;AAE1C,UAAM,aAAa,CAACA,MAAK,UAAU;AACnC,gBAAY,IAAI,UAAU;AAC1B,QAAI,YAAY,SAAS,GAAG;AAC3B,aAAO,MAAM,KAAKhB,OAAM,KAAK;IAChC;AACE,IAAAgB,KAAI,KAAK;AACT,WAAO,MAAM;AACZ,kBAAY,OAAO,UAAU;AAC7B,UAAI,YAAY,SAAS,KAAK,MAAM;AACnC,aAAI;AACJ,eAAO;MACX;IACA;EACA;AACC,SAAO,EAAE,KAAK,QAAAhB,SAAQ,WAAAe,WAAS;AAChC;AAsCO,SAAS,QAAQ,QAAQ,IAAI,eAAe;AAClD,QAAM,SAAS,CAAC,MAAM,QAAQ,MAAM;AAEpC,QAAM,eAAe,SAAS,CAAC,MAAM,IAAI;AACzC,MAAI,CAAC,aAAa,MAAM,OAAO,GAAG;AACjC,UAAM,IAAI,MAAM,sDAAsD;EACxE;AACC,QAAM,OAAO,GAAG,SAAS;AACzB,SAAO,SAAS,eAAe,CAAC,KAAKf,YAAW;AAC/C,QAAI,UAAU;AACd,UAAM,SAAS,CAAA;AACf,QAAI,UAAU;AACd,QAAI,UAAU;AACd,UAAM,OAAO,MAAM;AAClB,UAAI,SAAS;AACZ;MACJ;AACG,cAAO;AACP,YAAM,SAAS,GAAG,SAAS,OAAO,CAAC,IAAI,QAAQ,KAAKA,OAAM;AAC1D,UAAI,MAAM;AACT,YAAI,MAAM;MACd,OAAU;AACN,kBAAU,YAAY,MAAM,IAAI,SAAS;MAC7C;IACA;AACE,UAAM,gBAAgB,aAAa;MAAI,CAAC,OAAOhB,OAC9C;QACC;QACA,CAAC,UAAU;AACV,iBAAOA,EAAC,IAAI;AACZ,qBAAW,EAAE,KAAKA;AAClB,cAAI,SAAS;AACZ,iBAAI;UACV;QACA;QACI,MAAM;AACL,qBAAW,KAAKA;QACrB;MACA;IACA;AACE,cAAU;AACV,SAAI;AACJ,WAAO,SAAS,OAAO;AACtB,cAAQ,aAAa;AACrB,cAAO;AAIP,gBAAU;IACb;EACA,CAAE;AACF;SCvKgB,aAAU;AACtB,SAAO,IAAI,KAAK;AAEhB,SAAO,IAAI,EAAC,GAAG,2BAA0B,CAAC;AAC1C,QAAM,IAAI,EAAC,GAAG,0BAAyB,CAAC;AACxC,SAAO,MAAK;AAEZ,qBAAmB,IAAI,CAAA,CAAE;AACzB,kBAAgB,IAAI,MAAS;AAE7B,eAAa,IAAI,MAAS;AAC1B,eAAa,IAAI,MAAS;AAC1B,gBAAc,IAAI,EAAC,GAAG,qBAAoB,CAAC;AAE3C,yBAAuB,IAAI,MAAS;AACpC,yBAAuB,IAAI,MAAS;AACpC,0BAAwB,IAAI,EAAC,GAAG,+BAA8B,CAAC;AAE/D,eAAa,IAAI,MAAS;AAC1B,aAAW,IAAI,MAAS;AACxB,sBAAoB,IAAI,MAAS;AACrC;AAGO,IAAM,SAAS,SAAkB,KAAK;AAGtC,IAAM,gBAAgB,SAAS,KAAK;AASpC,IAAM,+BAAsD;EAC/D,UAAU;EACV,OAAO;EACP,YAAY;;AAGA,SAAA,kBAAkB,OAAO,8BAA4B;AACjE,QAAM,QAAQ,SAAS,IAAI;AAC3B,QAAM,EAAC,WAAA+B,YAAW,IAAG,IAAI;AAEzB,MAAI;AACJ,MAAI,OAAO,iBAAiB,aAAa;AACrC,cAAU,IAAI,oBAAoB,cAAc;AAChD,YAAQ,KAAK,UAAU,EAAE,KAAK,CAAC,aAAY;AACvC,UAAI,UAAU;AACV,YAAI,KAAK,MAAM,QAAQ,CAAC;MAC3B;IACL,CAAC;EACJ;AAED,SAAO;IACH,WAAAA;IACA,KAAK,CAACd,OAAK;AACP,UAAI,SAAS;AACT,gBAAQ,MAAM,YAAY,KAAK,UAAUA,EAAC,CAAC;MAC9C;AACD,UAAIA,EAAC;;IAET,QAAQ,CAAC,OAAM;AACX,YAAM,eAAe,GAAGgB,gBAAI,KAAK,CAAC;AAClC,UAAI,SAAS;AACT,gBAAQ,MAAM,YAAY,KAAK,UAAU,YAAY,CAAC;MACzD;AACD,UAAI,YAAY;;;AAG5B;AAEO,IAAMC,aAA4C,kBAAiB;AASnE,IAAM,4BAAgD;EACzD,OAAO;EACP,UAAU;;AAGP,IAAM,QAAQ,SAA6B,yBAAyB;AAQpE,IAAM,6BAAkD;EAC3D,MAAM;EACN,SAAS,CAAA;;AAQN,IAAM,aAAa,MAAa;AACnC,QAAM,EAAC,KAAK,WAAAH,YAAW,QAAAf,QAAM,IAAI,SAA8B,0BAA0B;AACzF,SAAO;;IAEH,MAAM,MACFA,QAAO,CAAC,aAAkC;MACtC,GAAG;MACH,MAAM,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,CAAC;MAChD,SAAS,QAAQ,QAAQ,MAAM,GAAG,EAAE;IACvC,EAAC;;IAEN,MAAM,CAAC,SACHA,QAAO,CAAC,aAAa;MACjB,GAAG;MACH;MACA,SAAS,CAAC,GAAG,QAAQ,SAAS,QAAQ,IAAI;IAC7C,EAAC;IACN;IACA,WAAAe;IACA,QAAAf;;AAER;AAEO,IAAM,SAAS,WAAU;AAIzB,IAAM,qBAAqB,SAA2B,CAAA,CAAE;AAExD,IAAM,kBAAkB,SAAsC,MAAS;AAYvE,IAAM,aAAa,MAAa;AACnC,QAAM,EAAC,KAAK,WAAAe,YAAW,QAAAf,QAAM,IAAI,SAA0C,MAAS;AACpF,SAAO;IACH,OAAO,MAAM,IAAI,MAAS;IAC1B;IACA,WAAAe;IACA,QAAAf;;AAER;AAEO,IAAM,SAAS,WAAU;AAazB,IAAM,uBAAuB;EAChC,SAAS;EACT,iBAAiB;EACjB,mBAAmB;;AAGhB,IAAM,eAAe,SAAmC,MAAS;AACjE,IAAM,eAAe,SAAmC,MAAS;AACjE,IAAM,gBAAgB,SAAiC,EAAC,GAAG,qBAAoB,CAAC;AAShF,IAAM,iCAAuE;EAChF,OAAO;EACP,UAAU;;AAGP,IAAM,yBAAyB,SAA2C,MAAS;AACnF,IAAM,0BAA0B,SAA+C;EAClF,GAAG;AACN,CAAA;AACM,IAAM,yBAAyB,SAA6C,MAAS;AAErF,IAAM,eAAe,SAA6B,MAAS;AAE3D,IAAM,aAAa,SAA+B,MAAS;AAE3D,IAAM,sBAAsB,SAA0C,MAAS;;;;;;MC9MzD;;QAAA,IAAG,CAAA,EAAA,eAAgB,EAAA,SAAS,QAAO,CAAA;;;;QAAa,IAAa,CAAA;;;;;;;;;;;;;;AAA7D,UAAA;MAAA;AAAA,6BAAA;QAAAU,KAAG,CAAA,EAAA,eAAgB,EAAA,SAAS,QAAO,CAAA;;;;QAAaA,KAAa,CAAA;;;;;;;;;;;;;;;;;;;;;;;IADjF,IAAa,CAAA,KAAAI,kBAAA,GAAA;;;;;;;;;;AADtB,aAIK,QAAA,KAAA,MAAA;;;;;;;;QAHIJ,KAAa,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAJX,GAAAjB,GAAC,IAAI,WAAqB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACH3C,aAEI,QAAA,IAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCuCqBiB,KAAI,CAAA;;AAAA,aAAA;;;MAICA,KAAW,CAAA;;AAAA,aAAA;;;;;;;;IASpB,IAAK,CAAA,KAAAS,oBAAA,GAAA;;;;IAIL,IAAY,CAAA,KAAAC,oBAAA,GAAA;;;;;;;;;;;;QANI,IAAK,CAAA;MAAA;;;;;;;;;;;;;QAbrB,IAAI,CAAA;MAAA;;;;;;AAAb,aAwBG,QAAA,GAAA,MAAA;AAvBC,aAUK,GAAA,GAAA;;;;;AAEL,aAAiC,GAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAZV,KAAK,CAAA;QAAA;;;QAErBA,KAAK,CAAA;QAAA;;;;;;;;;;;;;;QAILA,KAAY,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;UAnBZA,KAAI,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAxBAA,KAAI,CAAA;;AAAA,aAAA;;;MAICA,KAAW,CAAA;;AAAA,aAAA;;;;;;;;IASpB,IAAK,CAAA,KAAAE,oBAAA,GAAA;;;;IAIL,IAAY,CAAA,KAAAC,oBAAA,GAAA;;;;;;;;;;;;QANI,IAAK,CAAA;MAAA;;;;;;;;;;;;AAb9B,aAwBQ,QAAA,QAAA,MAAA;AAvBJ,aAUK,QAAA,GAAA;;;;;AAEL,aAAiC,QAAA,IAAA;;;;;;;;;;;AAbnB,cAAA;;YAAA,IAAO,CAAA;UAAA;AAAP,gBAAO,CAAA,EAAA,MAAA,MAAA,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAaA,IAAK,CAAA;QAAA;;;QAErB,IAAK,CAAA;QAAA;;;;;;;;;;;;;;QAIL,IAAY,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeO,IAAW,CAAA;IAAA,EAAA,CAAA;;;;;;;;AAD3B,aAEK,QAAA,KAAA,MAAA;;;;;;;;;QADWH,KAAW,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAJb,IAAI,CAAA,CAAA;AAAA,aAAA,KAAA,OAAA,aAAA;;MAAU,IAAK,CAAA,CAAA,OAAA;;;;;AADjC,aAEK,QAAA,KAAA,MAAA;AADD,aAAuC,KAAA,GAAA;;;;;MAA7BA,KAAI,CAAA,CAAA,GAAA;;;;;MAAUA,KAAK,CAAA,CAAA,UAAA;;;;;;;;;;;;;;;;;;;;;QAYhB,IAAK,CAAA;MAAA;;;;AAA1B,aAAiC,QAAA,MAAA,MAAA;;;;;;;;;UAAZA,KAAK,CAAA;QAAA;;;;;;;;;;;;;;;IAKV,IAAY,CAAA;IAAA,EAAA,CAAA;;;;;;;;AAD5B,aAEK,QAAA,KAAA,MAAA;;;;;;;;;QADWA,KAAY,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;IAvCR,IAAW,CAAA;IAAA,EAAA,CAAA;;;;;;;;AAD3B,aAEK,QAAA,KAAA,MAAA;;;;;;;;;QADWA,KAAW,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAJb,IAAI,CAAA,CAAA;AAAA,aAAA,KAAA,OAAA,aAAA;;MAAU,IAAK,CAAA,CAAA,OAAA;;;;;AADjC,aAEK,QAAA,KAAA,MAAA;AADD,aAAuC,KAAA,GAAA;;;;;MAA7BA,KAAI,CAAA,CAAA,GAAA;;;;;MAAUA,KAAK,CAAA,CAAA,UAAA;;;;;;;;;;;;;;;;;;;;;QAYhB,IAAK,CAAA;MAAA;;;;AAA1B,aAAiC,QAAA,MAAA,MAAA;;;;;;;;;UAAZA,KAAK,CAAA;QAAA;;;;;;;;;;;;;;;IAKV,IAAY,CAAA;IAAA,EAAA,CAAA;;;;;;;;AAD5B,aAEK,QAAA,KAAA,MAAA;;;;;;;;;QADWA,KAAY,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAtBlCA,KAAI,CAAA;AAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFlB,aAwDI,QAAA,IAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjEW,MAAA,EAAA,QAA4B,OAAS,IAAA;QACrC,UAAO,MAAA;;AACP,MAAA,EAAA,cAAwD,OAAS,IAAA;AACjE,MAAA,EAAA,eAAgE,gBAAe,IAAA;AAC/E,MAAA,EAAA,OAA2B,OAAS,IAAA;AACpC,MAAA,EAAA,QAA4B,OAAS,IAAA;AACrC,MAAA,EAAA,OAA2B,OAAS,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACL7C,SAAU,WAAW,KAAW;AAClC,SAAO,IAAI,WAAW,SAAS,KAAK,IAAI,WAAW,UAAU;AACjE;AAEM,SAAU,cAAc,KAAW;AACrC,SAAO,IAAI,WAAW,aAAa;AACvC;AAEM,SAAU,YAAY,KAAW;AACnC,SAAO,OAAO;AAClB;AAGM,SAAU,cACZ,UAAgD;;AAEhD,QAAM,EAAC,MAAM,KAAI,IAAI;AACrB,MAAI,EAAC,MAAK,IAAIO,gBAAIC,UAAQ;AAC1B,QAAM,gBAAgB,UAAU,UAAU,SAAS;AAEnD,MAAI,CAAC,OAAO;AAER,WAAO,WAAW,8BAA8B,EAAE,UAC3C,QAAQ,SACR,QAAQ;EAClB;AAED,MAAI,CAAC,MAAM;AACP,QAAI,aAAa,UAAU;AACvB,eAAO,cAAS,QAAO,MAAhB,mBAAqB,aAAU,cAAS,QAAO,MAAhB,mBAAqB;IAC9D;AACD,YAAQ,KAAK,GAAG,IAAI,wBAAwB;AAC5C;EACH;AAED,QAAM,QAAQ,KAAK,KAAK,KAAK,KAAK,aAAa;AAE/C,MAAI,CAAC,WAAW,MAAM,SAAQ,CAAE,KAAK,CAAC,cAAc,MAAM,SAAQ,CAAE,GAAG;AACnE,YAAQ,KAAK,GAAG,IAAI,IAAI,KAAK,wCAAwC;AACrE;EACH;AAED,SAAO;AACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9BI,aAYS,QAAA,SAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAXO,IAAK,CAAA;MAAA;;;;;;;;;;;UAALR,KAAK,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;MAIE;;QAAA,IAAK,CAAA,EAAC;;;;MAGP,MAAA;;QAAc,IAAK,CAAA;MAAA;;;;;;;;;;;;;;AAHlB,UAAA;MAAA;AAAA,yBAAA;QAAA,IAAK,CAAA,EAAC;;;;AAGP,UAAA;MAAA;AAAA,yBAAA,OAAA;;UAAc,IAAK,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;IAL1B,IAAM,CAAA;EAAA;;mCAAX,QAAI1B,MAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAC0B,KAAM,CAAA;QAAA;;qCAAX,QAAI1B,MAAA,GAAA;;;;;;;;;;;;;6BAAJ,QAAIA,KAAA,YAAA,QAAAA,MAAA,GAAA;;;;;;;;;uCAAJ,QAAIA,MAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;IAJb,IAAM,CAAA,KAAA8B,kBAAA,GAAA;;;;;;;;;;;;;;;;;QAANJ,KAAM,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QATI,OAAyB,IAAA;QACzB,MAAa,IAAA;AAElB,QAAAW,YAAW,sBAAqB;AAaP,QAAA,OAAA,WAAAA,UAAS,UAAU,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;ICL1C,IAAI,CAAA;IAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IADf,IAAI,CAAA,KAAAP,kBAAA,GAAA;;;;;;;;;;MAIF,IAAK,CAAA,CAAA;;MALO,IAAO,CAAA,IAAA,gBAAA;;;;;AAA9B,aAMQ,QAAA,QAAA,MAAA;;;;AADJ,aAAmB,QAAA,IAAA;;;;;;;;;;;;UALoB,IAAO,CAAA;QAAA;;;;;;;QACzCJ,KAAI,CAAA;;AAAA,iBAAA,EAAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;QANE,KAAiB,IAAA;UAErB,OAAO,MAAM,SAAS,UAAU,WAAW,UAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACNnE,aAOC,QAAA,OAAA,MAAA;;;;;;;;;;;;;;AAH4B,gBAAA;;cAAA,IAAO,CAAA;YAAA;AAAP,kBAAO,CAAA,EAAA,MAAA,MAAA,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAXrB,MAAa,IAAA;QACb,YAAmB,IAAA;QACnB,QAAO,IAAA;AACP,MAAA,EAAA,YAAqB,MAAK,IAAA;AAC1B,MAAA,EAAA,OAAAY,SAAiB,MAAK,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QCHtB,MAAa,IAAA;QACb,QAAe,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCuHL,IAAW,EAAA;;;;IAGR,IAAK,CAAA;;;MACV,IAAe,CAAA;MAAI,IAAK,CAAA;MAAK,IAAS,CAAA;;;;;IAHjC,IAAK,CAAA,MAAA;IAAA;;IAAL,IAAK,CAAA;;;;;;IAKhB,IAAe,CAAA,KAAAC,oBAAA,GAAA;;;;;QAUZ,SAAS;QACT;;UAAS,IAAM,EAAA;;QACf;;UAAO,IAAE,CAAA,EAAC,sBACN,EAAA,SAAS,iBAAgB,CAAA;;;;;;;;;;;;;;;;;;;;;AArBzC,aAyBK,QAAA,KAAA,MAAA;;;;;;;;;;;;;;;;;;;;QApBeb,KAAK,CAAA;;;;QACVA,KAAe,CAAA;QAAIA,KAAK,CAAA;QAAKA,KAAS,CAAA;;;;;QAHjCA,KAAK,CAAA;;;;;;QAKhBA,KAAe,CAAA;QAAA;;;;;;;;;;;;;;;;UAUZ,SAAS;UACT;;YAASA,KAAM,EAAA;;UACf;;YAAOA,KAAE,CAAA,EAAC,sBACN,EAAA,SAAS,iBAAgB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA5B5B;;QAAA,IAAE,CAAA,EAAC,yBAAuB;UAC/B,SAAS;UACT;;YAAW,IAAS,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAFf,UAAA;MAAA;AAAA,+BAAA;QAAAA,KAAE,CAAA,EAAC,yBAAuB;UAC/B,SAAS;UACT;;YAAWA,KAAS,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjBR,MAAA;;IAAA,IAAG,CAAA,EAAA,WAAY,EAAA,SAAS,aAAY,CAAA,IAAA;;;;;;;;;;AAAxD,aAA8D,QAAAlB,IAAA,MAAA;;;;AAA1C,UAAA;MAAA,OAAA,eAAA;MAAAkB,KAAG,CAAA,EAAA,WAAY,EAAA,SAAS,aAAY,CAAA,IAAA;AAAA,iBAAA,KAAA,SAAA;;;;;;;;;;;;;;;;;QAqB5C,IAAK,CAAA;MAAA;;;;;;;;;;;UAALA,KAAK,CAAA;QAAA;;;;;;;;;;;AAWJ,MAAA;;IAAA,IAAG,CAAA,EAAA,yBACA,EAAA,SAAS,yBAAwB,CAAA,IAAA;;;;;;;;;;;;QAEpC,IAAS,CAAA;MAAA;;;;AAJd,aAKG,QAAAlB,IAAA,MAAA;;;;;;AAJE,UAAA;MAAA,OAAA,cAAA;MAAAkB,KAAG,CAAA,EAAA,yBACA,EAAA,SAAS,yBAAwB,CAAA,IAAA;AAAA,iBAAA,IAAA,QAAA;;;;;;UAEpCA,KAAS,CAAA;QAAA;;;;;;;;;;AAvBV,MAAA;;IAAA,IAAE,CAAA,EAAC,0BAA0B,IAAA;;;;;;;;;;;AAA7B,UAAA;MAAA,OAAA,eAAA;MAAAA,KAAE,CAAA,EAAC,0BAA0B,IAAA;AAAA,iBAAA,KAAA,SAAA;;;;;;;;;;AAV7B,MAAA;;IAAA,IAAE,CAAA,EAAC,sBAAsB,IAAA;;;;;;;;;;;AAAzB,UAAA;MAAA,OAAA,eAAA;MAAAA,KAAE,CAAA,EAAC,sBAAsB,IAAA;AAAA,iBAAA,KAAA,SAAA;;;;;;;;;;;;;;;;;;;;;;;MAIlB,OAAA;;QAAO,IAAU,EAAA;MAAA;;;;;;;;;;;;;;;AAAjB,UAAA;MAAA;AAAA,yBAAA,QAAA;;UAAO,IAAU,EAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;IAFzB,IAAW,CAAA;EAAA;;mCAAhB,QAAI1B,MAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAC0B,KAAW,CAAA;QAAA;;qCAAhB,QAAI1B,MAAA,GAAA;;;;;;;;;;;;;6BAAJ,QAAIA,KAAA,YAAA,QAAAA,MAAA,GAAA;;;;;;;;;uCAAJ,QAAIA,MAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MALT0B,KAAK,CAAA;;AAAA,aAAA;AAEA;;MAAAA,KAAe,CAAA;MAAAA,KAAY,CAAA,EAAA,SAAS;;AAAC,aAAA;;;MAUrCA,KAAS,CAAA;;AAAA,aAAA;;IASRA,KAAW,CAAA;AAAA,aAAA;;;;;;;;;;;;;;AAtB1B,aAmDS,QAAA,SAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UA7HE,GAAAjB,GAAC,IAAI,WAAqB,MAAM;;QAE5B,QAAoC,IAAA;QACpC,OAAiB,IAAA;QACjB,aAAuC,IAAA;QACvC,MAAa,IAAA;AAElB,QAAA4B,YAAW,sBAAqB;MAKlC,OAAO,SAAS,IAAI;;AACpB,MAAA,QAAgB;AAChB,MAAA,YAAoB;MACpB;AACA,MAAA,kBAA2B;MAC3B;AACA,MAAA,YAAgC,aAAa,SAAS;AAE1D,UAAO,YAAA;QACC,aAAa,OAAO,0BAAwB;UACxC,WAAW,aAAa,mBAAiB;;AAErC,uBAAA,GAAA,YAAY,OAAM,MAAO,aAAa,kBAAkB,OAAO,CAAA,CAAA;iBAC1DC,QAAK;AACV,uBAAa,IAAI,OAAOA,MAAK,CAAA;gBACvBA;;;YAGR,WAAQ,MAAS,OAAO,KAAI;QAC9B,MAAM;QACN,QAAM,EACF,MAAI,CAAG,SAAS,EAAA;;AAGxB,WAAK,IAAI,KAAK;AACd,mBAAA,GAAA,cAAc,SAAS,SAAS,IAAK,aACjC,gBAAgB,KAAI,GAAI,QAAQ,YAAY,IAAI,QAAQ,eAAe,EAAA,CAAA,CAAA;eAEpE,aAAa,OAAO,yBAAuB;AAClD,WAAK,IAAI,KAAK;sBACd,cAAW,CAAA,CAAA;;;iBAIJ,SAAM;AACjB,SAAK,IAAI,IAAI;;YAEH,WAAQ,MAAS,OAAO,GAAG,MAAM,YAAY,KAAK;AACpD,UAAA,SAAS,aAAa,OAAO,KAAK,GAAA;wBAClC,cAAc,SAAS,YAAY;AACnC,qBAAA,GAAA,cAAc,SAAS,YAAY,IAAK,gBACpC,gBAAgB,KAAI,GAAI,SAAS,YAAY,IAAI,WAAW,SAAS,EAAA,CAAA,CAAA;;AAG7E,mBAAA,GAAA,kBAAkB,KAAK;aAClBA,QAAK;AACV,mBAAA,GAAA,kBAAkB,IAAI;;AAEtB,mBAAA,GAAA,YAAY,KAAK;AACjB,WAAK,IAAI,KAAK;;;AAIb,WAAA,YAAY,OAAoB;QACjC,MAAM,QAAQ,SAAO;AACrB,YAAM,eAAc;AACpB,aAAM;aACC;;;+BAcgBD,UAAS,UAAU,UAAU;;AAkBpC,YAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1G7B,aAYS,QAAA,SAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAXO,IAAK,CAAA;MAAA;;;;;;;;;;;UAALX,KAAK,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;QAIE,IAAM,CAAA,EAAC,SAAS;;;;YAGjB;;QAAc,IAAM,CAAA,EAAC;MAAQ;;;;;;;;;;;;;;;;;QAH5B,IAAM,CAAA,EAAC,SAAS;;;gCAGjB;;UAAc,IAAM,CAAA,EAAC;QAAQ;;;;;;;;;;;;;;;;;;;;;;;IALpC,IAAO,CAAA;EAAA;;mCAAZ,QAAI1B,MAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAC0B,KAAO,CAAA;QAAA;;qCAAZ,QAAI1B,MAAA,GAAA;;;;;;;;;;;;;6BAAJ,QAAIA,KAAA,YAAA,QAAAA,MAAA,GAAA;;;;;;;;;uCAAJ,QAAIA,MAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;IAJb,IAAO,CAAA,KAAA8B,kBAAA,GAAA;;;;;;;;;;;;;;;;;QAAPJ,KAAO,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QATG,QAAoC,IAAA;QACpC,MAAa,IAAA;AAElB,QAAAW,YAAW,sBAAqB;0BAaPA,UAAS,UAAU,KAAK;;;;;;;;;;;;;;;ACnBpD,SAAS,QAAQ,KAAK;AAC5B,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAChD;ACwGO,SAAS,WAAW5B,IAAG;AAC7B,SAAOA,KAAI,MAAM,IAAMA,KAAIA,KAAIA,KAAI,MAAM,KAAK,IAAI,IAAMA,KAAI,GAAK,CAAG,IAAI;AACzE;AAgBO,SAAS,SAASA,IAAG;AAC3B,QAAM+B,KAAI/B,KAAI;AACd,SAAO+B,KAAIA,KAAIA,KAAI;AACpB;AC7HA,SAAS,iBAAiB,GAAG,GAAG;AAC/B,MAAI,MAAM,KAAK,MAAM;AAAG,WAAO,MAAM;AACrC,QAAM,OAAO,OAAO;AACpB,MAAI,SAAS,OAAO,KAAK,MAAM,QAAQ,CAAC,MAAM,MAAM,QAAQ,CAAC,GAAG;AAC/D,UAAM,IAAI,MAAM,6CAA6C;EAC/D;AACC,MAAI,MAAM,QAAQ,CAAC,GAAG;AACrB,UAAM,MAAM,EAAE,IAAI,CAAC,IAAIxC,OAAM;AAC5B,aAAO,iBAAiB,EAAEA,EAAC,GAAG,EAAE;IACnC,CAAG;AACD,WAAO,CAACS,OAAM,IAAI,IAAI,CAAC,OAAO,GAAGA,EAAC,CAAC;EACrC;AACC,MAAI,SAAS,UAAU;AACtB,QAAI,CAAC,KAAK,CAAC;AAAG,YAAM,IAAI,MAAM,uBAAuB;AACrD,QAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC,GAAG;AAC7B,UAAI,EAAE,QAAO;AACb,UAAI,EAAE,QAAO;AACb,YAAM,QAAQ,IAAI;AAClB,aAAO,CAACA,OAAM,IAAI,KAAK,IAAIA,KAAI,KAAK;IACvC;AACE,UAAM,OAAO,OAAO,KAAK,CAAC;AAC1B,UAAM,gBAAgB,CAAA;AACtB,SAAK,QAAQ,CAAC,QAAQ;AACrB,oBAAc,GAAG,IAAI,iBAAiB,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;IACvD,CAAG;AACD,WAAO,CAACA,OAAM;AACb,YAAM,SAAS,CAAA;AACf,WAAK,QAAQ,CAAC,QAAQ;AACrB,eAAO,GAAG,IAAI,cAAc,GAAG,EAAEA,EAAC;MACtC,CAAI;AACD,aAAO;IACV;EACA;AACC,MAAI,SAAS,UAAU;AACtB,UAAM,QAAQ,IAAI;AAClB,WAAO,CAACA,OAAM,IAAIA,KAAI;EACxB;AACC,QAAM,IAAI,MAAM,sBAAsB,IAAI,SAAS;AACpD;AAWO,SAAS,QAAQ,OAAO,WAAW,CAAA,GAAI;AAC7C,QAAM,QAAQ,SAAS,KAAK;AAE5B,MAAI;AACJ,MAAI,eAAe;AAKnB,WAAS,IAAI,WAAW,MAAM;AAC7B,QAAI,SAAS,MAAM;AAClB,YAAM,IAAK,QAAQ,SAAS;AAC5B,aAAO,QAAQ,QAAO;IACzB;AACE,mBAAe;AACf,QAAI,gBAAgB;AACpB,QAAI,UAAU;AACd,QAAI;MACH,QAAQ;MACR,WAAW;MACX,SAASI;MACT,cAAc;IACjB,IAAM,OAAO,OAAO,CAAA,GAAI,QAAQ,GAAG,IAAI;AACrC,QAAI,aAAa,GAAG;AACnB,UAAI,eAAe;AAClB,sBAAc,MAAK;AACnB,wBAAgB;MACpB;AACG,YAAM,IAAK,QAAQ,YAAY;AAC/B,aAAO,QAAQ,QAAO;IACzB;AACE,UAAM,QAAQ,IAAG,IAAK;AACtB,QAAI;AACJ,WAAO,KAAK,CAACZ,SAAQ;AACpB,UAAIA,OAAM;AAAO,eAAO;AACxB,UAAI,CAAC,SAAS;AACb,aAAK,YAAY,OAAO,SAAS;AACjC,YAAI,OAAO,aAAa;AAAY,qBAAW,SAAS,OAAO,SAAS;AACxE,kBAAU;MACd;AACG,UAAI,eAAe;AAClB,sBAAc,MAAK;AACnB,wBAAgB;MACpB;AACG,YAAM,UAAUA,OAAM;AACtB,UAAI;MAAiC,UAAW;AAC/C,cAAM,IAAK,QAAQ,SAAS;AAC5B,eAAO;MACX;AAEG,YAAM,IAAK,QAAQ,GAAG,OAAO,UAAU,QAAQ,CAAC,CAAC;AACjD,aAAO;IACV,CAAG;AACD,WAAO,KAAK;EACd;AACC,SAAO;IACN;IACA,QAAQ,CAAC,IAAI,SAAS,IAAI,GAAG,cAAc,KAAK,GAAG,IAAI;IACvD,WAAW,MAAM;EACnB;AACA;;;;;;;;;;;;ACvBiB,QAAA;;MAAWyB,KAAS,CAAA;IAAA,KAAA;;MAAcA,KAAI,CAAA;IAAA;AAAA,aAAA;AAEjC,QAAA;;MAAYA,KAAI,CAAA;IAAA;AAAA,aAAA;;;;;;;;;;;;;;AAH9B,aAMK,QAAA,KAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAFe,IAAI,CAAA;OAAA,MAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAFN,IAAI,CAAA,CAAA;AAAA,aAAA,KAAA,OAAA,aAAA;;;;;AAAd,aAAsC,QAAA,KAAA,MAAA;;;;;;;;;;;;;;;;;;MASxB,IAAK,CAAA,CAAA;;;;AAAvB,aAA2B,QAAAlB,IAAA,MAAA;;;;;;;;;;;;;IAGrB,IAAS,CAAA;;;;;;;;;;;;;;;;MAATkB,KAAS,CAAA,CAAA,GAAA;;;;;;;;;;;;;;;;;;;AACiB,MAAA,UAAA;;IAAgB,IAAQ,CAAA;EAAA,IAAA;;;;;;;;MAAhC,IAAK,CAAA,CAAA;;;AAAzB,aAA4D,QAAA,MAAA,MAAA;;;;AAAhC,UAAA;MAAA,KAAA,aAAA,UAAA;;QAAgBA,KAAQ,CAAA;MAAA,IAAA;AAAA,iBAAAjB,IAAA,OAAA;;;;;;;;;;;;;;;;;;;;;IAhB3D,IAAI,CAAA,KAAAmB,oBAAA,GAAA;;;;IAWA,IAAK,CAAA,KAAAC,oBAAA,GAAA;;;;IAGL,IAAQ,CAAA,KAAAC,kBAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;QArCL,IAAM,CAAA;MAAA;;;;;QACN,IAAM,CAAA;MAAA;;;;;QACP,IAAM,CAAA;MAAA;AACK,WAAA,SAAA,gBAAA,cAAc,CAAC;;;;;;QAEX,IAAc,CAAA;MAAA;yCACb,CAAC;;;;;;;QAMhB,IAAM,CAAA;MAAA;;;;;QACN,IAAM,CAAA;MAAA;;;;;QACP,IAAM,CAAA;MAAA;oCACK,WAAW;;;;;;QAEP,IAAc,CAAA;MAAA;;;;;;QAEd,IAAM,CAAA;MAAA;;;;;QAAqB,IAAc,CAAA;MAAA;;;;;;;yBAtBxC,IAAI;0BAAU,IAAI;;;;;;;;;;;;AADjD,aA+CK,QAAA,MAAA,MAAA;AA9CD,aAwBK,MAAA,GAAA;AAvBD,aAUC,KAAA,OAAA;AACD,aAWC,KAAA,OAAA;;;;;AAaL,aASK,MAAA,IAAA;;;;;;;;;;;;;;;UArCqBJ,KAAc,CAAA;QAAA;;;;;;;;UAYdA,KAAc,CAAA;QAAA;;;;;;;;UAEaA,KAAc,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;QAI9DA,KAAI,CAAA;;AAAA,kBAAA,EAAAA,MAAA,KAAA;;;QAWAA,KAAK,CAAA;;AAAA,kBAAA,EAAAA,MAAA,KAAA;;;QAGLA,KAAQ,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AApFb,IAAA,OAAO;AACP,IAAA,cAAc;AAiCT,SAAA,gBAAgB,MAAU;AACzB,QAAA,WAAW,KAAK,QAAO,IAAK,KAAK,IAAG;AACtC,MAAA,WAAW,GAAC;eACD,KAAK,QAAQ,EAAE,YAAW,EAAG,MAAM,IAAI,EAAE;;SAEjD;;;;;QApDA,OAAI,CAAA,EAAA,IAAA;QAOV,OAAO,KAAK,MAAM,SAAAe,WAAU,KAAI,IAAI;MACrC;MACA;MACA;MAKA,SAAS,OAAO;MAChB,SAAS,SAAS;AAClB,MAAA,gBAAgB,QAAQ,IAAI,KAAK,KAAK,QAAM,EAC5C,UAAU,KACV,QAAQ,SAAQ,CAAA;;AAsBpB,YAAS,MAAA;QACD,OAAK;AACL,oBAAc,KAAK;;;;;;;;;;AArB1B;YACO,OAAK;AACL,wBAAc,KAAK;;YAGnB,KAAG;0BACH,WAAQ,IAAO,KAAK,GAAG,CAAA;AAEvB,uBAAA,IAAA,QAAQ;;8BACJ,YAAS,IAAO,KAAK,QAAQ,EAAE,QAAO,IAAK,KAAK,IAAG,CAAA;AAC/C,kBAAA,aAAa,GAAC;AACd,8BAAc,KAAK;AACnB,8BAAc,IAAI,GAAI;AACtB,6BAAA,IAAAA,WAAU,KAAK;;;YAEpB;;;;;;;AAzBV,mBAAA,GAAE,WAAWA,QAAO;;;;;;;;;;;;;;;;;;;;;;;;ACiBlB,SAAS,KAAK,MAAM,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS5B,SAAM,IAAK,CAAA,GAAI;AAC/E,QAAM,IAAI,CAAC,iBAAiB,IAAI,EAAE;AAClC,SAAO;IACN;IACA;IACA;IACA,KAAK,CAACJ,OAAM,YAAYA,KAAI,CAAC;EAC/B;AACA;AAUO,SAAS,IACf,MACA,EAAE,QAAQ,GAAG,WAAW,KAAK,SAAS,UAAU,GAAAV,KAAI,GAAG,IAAI,GAAG,UAAU,EAAC,IAAK,CAAA,GAC7E;AACD,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,iBAAiB,CAAC,MAAM;AAC9B,QAAM,YAAY,MAAM,cAAc,SAAS,KAAK,MAAM;AAC1D,QAAM,KAAK,kBAAkB,IAAI;AACjC,QAAM,CAAC,QAAQ,KAAK,IAAI,eAAeA,EAAC;AACxC,QAAM,CAAC,QAAQ,KAAK,IAAI,eAAe,CAAC;AACxC,SAAO;IACN;IACA;IACA;IACA,KAAK,CAACU,IAAGiC,OAAM;gBACD,SAAS,eAAe,IAAIjC,MAAK,MAAM,GAAG,KAAK,MAAM,IAAIA,MAAK,MAAM,GAAG,KAAK;cAC9E,iBAAiB,KAAKiC,EAAC;EACrC;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChDI,aAEK,QAAA,KAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAFmC,UAAU;YAAK;;cAAA,IAAC,CAAA;;YAAE;;cAAA,IAAC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAD1DhB,KAAU,CAAA;;AAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAlBA,MAAA,EAAA,YAA6C,OAAS,IAAA;AAE1D,QAAA,EAAA,WAAU,IAAI;QAEf,aAAU,CAAI,OAAO,KAAK;AAG1B,QAAA,cAAe,CAAAiB,eAA8B;WACxCA,eAAc,SAASA,eAAc,QAAQ,MAAG;;;;;;;;;;;AAG1D;;SAAG5C,IAAG,CAAC,IAAI,YACN,WAAW,SAAS,SAAS,KACxB,YAAY,SAAS,GAAG,CAAC,KACzB,GAAG,YAAY,SAAS,CAAA,IAC5B,CAAA,GAAG,CAAC;;;;;;;;;;;;;;;;AC6OP,MAAA;;IAAA,IAAG,CAAA,EAAA,WAAY,EAAA,SAAS,aAAY,CAAA,IAAA;;;;;;;;;AAAxC,aAA8C,QAAAS,IAAA,MAAA;;;;AAA1C,UAAA;MAAA,MAAA,eAAA;MAAAkB,KAAG,CAAA,EAAA,WAAY,EAAA,SAAS,aAAY,CAAA,IAAA;AAAA,iBAAA,KAAA,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MAlDnCA,KAAK,CAAA;MAAKA,KAAK,CAAA,EAAC;;AAAY,aAAA;AASvB;;MAAAA,KAAU,CAAA;MAAAA,KAAM,CAAA,EAAA;MAAeA,KAAO,CAAA;;AAAA,aAAA;AAStC;;MAAAA,KAAK,CAAA;MAAKA,KAAK,CAAA,EAAC;MAAmBA,KAAO,CAAA;MAAIA,KAAa,CAAA;;AAAA,aAAA;AAW3D;;MAAAA,KAAK,CAAA;MAAKA,KAAK,CAAA,EAAC;MAAoBA,KAAO,CAAA;MAAIA,KAAa,CAAA;;AAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAc1D;;UAAO,IAAE,CAAA,EAAC,kBAAgB;YACtB,SAAS;;;;;;;;;;;;;;;;;;;UADb;;YAAOA,KAAE,CAAA,EAAC,kBAAgB;cACtB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAdE,IAAoB,CAAA;;;;;;;;;;;;;;;;;;;QAApBA,KAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAXpB,IAAoB,CAAA;;;;;;;;;;;;;;;;;;;QAApBA,KAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QATpB,IAAoB,CAAA;;;;;;;;;;;;;;;;;;;QAApBA,KAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QATpB,IAAoB,CAAA;;;;;;;;;;;;;;;;;;;QAApBA,KAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;MAiC1B;;QAAA,IAAc,CAAA,EAAC;;;;QAChB,IAAO,CAAA;;;;QACD,IAAa,CAAA;;MACpB;;QAAA,IAAG,CAAA,EAAA,wBAAyB,EAAA,SAAS,oBAAmB,CAAA;;;;;;;IALpD,IAAgB,EAAA;EAAA;;;;IAChB,IAAa,EAAA;EAAA;;;;;;;;;;;AACf,UAAA;MAAA;AAAA,2BAAA;QAAAA,KAAc,CAAA,EAAC;;;;QAChBA,KAAO,CAAA;;;;QACDA,KAAa,CAAA;AACpB,UAAA;MAAA;AAAA,2BAAA;QAAAA,KAAG,CAAA,EAAA,wBAAyB,EAAA,SAAS,oBAAmB,CAAA;;;;;;;;;;;;;;;;;;;;;;;MAdtD;;QAAA,IAAc,CAAA,EAAC;;;;QAChB,IAAO,CAAA;;;;QACD,IAAa,CAAA;;MACpB;;QAAA,IAAG,CAAA,EAAA,uBAAwB,EAAA,SAAS,qBAAoB,CAAA;;;;;;;IALpD,IAAgB,EAAA;EAAA;;;;IAChB,IAAa,EAAA;EAAA;;;;;;;;;;;AACf,UAAA;MAAA;AAAA,2BAAA;QAAAA,KAAc,CAAA,EAAC;;;;QAChBA,KAAO,CAAA;;;;QACDA,KAAa,CAAA;AACpB,UAAA;MAAA;AAAA,2BAAA;QAAAA,KAAG,CAAA,EAAA,uBAAwB,EAAA,SAAS,qBAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;QAZvD,IAAO,CAAA;;MACR;;QAAA,IAAG,CAAA,EAAA,2BAA4B,EAAA,SAAS,sBAAqB,CAAA;;;;;;;IAHzD,IAAW,EAAA;EAAA;;;;IACX,IAAc,EAAA;EAAA;;;;;;;;;;;;;;QACjBA,KAAO,CAAA;AACR,UAAA;MAAA;AAAA,2BAAA;QAAAA,KAAG,CAAA,EAAA,2BAA4B,EAAA,SAAS,sBAAqB,CAAA;;;;;;;;;;;;;;;;;;;;;;;MAV3D;;QAAA,IAAa,CAAA,EAAC;;MAChB;;QAAA,IAAG,CAAA,EAAA,uBAAwB,EAAA,SAAS,kBAAiB,CAAA;;;;;;;IAHjD,IAAY,EAAA;EAAA;;;;IACZ,IAAM,EAAA;EAAA;;;;;;;;;;;AACR,UAAA;MAAA;AAAA,uBAAA;QAAAA,KAAa,CAAA,EAAC;AAChB,UAAA;MAAA;AAAA,uBAAA;QAAAA,KAAG,CAAA,EAAA,uBAAwB,EAAA,SAAS,kBAAiB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAPvE;;MAAAA,KAAM,CAAA;MAAIA,KAAa,CAAA;;AAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAxLjB,GAAAjB,GAAC,IAAI,WAAqB,MAAM;;AAEnC,MAAA,YAAY;AAEV,QAAA4B,YAAW,sBAAqB;MAKjC;YAAAO,QAAK;AACN,IAAAA,OAAa,MAAA,IAAA;AACb,IAAAA,OAAmC,iBAAA,IAAA;AACnC,IAAAA,OAA2B,aAAA,IAAA;AAC3B,IAAAA,OAAqC,kBAAA,IAAA;AACrC,IAAAA,OAA6B,cAAA,IAAA;EAL5B,GAAA,UAAA,QAAK,CAAA,EAAA;QAQJ,QAA+C,QAChD,CAAA,cAAc,aAAa,GAAA,CAAA,CAC1B,iBAAiB,gBAAgB,MAAA;AAC1B,QAAA,CAAA,mBAAmB,iBAAiB,YAAY,QAAS;aACnD;;AAEP,QAAA,gBAAgB,OAAK;AACd,aAAA,gBAAgB;;AAEpB,WAAA,gBAAgB,OAAO,KAAM,CAAAlC,OAAMA,GAAE,OAAO,iBAAiB,OAAO;;QAI7E,SAA0C,QAC3C,CAAA,OAAO,YAAY,GAAA,CAAA,CAClB,eAAe,eAAe,MAAA;SACvB,mBAAmB,kBAAkB,QAAS;aACxC;;WAEJ,gBAAgB,UAAU,aAAa;;;QAIhD,eAAgE,QACjE,CAAA,cAAc,aAAa,GAAA,CAAA,CAC1B,iBAAiB,gBAAgB,MAAA;AAC1B,QAAA,CAAA,mBAAmB,iBAAiB,sBAAsB,QAAS;aAC7D;;AAEJ,WAAA,gBAAgB,cAAc,iBAAiB,iBAAiB;;;MAI3E,SAAsC,QACrC,CAAA,cAAc,YAAY,GAAA,CAAA,CACzB,iBAAiB,oBAAoB,MAAA;AAC9B,QAAA,CAAA,mBAAe,CAAK,sBAAoB;;;QAIzC,qBAAqB,OAAO,iBAAe;AACpC,aAAA,gBAAgB,OAAO,OAAQ,CAAAmC,WAAK;AAElC,eAAA,CAAA,qBAAqB,OAAO,mBAC7B,qBAAqB,OAAO,gBAAgB,SAAS,OAAOA,OAAM,EAAE,CAAA;;;AAIzE,WAAA,gBAAgB;;;AAIzB,QAAA,0BAA0B,aAAa,UAAW,oBAAc;QAC9D,gBAAc;;;QAEd,OAAO,WAAW,GAAG,mBAAiB;UAClC,SAAS,eAAe;UACxB,SAAS;;;;AAGT,UAAA,eAAe,OAAK;AACpB,wBAAA,eAAA,eAAe,UAAU,eAAe,MAAM,IAAE,cAAA;;AAGhD,UAAA,eAAe,OAAO,WAAW,GAAC;uCAClC,eAAe,UAAU,eAAe,OAAO,CAAC,EAAE,IAAE,cAAA;;AAGpD,UAAA,eAAe,iBAAe;AAC9B,wBAAA,eAAA,eAAe,kBAAkB,eAAe,iBAAe,cAAA;;AAG/D,UAAA,eAAe,cAAc,WAAW,GAAC;uCACzC,eAAe,oBAAoB,GAAC,cAAA;;UAGpC,eAAe,sBAAsB,QAAS;AAC9C,wBAAA,eAAA,eAAe,oBAAoB,eAAe,mBAAiB,cAAA;;;;AAK/E,UAAO,MAAA;2BACH,OAAO,QAAQ,GAAG,eAAa,EAAG,SAAS,QAAO,CAAA,GAAA,MAAA;;AAGtD,YAAU,uBAAuB;QAE3B,OAAO,QACR,CAAA,eAAe,YAAY,GAAA,CAAA,CAC1B,kBAAkB,oBAAoB,MAAA;SAC/B,sBAAoB;AACd,aAAA,MAAM;;YAIb,qBACA,yBACA,0BACA,gBAAe,IACf,qBAAqB;SAEpB,iBAAiB,WAAW,mBAAmB,gBAAgB,WAAW,GAAC;AAC5E,sBAAA,eAAA,eAAe,UAAU,gBAAgB,CAAC,GAAA,cAAA;AACnC,aAAA,MAAM;IACL,WAAA,CAAA,iBAAiB,WAAW,iBAAiB,cAAc,OAAK;AACxE,sBAAA,eAAA,eAAe,UAAU,+CAAe,MAAM,IAAE,cAAA;AACzC,aAAA,MAAM;gBACL,iBAAiB,WAAW,qBAAmB;AAChD,aAAA,MAAM;gBACL,iBAAiB,mBAAmB,0BAAwB;AAC7D,aAAA,MAAM;gBACL,iBAAiB,mBAAmB,yBAAuB;AAC5D,aAAA,MAAM;;AAIjB,aAAQ;;;AAIV,QAAA,cAAe,OAAC;AAClB,oBAAA,eAAA,eAAe,UAAU,EAAE,QAAM,cAAA;AACjC,oBAAA,YAAA,cAAc,eAAa,WAAA;AAC3B,oBAAA,qBAAA,uBAAuB,OAAK,oBAAA;;QAE1B,gBAAa,MAAA;mCACf,eAAe,UAAU,QAAS,cAAA;AAClC,oBAAA,YAAA,cAAc,gBAAc,WAAA;AAC5B,oBAAA,qBAAA,uBAAuB,OAAK,oBAAA;;AAG1B,QAAA,mBAAoB,OAAC;AACvB,oBAAA,eAAA,eAAe,kBAAkB,EAAE,QAAM,cAAA;AACzC,oBAAA,YAAA,cAAc,QAAS,WAAA;AACvB,oBAAA,qBAAA,uBAAuB,OAAK,oBAAA;;AAO1B,QAAA,eAAgB,OAAC;AACnB,oBAAA,YAAA,cAAc,gBAAc,WAAA;AAC5B,oBAAA,eAAA,eAAe,oBAAoB,EAAE,QAAM,cAAA;AAC3C,oBAAA,qBAAA,uBAAuB,OAAK,oBAAA;;QAE1B,iBAAc,MAAA;mCAChB,eAAe,oBAAoB,QAAS,cAAA;AAC5C,oBAAA,YAAA,cAAc,QAAS,WAAA;AACvB,oBAAA,qBAAA,uBAAuB,OAAK,oBAAA;;QAG1B,WAAQ,MAAA;SACL,WAAS;AACV,kBAAY;AACZ,MAAAR,UAAS,YAAY,cAAc;AACnC,iBAAW,IAAI,MAAS;AACxB,oBAAc,IAAI,KAAK;;;QAIzBS,UAAM,MAAA;AACR,IAAAT,UAAS,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1MzB,aAEK,QAAA,KAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QCeO,SAAS;QACT;;UAAO,IAAE,CAAA,EAAC,WAAY,EAAA,SAAS,UAAS,CAAA;;QACxC;;UAAO,IAAA,CAAA;;QACP,MAAM;;;;;;;QAMN,SAAS;QACT;;UAAO,IAAE,CAAA,EAAC,UAAW,EAAA,SAAS,SAAQ,CAAA;;QACtC;;UAAO,IAAA,CAAA;;QACP,MAAM;QACN,WAAW;;;;;;;;;;;;;;;;;;;;;UAbX,SAAS;UACT;;YAAOX,KAAE,CAAA,EAAC,WAAY,EAAA,SAAS,UAAS,CAAA;;UACxC;;YAAOA,KAAA,CAAA;;UACP,MAAM;;;;;;;UAMN,SAAS;UACT;;YAAOA,KAAE,CAAA,EAAC,UAAW,EAAA,SAAS,SAAQ,CAAA;;UACtC;;YAAOA,KAAA,CAAA;;UACP,MAAM;UACN,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAxBZ,GAAAjB,GAAC,IAAI,WAAqB,MAAM;;AAEjC,QAAA4B,YAAW,sBAAqB;AAWf,QAAA,OAAA,MAAAA,UAAS,QAAQ;AASjB,QAAA,SAAA,MAAAA,UAAS,UAAU;;;;;;;;;;;;;;;ACnBpB,MAAA;;IAAA,IAAI,CAAA,EAAC,QAAK;;;;;AACV,MAAA;;IAAA,IAAI,CAAA,EAAC,QAAK;;;;;;;;;;;;;;;;AAFhC,aAGK,QAAA,KAAA,MAAA;AAFD,aAAgC,KAAA,EAAA;;;AAChC,aAAgC,KAAA,EAAA;;;;AADd,UAAA;MAAA,KAAA,cAAA;MAAAX,KAAI,CAAA,EAAC,QAAK;AAAA,iBAAA,IAAA,QAAA;AACV,UAAA;MAAA,KAAA,cAAA;MAAAA,KAAI,CAAA,EAAC,QAAK;AAAA,iBAAA,IAAA,QAAA;;;;;;;;;;;;;IAH/B,IAAI,CAAA,KAAAI,kBAAA,GAAA;;;;;;;;;;;;;;;;QAAJJ,KAAI,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;QANM,OAAI;IACX,OAAO;IACP,OAAO;;;;;;;;;;;;;;;;;;;ACSV,MAAA;;IAAA,IAAG,CAAA,EAAA,SAAU,EAAA,SAAS,QAAO,CAAA,IAAA;;;;;;;;;;;;AADlC,aAEQ,QAAA,QAAA,MAAA;;;;;;;;;;;;;AADH,UAAA;MAAA,KAAA,eAAA;MAAAA,KAAG,CAAA,EAAA,SAAU,EAAA,SAAS,QAAO,CAAA,IAAA;AAAA,iBAAA,KAAA,SAAA;;;;;;;;;;;;;;;UAPvB,GAAAjB,GAAC,IAAI,WAAqB,MAAM;;AACjC,QAAA4B,YAAW,sBAAqB;AAKlB,QAAA,gBAAA,MAAAA,UAAS,UAAU;;;;;;;;;;;;;;;;;ICMvB,IAAI,CAAA;IAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IADf,IAAI,CAAA,KAAAP,kBAAA,GAAA;;;;;;;;;;MAGF,IAAK,CAAA,CAAA;;;QAJO,IAAO,CAAA;MAAA,IAAA,gBAAA;;;;;;;;;;;;;;;;;;;;;;AAA9B,aAKG,QAAA,GAAA,MAAA;;;;AADC,aAAmB,GAAA,IAAA;;;;;;QAHdJ,KAAI,CAAA;;AAAA,iBAAA,EAAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;QAbE,KAOV,IAAA;AAEI,MAAA,EAAA,SAAS,MAAM,MAAM,OAAO,MAAM,QAAQ,UAAU,WAAU,IAAI;;;;;;;;;;;;;ACZ3E,IAAA,oBAAe;EACX,GAAG;EACH,GAAG;EACH,GAAG;EACH,GAAG;;ACJP,IAAA,OAAe;EACX,aAAa,KAAK;EAClB,gBAAgB,KAAK;EACrB,gBAAgB,KAAK;EACrB,YAAY,KAAK;;ACFP,IAAO,aAAP,MAAiB;EAG3B,YAAY,MAAS;AACjB,SAAK,OAAOqB,KAAK;AACjB,SAAK,OAAO;;EAEhB,YAAS;AACL,WAAO,KAAK,KAAK;;EAErB,MAAM,QAAgD;AAClD,aAAS/C,KAAI,GAAGA,KAAI,KAAK,KAAK,QAAQA,MAAK;AAEvC,aAAO,IAAI,KAAK,KAAK,WAAWA,EAAC,GAAG,CAAC;IACxC;;AAER;AClBa,IAAO,cAAP,MAAkB;EAG5B,cAAA;AACI,SAAK,SAAS,CAAA;AACd,SAAK,SAAS;;EAElB,IAAI,OAAK;AACL,UAAM,WAAW,KAAK,MAAM,QAAQ,CAAC;AACrC,YAAS,KAAK,OAAO,QAAQ,MAAO,IAAK,QAAQ,IAAO,MAAM;;EAGlE,IAAI,KAAK,QAAM;AACX,aAASA,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAC7B,WAAK,QAAS,QAAS,SAASA,KAAI,IAAM,MAAM,CAAC;IACpD;;EAGL,kBAAe;AACX,WAAO,KAAK;;EAGhB,OAAO,KAAG;AACN,UAAM,WAAW,KAAK,MAAM,KAAK,SAAS,CAAC;AAC3C,QAAI,KAAK,OAAO,UAAU,UAAU;AAChC,WAAK,OAAO,KAAK,CAAC;IACrB;AAED,QAAI,KAAK;AACL,WAAK,OAAO,QAAQ,KAAK,QAAS,KAAK,SAAS;IACnD;AAED,SAAK;;AAEZ;AClCD,IAAM,SAAS;EACX,MAAM,SAAUiB,IAAC;AACb,QAAIA,KAAI,GAAG;AACP,YAAM,IAAI,MAAM,UAAUA,KAAI,GAAG;IACpC;AAED,WAAO,OAAO,UAAUA,EAAC;;EAG7B,MAAM,SAAUA,IAAC;AACb,WAAOA,KAAI,GAAG;AACV,MAAAA,MAAK;IACR;AAED,WAAOA,MAAK,KAAK;AACb,MAAAA,MAAK;IACR;AAED,WAAO,OAAO,UAAUA,EAAC;;EAG7B,WAAW,IAAI,MAAM,GAAG;EAExB,WAAW,IAAI,MAAM,GAAG;;AAG5B,SAASjB,KAAI,GAAGA,KAAI,GAAGA,MAAK;AACxB,SAAO,UAAUA,EAAC,IAAI,KAAKA;AAC9B;AACD,SAASA,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC1B,SAAO,UAAUA,EAAC,IACd,OAAO,UAAUA,KAAI,CAAC,IACtB,OAAO,UAAUA,KAAI,CAAC,IACtB,OAAO,UAAUA,KAAI,CAAC,IACtB,OAAO,UAAUA,KAAI,CAAC;AAC7B;AACD,SAASA,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC1B,SAAO,UAAU,OAAO,UAAUA,EAAC,CAAC,IAAIA;AAC3C;ACpCa,IAAO,eAAP,MAAO,cAAY;EAE7B,YAAY,KAAK,OAAK;AAClB,QAAI,IAAI,UAAU,QAAW;AACzB,YAAM,IAAI,MAAM,IAAI,SAAS,MAAM,KAAK;IAC3C;AAED,QAAI,SAAS;AAEb,WAAO,SAAS,IAAI,UAAU,IAAI,MAAM,KAAK,GAAG;AAC5C;IACH;AAED,SAAK,MAAM,IAAI,MAAM,IAAI,SAAS,SAAS,KAAK;AAChD,aAASA,KAAI,GAAGA,KAAI,IAAI,SAAS,QAAQA,MAAK;AAC1C,WAAK,IAAIA,EAAC,IAAI,IAAIA,KAAI,MAAM;IAC/B;;EAGL,IAAI,OAAsB;AACtB,WAAO,KAAK,IAAI,KAAK;;EAGzB,YAAS;AACL,WAAO,KAAK,IAAI;;EAGpB,SAAS,GAAwD;AAC7D,UAAM,MAAM,IAAI,MAAM,KAAK,UAAS,IAAK,EAAE,UAAS,IAAK,CAAC;AAE1D,aAASA,KAAI,GAAGA,KAAI,KAAK,UAAS,GAAIA,MAAK;AACvC,eAASgD,KAAI,GAAGA,KAAI,EAAE,UAAS,GAAIA,MAAK;AACpC,YAAIhD,KAAIgD,EAAC,KAAKC,OAAK,KAAKA,OAAK,KAAK,KAAK,IAAIjD,EAAC,CAAC,IAAIiD,OAAK,KAAK,EAAE,IAAID,EAAC,CAAC,CAAC;MACvE;IACJ;AAED,WAAO,IAAI,cAAa,KAAK,CAAC;;EAGlC,IAAI,GAAwD;AACxD,QAAI,KAAK,UAAS,IAAK,EAAE,UAAS,IAAK,GAAG;AACtC,aAAO;IACV;AAED,UAAM,QAAQC,OAAK,KAAK,KAAK,IAAI,CAAC,CAAC,IAAIA,OAAK,KAAK,EAAE,IAAI,CAAC,CAAC;AAEzD,UAAM,MAAM,IAAI,MAAM,KAAK,UAAS,CAAE;AAEtC,aAASjD,KAAI,GAAGA,KAAI,KAAK,UAAS,GAAIA,MAAK;AACvC,UAAIA,EAAC,IAAI,KAAK,IAAIA,EAAC;IACtB;AAED,aAASA,KAAI,GAAGA,KAAI,EAAE,UAAS,GAAIA,MAAK;AACpC,UAAIA,EAAC,KAAKiD,OAAK,KAAKA,OAAK,KAAK,EAAE,IAAIjD,EAAC,CAAC,IAAI,KAAK;IAClD;AAGD,WAAO,IAAI,cAAa,KAAK,CAAC,EAAE,IAAI,CAAC;;AAE5C;AC1DD,IAAqB,YAArB,MAAqB,WAAS;EAI1B,YAAY,YAAY,WAAS;AAC7B,SAAK,aAAa;AAClB,SAAK,YAAY;;EA0PrB,OAAO,YAAY,YAAY,mBAAiB;AAC5C,UAAM,UAAU,WAAU,gBAAgB,YAAY,iBAAiB;AAEvE,QAAI,WAAW,QAAW;AACtB,YAAM,IAAI,MACN,+BACI,aACA,wBACA,iBAAiB;IAE5B;AAED,UAAM,SAAS,QAAQ,SAAS;AAEhC,UAAM,OAAc,CAAA;AAEpB,aAASA,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAC7B,YAAM,QAAQ,QAAQA,KAAI,IAAI,CAAC;AAC/B,YAAM,aAAa,QAAQA,KAAI,IAAI,CAAC;AACpC,YAAM,YAAY,QAAQA,KAAI,IAAI,CAAC;AAEnC,eAASgD,KAAI,GAAGA,KAAI,OAAOA,MAAK;AAC5B,aAAK,KAAK,IAAI,WAAU,YAAY,SAAS,CAAC;MACjD;IACJ;AAED,WAAO;;EAGX,OAAO,gBAAgB,YAAY,mBAAiB;AAChD,YAAQ,mBAAiB;MACrB,KAAKE,kBAAI;AACL,eAAO,WAAU,gBAAgB,aAAa,KAAK,IAAI,CAAC;MAC5D,KAAKA,kBAAI;AACL,eAAO,WAAU,gBAAgB,aAAa,KAAK,IAAI,CAAC;MAC5D,KAAKA,kBAAI;AACL,eAAO,WAAU,gBAAgB,aAAa,KAAK,IAAI,CAAC;MAC5D,KAAKA,kBAAI;AACL,eAAO,WAAU,gBAAgB,aAAa,KAAK,IAAI,CAAC;MAC5D;AACI,eAAO;IACd;;;AAhSE,UAAA,iBAAiB;;;;;;EAOpB,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,CAAC;;EAGT,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,EAAE;;EAGV,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,EAAE;;EAGV,CAAC,GAAG,KAAK,EAAE;EACX,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,CAAC;;EAGT,CAAC,GAAG,KAAK,GAAG;EACZ,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;;EAGrB,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,EAAE;;EAGV,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,EAAE;EACV,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;;EAGrB,CAAC,GAAG,KAAK,EAAE;EACX,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;;EAGrB,CAAC,GAAG,KAAK,GAAG;EACZ,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;;EAGrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;;EAGrB,CAAC,GAAG,KAAK,EAAE;EACX,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;;EAGrB,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAE;EACvB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;;EAGrB,CAAC,GAAG,KAAK,GAAG;EACZ,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;;EAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;EACzB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;;EAGtB,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAE;EACvB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,IAAI,IAAI,EAAE;;EAGX,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAE;EACvB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;EACzB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;EACzB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;EACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;EACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;EACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;EACzB,CAAC,IAAI,IAAI,EAAE;EACX,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;;EAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;EACzB,CAAC,IAAI,IAAI,EAAE;EACX,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,EAAE;;EAGX,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;EACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;EACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;;EAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;EACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;EAC1B,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;;EAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;EACzB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;EAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;EACzB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;EAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;EAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,IAAI,KAAK,GAAG;EACb,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;EAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;EAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;;EAGtB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;EAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;EAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGtB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;EAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;EAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;EAC1B,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;EACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;EAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;EAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;;AC5PxB,IAAM,gBAAgB;EACzB,YAAY;EACZ,YAAY;EACZ,YAAY;EACZ,YAAY;EACZ,YAAY;EACZ,YAAY;EACZ,YAAY;EACZ,YAAY;;AAGhB,IAAM,SAAS;EACX,wBAAwB;IACpB,CAAA;IACA,CAAC,GAAG,EAAE;IACN,CAAC,GAAG,EAAE;IACN,CAAC,GAAG,EAAE;IACN,CAAC,GAAG,EAAE;IACN,CAAC,GAAG,EAAE;IACN,CAAC,GAAG,IAAI,EAAE;IACV,CAAC,GAAG,IAAI,EAAE;IACV,CAAC,GAAG,IAAI,EAAE;IACV,CAAC,GAAG,IAAI,EAAE;IACV,CAAC,GAAG,IAAI,EAAE;IACV,CAAC,GAAG,IAAI,EAAE;IACV,CAAC,GAAG,IAAI,EAAE;IACV,CAAC,GAAG,IAAI,IAAI,EAAE;IACd,CAAC,GAAG,IAAI,IAAI,EAAE;IACd,CAAC,GAAG,IAAI,IAAI,EAAE;IACd,CAAC,GAAG,IAAI,IAAI,EAAE;IACd,CAAC,GAAG,IAAI,IAAI,EAAE;IACd,CAAC,GAAG,IAAI,IAAI,EAAE;IACd,CAAC,GAAG,IAAI,IAAI,EAAE;IACd,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE;IAClB,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE;IAClB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;IACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;IACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;IACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;IACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;IACnB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG;IACvB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;IACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;IACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;IACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;IACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;IACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;IACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;IAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;IAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;IAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;IAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;IAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;EAChC;EAED,KAAM,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;EAC9E,KAAM,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;EAC3F,UAAW,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK;EAE/D,gBAAgB,SAAU,MAAI;AAC1B,QAAInC,KAAI,QAAQ;AAChB,WAAO,OAAO,YAAYA,EAAC,IAAI,OAAO,YAAY,OAAO,GAAG,KAAK,GAAG;AAChE,MAAAA,MAAK,OAAO,OAAQ,OAAO,YAAYA,EAAC,IAAI,OAAO,YAAY,OAAO,GAAG;IAC5E;AACD,YAAS,QAAQ,KAAMA,MAAK,OAAO;;EAGvC,kBAAkB,SAAU,MAAI;AAC5B,QAAIA,KAAI,QAAQ;AAChB,WAAO,OAAO,YAAYA,EAAC,IAAI,OAAO,YAAY,OAAO,GAAG,KAAK,GAAG;AAChE,MAAAA,MAAK,OAAO,OAAQ,OAAO,YAAYA,EAAC,IAAI,OAAO,YAAY,OAAO,GAAG;IAC5E;AACD,WAAQ,QAAQ,KAAMA;;EAG1B,aAAa,SAAU,MAAI;AACvB,QAAI,QAAQ;AAEZ,WAAO,QAAQ,GAAG;AACd;AACA,gBAAU;IACb;AAED,WAAO;;EAGX,oBAAoB,SAAU,YAAU;AACpC,WAAO,OAAO,uBAAuB,aAAa,CAAC;;EAGvD,SAAS,SAAU,aAAaf,IAAGgD,IAAC;AAChC,YAAQ,aAAW;MACf,KAAK,cAAc;AACf,gBAAQhD,KAAIgD,MAAK,KAAK;MAC1B,KAAK,cAAc;AACf,eAAOhD,KAAI,KAAK;MACpB,KAAK,cAAc;AACf,eAAOgD,KAAI,KAAK;MACpB,KAAK,cAAc;AACf,gBAAQhD,KAAIgD,MAAK,KAAK;MAC1B,KAAK,cAAc;AACf,gBAAQ,KAAK,MAAMhD,KAAI,CAAC,IAAI,KAAK,MAAMgD,KAAI,CAAC,KAAK,KAAK;MAC1D,KAAK,cAAc;AACf,eAAShD,KAAIgD,KAAK,IAAOhD,KAAIgD,KAAK,KAAM;MAC5C,KAAK,cAAc;AACf,gBAAUhD,KAAIgD,KAAK,IAAOhD,KAAIgD,KAAK,KAAM,KAAK;MAClD,KAAK,cAAc;AACf,gBAAUhD,KAAIgD,KAAK,KAAOhD,KAAIgD,MAAK,KAAM,KAAK;MAElD;AACI,cAAM,IAAI,MAAM,qBAAqB,WAAW;IACvD;;EAGL,2BAA2B,SAAU,oBAAkB;AACnD,QAAI,IAAI,IAAIG,aAAW,CAAC,CAAC,GAAG,CAAC;AAE7B,aAASnD,KAAI,GAAGA,KAAI,oBAAoBA,MAAK;AACzC,UAAI,EAAE,SAAS,IAAImD,aAAW,CAAC,GAAGF,OAAK,KAAKjD,EAAC,CAAC,GAAG,CAAC,CAAC;IACtD;AAED,WAAO;;EAGX,iBAAiB,SAAU,MAAM,MAAI;AACjC,QAAI,KAAK,QAAQ,OAAO,IAAI;AAGxB,cAAQ,MAAI;QACR,KAAK,KAAK;AACN,iBAAO;QACX,KAAK,KAAK;AACN,iBAAO;QACX,KAAK,KAAK;AACN,iBAAO;QACX,KAAK,KAAK;AACN,iBAAO;QACX;AACI,gBAAM,IAAI,MAAM,UAAU,IAAI;MACrC;IACJ,WAAU,OAAO,IAAI;AAGlB,cAAQ,MAAI;QACR,KAAK,KAAK;AACN,iBAAO;QACX,KAAK,KAAK;AACN,iBAAO;QACX,KAAK,KAAK;AACN,iBAAO;QACX,KAAK,KAAK;AACN,iBAAO;QACX;AACI,gBAAM,IAAI,MAAM,UAAU,IAAI;MACrC;IACJ,WAAU,OAAO,IAAI;AAGlB,cAAQ,MAAI;QACR,KAAK,KAAK;AACN,iBAAO;QACX,KAAK,KAAK;AACN,iBAAO;QACX,KAAK,KAAK;AACN,iBAAO;QACX,KAAK,KAAK;AACN,iBAAO;QACX;AACI,gBAAM,IAAI,MAAM,UAAU,IAAI;MACrC;IACJ,OAAM;AACH,YAAM,IAAI,MAAM,UAAU,IAAI;IACjC;;EAGL,cAAc,SAAU,QAAM;AAC1B,UAAM,cAAc,OAAO,eAAc;AAEzC,QAAI,YAAY;AAIhB,aAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AACxC,eAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AACxC,YAAI,YAAY;AAChB,cAAM,OAAO,OAAO,OAAO,KAAK,GAAG;AAEnC,iBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC1B,cAAI,MAAM,IAAI,KAAK,eAAe,MAAM,GAAG;AACvC;UACH;AAED,mBAASU,KAAI,IAAIA,MAAK,GAAGA,MAAK;AAC1B,gBAAI,MAAMA,KAAI,KAAK,eAAe,MAAMA,IAAG;AACvC;YACH;AAED,gBAAI,KAAK,KAAKA,MAAK,GAAG;AAClB;YACH;AAED,gBAAI,QAAQ,OAAO,OAAO,MAAM,GAAG,MAAMA,EAAC,GAAG;AACzC;YACH;UACJ;QACJ;AAED,YAAI,YAAY,GAAG;AACf,uBAAa,IAAI,YAAY;QAChC;MACJ;IACJ;AAID,aAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO;AAC5C,eAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO;AAC5C,YAAI,QAAQ;AACZ,YAAI,OAAO,OAAO,KAAK,GAAG;AAAG;AAC7B,YAAI,OAAO,OAAO,MAAM,GAAG,GAAG;AAAG;AACjC,YAAI,OAAO,OAAO,KAAK,MAAM,CAAC;AAAG;AACjC,YAAI,OAAO,OAAO,MAAM,GAAG,MAAM,CAAC;AAAG;AACrC,YAAI,SAAS,KAAK,SAAS,GAAG;AAC1B,uBAAa;QAChB;MACJ;IACJ;AAID,aAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AACxC,eAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO;AAC5C,YACI,OAAO,OAAO,KAAK,GAAG,KACtB,CAAC,OAAO,OAAO,KAAK,MAAM,CAAC,KAC3B,OAAO,OAAO,KAAK,MAAM,CAAC,KAC1B,OAAO,OAAO,KAAK,MAAM,CAAC,KAC1B,OAAO,OAAO,KAAK,MAAM,CAAC,KAC1B,CAAC,OAAO,OAAO,KAAK,MAAM,CAAC,KAC3B,OAAO,OAAO,KAAK,MAAM,CAAC,GAC5B;AACE,uBAAa;QAChB;MACJ;IACJ;AAED,aAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AACxC,eAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO;AAC5C,YACI,OAAO,OAAO,KAAK,GAAG,KACtB,CAAC,OAAO,OAAO,MAAM,GAAG,GAAG,KAC3B,OAAO,OAAO,MAAM,GAAG,GAAG,KAC1B,OAAO,OAAO,MAAM,GAAG,GAAG,KAC1B,OAAO,OAAO,MAAM,GAAG,GAAG,KAC1B,CAAC,OAAO,OAAO,MAAM,GAAG,GAAG,KAC3B,OAAO,OAAO,MAAM,GAAG,GAAG,GAC5B;AACE,uBAAa;QAChB;MACJ;IACJ;AAID,QAAI,YAAY;AAEhB,aAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AACxC,eAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AACxC,YAAI,OAAO,OAAO,KAAK,GAAG,GAAG;AACzB;QACH;MACJ;IACJ;AAED,UAAM,QAAQ,KAAK,IAAK,MAAM,YAAa,cAAc,cAAc,EAAE,IAAI;AAC7E,iBAAa,QAAQ;AAErB,WAAO;;;ACnRf,IAAqB,SAArB,MAAqB,QAAM;EAQvB,YAAY,YAAY,mBAAiB;AACrC,SAAK,aAAa;AAClB,SAAK,oBAAoB;AACzB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,WAAW,CAAA;;EAGpB,QAAQ,MAAS;AACb,UAAM,UAAU,IAAI0C,WAAQ,IAAI;AAChC,SAAK,SAAS,KAAK,OAAO;AAC1B,SAAK,YAAY;;EAGrB,OAAO,KAAa,KAAW;AAC3B,QAAI,MAAM,KAAK,KAAK,eAAe,OAAO,MAAM,KAAK,KAAK,eAAe,KAAK;AAC1E,YAAM,IAAI,MAAM,MAAM,MAAM,GAAG;IAClC;AACD,WAAO,KAAK,QAAQ,GAAG,EAAE,GAAG;;EAGhC,iBAAc;AACV,WAAO,KAAK;;EAGhB,OAAI;AAEA,QAAI,KAAK,aAAa,GAAG;AACrB,UAAI,aAAa;AACjB,WAAK,aAAa,GAAG,aAAa,IAAI,cAAc;AAChD,cAAM,WAAWC,UAAQ,YAAY,YAAY,KAAK,iBAAiB;AAEvE,cAAM,SAAS,IAAIC,YAAS;AAC5B,YAAI,iBAAiB;AACrB,iBAAStD,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACtC,4BAAkB,SAASA,EAAC,EAAE;QACjC;AAED,iBAASA,KAAI,GAAGA,KAAI,KAAK,SAAS,QAAQA,MAAK;AAC3C,gBAAM,OAAO,KAAK,SAASA,EAAC;AAC5B,iBAAO,IAAI,KAAK,MAAM,CAAC;AACvB,iBAAO,IAAI,KAAK,UAAS,GAAIuD,OAAK,gBAAgB,KAAK,MAAM,UAAU,CAAC;AACxE,eAAK,MAAM,MAAM;QACpB;AACD,YAAI,OAAO,gBAAe,KAAM,iBAAiB;AAAG;MACvD;AACD,WAAK,aAAa;IACrB;AACD,SAAK,SAAS,OAAO,KAAK,mBAAkB,CAAE;;EAGlD,SAAS,MAAW,aAAgB;AAChC,SAAK,cAAc,KAAK,aAAa,IAAI;AACzC,SAAK,UAAU,IAAI,MAAM,KAAK,WAAW;AAEzC,aAAS,MAAM,GAAG,MAAM,KAAK,aAAa,OAAO;AAC7C,WAAK,QAAQ,GAAG,IAAI,IAAI,MAAM,KAAK,WAAW;AAE9C,eAAS,MAAM,GAAG,MAAM,KAAK,aAAa,OAAO;AAC7C,aAAK,QAAQ,GAAG,EAAE,GAAG,IAAI;MAC5B;IACJ;AAED,SAAK,0BAA0B,GAAG,CAAC;AACnC,SAAK,0BAA0B,KAAK,cAAc,GAAG,CAAC;AACtD,SAAK,0BAA0B,GAAG,KAAK,cAAc,CAAC;AACtD,SAAK,2BAA0B;AAC/B,SAAK,mBAAkB;AACvB,SAAK,cAAc,MAAM,WAAW;AAEpC,QAAI,KAAK,cAAc,GAAG;AACtB,WAAK,gBAAgB,IAAI;IAC5B;AAED,QAAI,KAAK,aAAa,MAAM;AACxB,WAAK,YAAY,QAAO,WACpB,KAAK,YACL,KAAK,mBACL,KAAK,QAAQ;IAEpB;AAED,SAAK,QAAQ,KAAK,WAAW,WAAW;;EAG5C,0BAA0B,KAAa,KAAW;AAC9C,aAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC1B,UAAI,MAAM,KAAK,MAAM,KAAK,eAAe,MAAM;AAAG;AAElD,eAAS7C,KAAI,IAAIA,MAAK,GAAGA,MAAK;AAC1B,YAAI,MAAMA,MAAK,MAAM,KAAK,eAAe,MAAMA;AAAG;AAElD,YACK,KAAK,KAAK,KAAK,MAAMA,MAAK,KAAKA,MAAK,MACpC,KAAKA,MAAKA,MAAK,MAAM,KAAK,KAAK,KAAK,MACpC,KAAK,KAAK,KAAK,KAAK,KAAKA,MAAKA,MAAK,GACtC;AACE,eAAK,QAAQ,MAAM,CAAC,EAAE,MAAMA,EAAC,IAAI;QACpC,OAAM;AACH,eAAK,QAAQ,MAAM,CAAC,EAAE,MAAMA,EAAC,IAAI;QACpC;MACJ;IACJ;;EAGL,qBAAkB;AACd,QAAI,eAAe;AACnB,QAAI,UAAU;AAEd,aAASV,KAAI,GAAGA,KAAI,GAAGA,MAAK;AACxB,WAAK,SAAS,MAAMA,EAAC;AAErB,YAAM,YAAYuD,OAAK,aAAa,IAAI;AAExC,UAAIvD,MAAK,KAAK,eAAe,WAAW;AACpC,uBAAe;AACf,kBAAUA;MACb;IACJ;AAED,WAAO;;EAGX,qBAAkB;AACd,aAAS,IAAI,GAAG,IAAI,KAAK,cAAc,GAAG,KAAK;AAC3C,UAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,MAAM;AAC5B;MACH;AACD,WAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK;IACjC;AAED,aAASU,KAAI,GAAGA,KAAI,KAAK,cAAc,GAAGA,MAAK;AAC3C,UAAI,KAAK,QAAQ,CAAC,EAAEA,EAAC,KAAK,MAAM;AAC5B;MACH;AACD,WAAK,QAAQ,CAAC,EAAEA,EAAC,IAAIA,KAAI,KAAK;IACjC;;EAGL,6BAA0B;AACtB,UAAM,MAAM6C,OAAK,mBAAmB,KAAK,UAAU;AAEnD,aAASvD,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACjC,eAASgD,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACjC,cAAM,MAAM,IAAIhD,EAAC;AACjB,cAAM,MAAM,IAAIgD,EAAC;AAEjB,YAAI,KAAK,QAAQ,GAAG,EAAE,GAAG,KAAK,MAAM;AAChC;QACH;AAED,iBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC1B,mBAAStC,KAAI,IAAIA,MAAK,GAAGA,MAAK;AAC1B,gBAAI,KAAK,MAAM,KAAK,KAAKA,MAAK,MAAMA,MAAK,KAAM,KAAK,KAAKA,MAAK,GAAI;AAC9D,mBAAK,QAAQ,MAAM,CAAC,EAAE,MAAMA,EAAC,IAAI;YACpC,OAAM;AACH,mBAAK,QAAQ,MAAM,CAAC,EAAE,MAAMA,EAAC,IAAI;YACpC;UACJ;QACJ;MACJ;IACJ;;EAGL,gBAAgB,MAAS;AACrB,UAAM,OAAO6C,OAAK,iBAAiB,KAAK,UAAU;AAElD,aAASvD,KAAI,GAAGA,KAAI,IAAIA,MAAK;AACzB,YAAM,MAAM,CAAC,SAAU,QAAQA,KAAK,MAAM;AAC1C,WAAK,QAAQ,KAAK,MAAMA,KAAI,CAAC,CAAC,EAAGA,KAAI,IAAK,KAAK,cAAc,IAAI,CAAC,IAAI;IACzE;AAED,aAASA,KAAI,GAAGA,KAAI,IAAIA,MAAK;AACzB,YAAM,MAAM,CAAC,SAAU,QAAQA,KAAK,MAAM;AAC1C,WAAK,QAASA,KAAI,IAAK,KAAK,cAAc,IAAI,CAAC,EAAE,KAAK,MAAMA,KAAI,CAAC,CAAC,IAAI;IACzE;;EAGL,cAAc,MAAW,aAAmB;AACxC,UAAM,OAAQ,KAAK,qBAAqB,IAAK;AAC7C,UAAM,OAAOuD,OAAK,eAAe,IAAI;AAGrC,aAASvD,KAAI,GAAGA,KAAI,IAAIA,MAAK;AACzB,YAAM,MAAM,CAAC,SAAU,QAAQA,KAAK,MAAM;AAE1C,UAAIA,KAAI,GAAG;AACP,aAAK,QAAQA,EAAC,EAAE,CAAC,IAAI;MACxB,WAAUA,KAAI,GAAG;AACd,aAAK,QAAQA,KAAI,CAAC,EAAE,CAAC,IAAI;MAC5B,OAAM;AACH,aAAK,QAAQ,KAAK,cAAc,KAAKA,EAAC,EAAE,CAAC,IAAI;MAChD;IACJ;AAGD,aAASA,KAAI,GAAGA,KAAI,IAAIA,MAAK;AACzB,YAAM,MAAM,CAAC,SAAU,QAAQA,KAAK,MAAM;AAE1C,UAAIA,KAAI,GAAG;AACP,aAAK,QAAQ,CAAC,EAAE,KAAK,cAAcA,KAAI,CAAC,IAAI;MAC/C,WAAUA,KAAI,GAAG;AACd,aAAK,QAAQ,CAAC,EAAE,KAAKA,KAAI,IAAI,CAAC,IAAI;MACrC,OAAM;AACH,aAAK,QAAQ,CAAC,EAAE,KAAKA,KAAI,CAAC,IAAI;MACjC;IACJ;AAGD,SAAK,QAAQ,KAAK,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC;;EAG7C,QAAQ,MAAsB,aAAgB;AAC1C,QAAI,MAAM;AACV,QAAI,MAAM,KAAK,cAAc;AAC7B,QAAI,WAAW;AACf,QAAI,YAAY;AAEhB,aAAS,MAAM,KAAK,cAAc,GAAG,MAAM,GAAG,OAAO,GAAG;AACpD,UAAI,OAAO;AAAG;AAEd,iBAAS;AACL,iBAASU,KAAI,GAAGA,KAAI,GAAGA,MAAK;AACxB,cAAI,KAAK,QAAQ,GAAG,EAAE,MAAMA,EAAC,KAAK,MAAM;AACpC,gBAAI,OAAO;AAEX,gBAAI,YAAY,KAAK,QAAQ;AACzB,sBAAS,KAAK,SAAS,MAAM,WAAY,MAAM;YAClD;AAED,kBAAM,OAAO6C,OAAK,QAAQ,aAAa,KAAK,MAAM7C,EAAC;AAEnD,gBAAI,MAAM;AACN,qBAAO,CAAC;YACX;AAED,iBAAK,QAAQ,GAAG,EAAE,MAAMA,EAAC,IAAI;AAC7B;AAEA,gBAAI,YAAY,IAAI;AAChB;AACA,yBAAW;YACd;UACJ;QACJ;AAED,eAAO;AAEP,YAAI,MAAM,KAAK,KAAK,eAAe,KAAK;AACpC,iBAAO;AACP,gBAAM,CAAC;AACP;QACH;MACJ;IACJ;;EAML,OAAO,WAAW,YAAiB,mBAAwB,UAAwB;AAC/E,UAAM,WAAW2C,UAAQ,YAAY,YAAY,iBAAiB;AAElE,UAAM,SAAS,IAAIC,YAAS;AAE5B,aAAStD,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACtC,YAAM,OAAO,SAASA,EAAC;AACvB,aAAO,IAAI,KAAK,MAAM,CAAC;AACvB,aAAO,IAAI,KAAK,UAAS,GAAIuD,OAAK,gBAAgB,KAAK,MAAM,UAAU,CAAC;AACxE,WAAK,MAAM,MAAM;IACpB;AAGD,QAAI,iBAAiB;AACrB,aAASvD,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACtC,wBAAkB,SAASA,EAAC,EAAE;IACjC;AAED,QAAI,OAAO,gBAAe,IAAK,iBAAiB,GAAG;AAC/C,YAAM,IAAI,MACN,4BACI,OAAO,gBAAe,IACtB,MACA,iBAAiB,IACjB,GAAG;IAEd;AAGD,QAAI,OAAO,gBAAe,IAAK,KAAK,iBAAiB,GAAG;AACpD,aAAO,IAAI,GAAG,CAAC;IAClB;AAGD,WAAO,OAAO,gBAAe,IAAK,KAAK,GAAG;AACtC,aAAO,OAAO,KAAK;IACtB;AAGD,eAAS;AACL,UAAI,OAAO,gBAAe,KAAM,iBAAiB,GAAG;AAChD;MACH;AACD,aAAO,IAAI,QAAO,MAAM,CAAC;AAEzB,UAAI,OAAO,gBAAe,KAAM,iBAAiB,GAAG;AAChD;MACH;AACD,aAAO,IAAI,QAAO,MAAM,CAAC;IAC5B;AAED,WAAO,QAAO,YAAY,QAAQ,QAAQ;;EAG9C,OAAO,YAAY,QAA4B,UAAwB;AACnE,QAAI,SAAS;AAEb,QAAI,aAAa;AACjB,QAAI,aAAa;AAEjB,UAAM,SAAS,IAAI,MAAM,SAAS,MAAM;AACxC,UAAM,SAAS,IAAI,MAAM,SAAS,MAAM;AAExC,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,YAAM,UAAU,SAAS,CAAC,EAAE;AAC5B,YAAM,UAAU,SAAS,CAAC,EAAE,aAAa;AAEzC,mBAAa,KAAK,IAAI,YAAY,OAAO;AACzC,mBAAa,KAAK,IAAI,YAAY,OAAO;AAEzC,aAAO,CAAC,IAAI,IAAI,MAAM,OAAO;AAE7B,eAASA,KAAI,GAAGA,KAAI,OAAO,CAAC,EAAE,QAAQA,MAAK;AACvC,eAAO,CAAC,EAAEA,EAAC,IAAI,MAAO,OAAO,OAAOA,KAAI,MAAM;MACjD;AACD,gBAAU;AAEV,YAAM,SAASuD,OAAK,0BAA0B,OAAO;AACrD,YAAM,UAAU,IAAIJ,aAAW,OAAO,CAAC,GAAG,OAAO,UAAS,IAAK,CAAC;AAEhE,YAAM,UAAU,QAAQ,IAAI,MAAM;AAClC,aAAO,CAAC,IAAI,IAAI,MAAM,OAAO,UAAS,IAAK,CAAC;AAC5C,eAASnD,KAAI,GAAGA,KAAI,OAAO,CAAC,EAAE,QAAQA,MAAK;AACvC,cAAM,WAAWA,KAAI,QAAQ,UAAS,IAAK,OAAO,CAAC,EAAE;AACrD,eAAO,CAAC,EAAEA,EAAC,IAAI,YAAY,IAAI,QAAQ,IAAI,QAAQ,IAAI;MAC1D;IACJ;AAED,QAAI,iBAAiB;AACrB,aAASA,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACtC,wBAAkB,SAASA,EAAC,EAAE;IACjC;AAED,UAAM,OAAO,IAAI,MAAM,cAAc;AACrC,QAAI,QAAQ;AAEZ,aAASA,KAAI,GAAGA,KAAI,YAAYA,MAAK;AACjC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,YAAIA,KAAI,OAAO,CAAC,EAAE,QAAQ;AACtB,eAAK,OAAO,IAAI,OAAO,CAAC,EAAEA,EAAC;QAC9B;MACJ;IACJ;AAED,aAASA,KAAI,GAAGA,KAAI,YAAYA,MAAK;AACjC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,YAAIA,KAAI,OAAO,CAAC,EAAE,QAAQ;AACtB,eAAK,OAAO,IAAI,OAAO,CAAC,EAAEA,EAAC;QAC9B;MACJ;IACJ;AAED,WAAO;;;AAnHJ,OAAI,OAAG;AACP,OAAI,OAAG;ACnQM,SAAA,SAASG,OAAc,QAA+B,KAAKqD,WAAU,IAAE;AAC3F,MAAI;AACA,UAAM,KAAK,IAAI,OAAOA,UAAS,kBAAkB,KAAK,CAAC;AACvD,UAAM,QAAgB,CAAA;AAEtB,OAAG,QAAQrD,KAAI;AACf,OAAG,KAAI;AAEP,UAAM,OAAO,GAAG;AAChB,UAAMwB,QAAO,KAAK;AAElB,eAAW,CAAC,GAAG,GAAG,KAAK,KAAK,QAAO,GAAI;AACnC,UAAI;AACJ,iBAAW,CAAC5B,IAAG,EAAE,KAAK,IAAI,QAAO,GAAI;AACjC,YAAI,IAAI;AACJ,cAAI,CAAC;AAAM,mBAAO,EAAC,GAAAA,IAAG,GAAG,OAAO,GAAG,QAAQ,EAAC;AAC5C,eAAK;QACR,OAAM;AACH,cAAI,QAAQ,KAAK,QAAQ,GAAG;AACxB,kBAAM,KAAK,IAAI;UAClB;AACD,iBAAO;QACV;MACJ;AACD,UAAI,QAAQ,KAAK,QAAQ,GAAG;AACxB,cAAM,KAAK,IAAI;MAClB;IACJ;AAED,UAAM,MAAgB;MAClB,oEAAoE4B,KAAI,IAAIA,KAAI;;AAEpF,eAAW,EAAC,GAAA5B,IAAG,GAAG,OAAO,OAAM,KAAK,OAAO;AACvC,UAAI,KAAK,YAAYA,EAAC,QAAQ,CAAC,YAAY,KAAK,aAAa,MAAM,MAAM;IAC5E;AACD,QAAI,KAAK,QAAQ;AAEjB,WAAO,IAAI,KAAK,EAAE;EACrB,SAAQ,GAAG;AACR,YAAQ,IAAI,8BAA8B,CAAC;EAC9C;AACL;;;;;;;;;;;;;;;;;;;;;ICYa,IAAO,CAAA,KAAA0D,oBAAA,GAAA;;;;IAgBH,IAAO,CAAA,KAAA7B,oBAAA,GAAA;;;;;;;IAaC,IAAM,CAAA,KAAAC,oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA9B3B,aAyCK,QAAA,MAAA,MAAA;;;;AAzBD,aAwBK,MAAA,IAAA;;;;AAfD,aAcQ,MAAA,MAAA;AAbJ,aASK,QAAA,IAAA;AARD,aAEK,MAAA,IAAA;;;;;;AAOT,aAEK,QAAA,IAAA;;;;;;;;;;;;;;;QArCRH,KAAO,CAAA;QAAA;;;;;;;;;;;;;;QAgBHA,KAAO,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;QAaCA,KAAM,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA5BnB,aAEK,QAAA,KAAA,MAAA;;MADM,IAAO,CAAA;;AAElB,aAQQ,QAAA,UAAA,MAAA;AAHJ,aAEQ,UAAA,MAAA;;MADG,IAAO,CAAA;;;;;;;;YADW,IAAQ,CAAA;UAAA;;;YAHtB,IAAe,CAAA;UAAA,CAAA;;;YACqB,IAAW,CAAA;UAAA,CAAA,GAAA,IAAA;;;;;;;;;QALvDA,KAAO,CAAA;AAAA,UAAA;MAAA;AAAA,eAAA;QAQHA,KAAO,CAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB,aAKQ,QAAA,QAAA,MAAA;;;AAHJ,aAEK,QAAA,GAAA;;;;;;;UAJwB,IAAc,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAavC,aAEK,QAAA,KAAA,MAAA;;;;;;;;;;;;AAFgC,cAAA,CAAA;AAAA,6BAAA,gCAAA,KAAA,MAAA,EAAA,UAAU,KAAK,QAAQ,WAAU,GAAA,IAAA;;;;;;;;;AAAjC,YAAA,CAAA;AAAA,2BAAA,gCAAA,KAAA,MAAA,EAAA,UAAU,KAAK,QAAQ,WAAU,GAAA,KAAA;;;;;;;;;;;;;;;;;;;;IAhCzF,IAAI,CAAA,KAAAI,kBAAA,GAAA;;;;;;;;;;;;;;;;;QAAJJ,KAAI,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1DM,MAAA,EAAA,OAAO,GAAE,IAAA;MAEhB;AACA,MAAA,WAAW;AACX,MAAA,SAAS;AAEP,QAAA,SAAS,SAAQ;;AACvB,UAAO,MAAA;;AAEC,aAAO,IAAIgC,SAAW,IAAI,CAAA;aACrB,GAAC;AACN,cAAQ,MAAM,2BAA2B,CAAC;;;QAI5C,iBAAc,MAAA;QACZ,UAAQ;AACR,eAAQ;;AAER,iBAAW;AACX,aAAO,UAAS;;;QAIlB,WAAQ,MAAA;AACV,WAAO,MAAK;AACZ,eAAW;;AAIN,WAAA,gBAAgB,OAAK;QACtB,OAAO,OAAO,sBAAqB;AACnC,QAAA,aACA,KAAK,OAAO,MAAM,WAClB,MAAM,WAAW,KAAK,MAAM,KAAK,UACjC,KAAK,QAAQ,MAAM,WACnB,MAAM,WAAW,KAAK,OAAO,KAAK;SACjC,YAAU;AACX,eAAQ;;;AAKP,WAAA,YAAY,OAAK;QAClB,MAAM,QAAQ,UAAQ;AACtB,eAAQ;;;AAKP,WAAA,gBAAgBC,OAAY;AAC5B,QAAA,CAAA,UAAU;AAAS;AACxB,cAAU,UAAU,UAAUA,KAAI;AAClC,iBAAA,GAAA,SAAS,IAAI;AACb,eAAkB,MAAA,aAAA,GAAA,SAAS,KAAK,GAAG,IAAI;;;;AAWpB,eAAM;;;;AAmBgB,QAAA,gBAAA,MAAA,gBAAgB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7DC,iBAAA,QAAA;MAAA,IAAI,CAAA,EAAC;;;;;AAD3E,aAEK,QAAA,KAAA,MAAA;AADD,aAAyF,KAAA,UAAA;;;;;;;;UAA/C,IAAY,CAAA;QAAA;;;;;AAAY,UAAA;MAAA,KAAA,4BAAA;MAAAjC,KAAI,CAAA,EAAC,UAAO;;;;;;;;;;;;;;;;;AAnB1E,IAAA,aAAa;;QAJN,OAAI,CAAA,EAAA,IAAA;MAEX;MACA;AAGK,WAAA,aAAa,OAAK;QACnB,YAAY,MAAM,OAAO,eAAe,MAAM,OAAO;AACrD,QAAA,gBAAgB,MAAM,OAAO,YAAY;QACzC,mBAAmB,gBAAgB;AACnC,QAAA,uBAAuB,IAAI,iBAAiB;AAChD,YAAQ,MAAM,YAAY,wBAAwB,gBAAgB;AAClE,YAAQ,MAAM,YAAY,2BAA2B,mBAAmB;;AAG5E,UAAO,MAAA;AACC,QAAA,mBACC,IAAI,SAAS,aAAa,SAAS,eAAe,SAAS,iBAAiB;AACjF,YAAQ,MAAM,YAAY,2BAA2B,eAAe;;;;AAKnD,iBAAQ;;;;;;AADD,gBAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCwFnB,SAAO,CAAA,MAAP,mBAAS,KAAK,SAAK;;;;;;;;;;;;;;QAAnBkC,MAAAlC,KAAO,CAAA,MAAP,gBAAAkC,IAAS,KAAK,SAAK;AAAA,iBAAAnD,IAAA,OAAA;;;;;;;;;;;;;MACpB,SAAO,CAAA,MAAP,mBAAS,KAAK,QAAI;;;;;;;;;;;;;;QAAlBmD,MAAAlC,KAAO,CAAA,MAAP,gBAAAkC,IAAS,KAAK,QAAI;AAAA,iBAAAnD,IAAA,OAAA;;;;;;;;;;;;;AAOrB,QAAA,gCAAA;;IAAA,IAAS,CAAA,EAAC;EAAK;AAHb,MAAA;;IAAA,IAAS,CAAA,EAAC;;;;;;AAGZ,8BAAA,kBAAA,+BAAA,CAAA;;QAAAiB,KAAS,CAAA,EAAC;MAAK,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHb,UAAA;MAAA,KAAA,kBAAA;MAAAA,KAAS,CAAA,EAAC,YAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAGrB,kBAAA,+BAAA,CAAA;;UAAAA,KAAS,CAAA,EAAC;QAAK,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IALpB,IAAS,CAAA;EAAA;;mCAAd,QAAI1B,MAAA,GAAA;;;;;;;;;;;;;;;;;;;;;AALV,aAaK,QAAA,MAAA,MAAA;AAZD,aAGK,MAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UACE0B,KAAS,CAAA;QAAA;;qCAAd,QAAI1B,MAAA,GAAA;;;;;;;;;;;;;6BAAJ,QAAIA,KAAA,YAAA,QAAAA,MAAA,GAAA;;;;;;;;;;;uCAAJ,QAAIA,MAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjGA,QAAA,WAAW,QAAQ,QAAS,CAAA6D,aAAO;UAC/B,aAAU,CAAA;QACZA,UAAO;AACP,MAAAA,SAAQ,KAAK,SAAS,QAAS,CAAA3D,aAAO;AAC1B,gBAAAA,SAAQ,MAAI;eACX;AACD,uBAAW,KAAI;cACX,WAAW;cACX,OACI,EAAA,MAAMA,SAAQ,KAAI;;;;eAKzB;AACD,uBAAW,KAAI;cACX,WAAW;cACX,OACI,EAAA,MAAMA,SAAQ,KAAI;;;;eAKzB;AACD,uBAAW,KAAI;cACX,WAAW;cACX,OACI,EAAA,OAAOA,SAAQ,MAAK;;;;eAK3B;AACD,uBAAW,KAAI;cACX,WAAW;cACX,OACI,EAAA,MAAMA,SAAQ,KAAI;;;;eAKzB;AACD,uBAAW,KAAI;cACX,WAAW;cACX,OACI,EAAA,MAAMA,SAAQ,KAAI;;;;eAKzB;AACD,uBAAW,KAAI;cACX,WAAW;cACX,OACI,EAAA,MAAMA,SAAQ,KAAI;;;;eAKzB;AACD,uBAAW,KAAI;cACX,WAAW;cACX,OACI,EAAA,MAAMA,SAAQ,KAAI;;;;eAKzB;AACD,uBAAW,KAAI;cACX,WAAW;cACX,OACI,EAAA,MAAMA,SAAQ,KAAI;;;;;sBAMhB,MAAK,yBAA0BA,SAAQ,IAAI,EAAA;;;;;WAK9D;;;AAGL,QAAAmC,YAAW,sBAAqB;AAcX,QAAA,mBAAA,MAAAA,UAAS,UAAU;AACrB,QAAA,iBAAA,MAAAA,UAAS,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxFtC,aAaS,QAAA,KAAA,MAAA;AAZL,aAA2pC,KAAA,KAAA;AAC3pC,aAA8f,KAAA,KAAA;AAC9f,aAAs3B,KAAA,KAAA;AACt3B,aAAg8B,KAAA,KAAA;AACh8B,aAAqwB,KAAA,KAAA;AACrwB,aAAooC,KAAA,KAAA;AACpoC,aAA61C,KAAA,KAAA;AAC71C,aAAgS,KAAA,KAAA;AAChS,aAAyhE,KAAA,KAAA;AACzhE,aAAopB,KAAA,KAAA;AACppB,aAAuP,KAAA,MAAA;AACvP,aAAsnB,KAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA7B1nB,aAaS,QAAA,KAAA,MAAA;AAZL,aAAsqC,KAAA,KAAA;AACtqC,aAA2f,KAAA,KAAA;AAC3f,aAA42B,KAAA,KAAA;AAC52B,aAAk7B,KAAA,KAAA;AACl7B,aAAqwB,KAAA,KAAA;AACrwB,aAAqoC,KAAA,KAAA;AACroC,aAAi2C,KAAA,KAAA;AACj2C,aAAgS,KAAA,KAAA;AAChS,aAAshE,KAAA,KAAA;AACthE,aAAmpB,KAAA,KAAA;AACnpB,aAAwP,KAAA,MAAA;AACxP,aAAwmB,KAAA,MAAA;;;;;;;;;;;;IAoBpmB,IAAE,CAAA,EAAC,0BAAwB,EAAG,QAAO,CAAA,IAAA;;;;;;;;;;;;;MAArCX,KAAE,CAAA,EAAC,0BAAwB,EAAG,QAAO,CAAA,IAAA;AAAA,iBAAA,KAAA,SAAA;;;;;;;;;;AAE5C,MAAA;;IAAA,IAAE,CAAA,EAAC,uBAAuB,IAAA;;;;;;;;;;;AAA1B,UAAA;MAAA,KAAA,eAAA;MAAAA,KAAE,CAAA,EAAC,uBAAuB,IAAA;AAAA,iBAAA,KAAA,SAAA;;;;;;;;;;;;;;;;;;;AArC1B;;MAAAA,KAAK,CAAA,MAAK;;AAAM,aAAAI;;;;;;;;;;;;;;;;;;;;QAyCb,QAAQ;QACR,SAAS;QACT;;UAAO,IAAE,CAAA,EAAC,qBAAqB;;QAC/B,MAAM;QACN,QAAQ;;;;;;;;;;;;;;;;;AA9CpB,aAiDK,QAAA,KAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAPO,QAAQ;UACR,SAAS;UACT;;YAAOJ,KAAE,CAAA,EAAC,qBAAqB;;UAC/B,MAAM;UACN,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAnDT,GAAAjB,GAAC,IAAI,WAAqB,MAAM;;AAElC,eAAA,GAAA,EAAA,MAAK,IAAIwB,gBAAIC,UAAQ,GAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCCSF,QAAO,EAAA,CAAA;;;;;;;;AADvB,aAEK,QAAA,KAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAHJ,IAAO,CAAA,KAAAJ,kBAAA;;;;;;;;QAFX,IAAK,CAAA;MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAFN,aAAoF,QAAA,OAAA,MAAA;;;;;;;;;;AAAX,cAAA;;YAAA,IAAQ,CAAA;UAAA;AAAR,gBAAQ,CAAA,EAAA,MAAA,MAAA,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAEhF,IAAK,CAAA;QAAA;;;QAED,IAAO,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANpB,aAYO,QAAA,SAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QArBQ,KAAY,IAAA;QACZ,MAAoC,IAAA;QACpC,QAAgB,IAAA;QAChB,MAAyB,IAAA;QACzB,SAAoB,IAAA;QACpB,OAAe,IAAA;QACf,MAAK,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICUG;;MAAA,IAAM,CAAA,EAAC;;;;MACR,IAAO,CAAA;;IACN;;MAAA,IAAM,CAAA,EAAC;;IACL;;MAAA,IAAU,CAAA;;QAAA,IAAa,CAAA;MAAA;MAAA,IAAM,CAAA,EAAC;;;;;AAC3B;;IAAA,IAAS,CAAA;;MAAC,IAAO,CAAA;IAAA,MAAA;IAAA;AAAjB,qBAAA;IAAA,IAAS,CAAA;;MAAC,IAAO,CAAA;IAAA;;;;;;;;;;;;;;;;;AAJtB,UAAA;MAAA;AAAA,2BAAA;QAAA,IAAM,CAAA,EAAC;;;;QACR,IAAO,CAAA;AACN,UAAA;MAAA;AAAA,2BAAA;QAAA,IAAM,CAAA,EAAC;AACL,UAAA;MAAA;AAAA,2BAAA;QAAA,IAAU,CAAA;;UAAA,IAAa,CAAA;QAAA;QAAA,IAAM,CAAA,EAAC;;;;;;;AAC3B,2BAAA;QAAA,IAAS,CAAA;;UAAC,IAAO,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAPlC,IAAO,CAAA;EAAA;;mCAAZ,QAAI9B,MAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAC0B,KAAO,CAAA;QAAA;;qCAAZ,QAAI1B,MAAA,GAAA;;;;;;;;;;;;;6BAAJ,QAAIA,KAAA,YAAA,QAAAA,MAAA,GAAA;;;;;;;;;uCAAJ,QAAIA,MAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAVK,QAAe,IAAA;QACf,QAA+B,IAAA;AAG/B,MAAA,EAAA,WAA4D,WAAK;gCACxE,UAAU,OAAO,IAAI,OAAK,SAAA;;2BAaF,SAAS,OAAO,KAAK;;AADzB,QAAA,OAAA,GAAA,UAAA,UAAU,OAAO,GAAA,KAAA,GAAA;AAAjB,gBAAU,OAAO,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;QCgEd,IAAoB,CAAA;;;;;;;;;;;;;;;;;;;QAApB0B,KAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAGxB,IAAE,CAAA,EAAA,sBAAA;;;;;;;MAGF;;QAAA,IAAU,CAAA,EAAA;;UACX,IAAE,CAAA,EAAA;UAAmB,IAAS,CAAA,EAAC,KAAK,EAAA;;;UACpC,IAAE,CAAA,EAAC,0BAA0B;;;;;;;;;QAG5B,IAAE,CAAA,EAAA,yBAAA;;;;;;;aAGFoC;;QAAU,IAAS,CAAA,EAAC;MAAQ;;;;;;;QAG5B,IAAE,CAAA,EAAA,2BAAA;;;;;;;MAGF;;QAAA,IAAU,CAAA,EAAA;;UACX,IAAE,CAAA,EAAA,6BAAA;;;UACF,IAAE,CAAA,EAAC,8BAA8B;;;;;;;MAGhC;;QAAA,IAAE,CAAA,EAAC,sBAAsB;;;;;;;;;;;MAKzB;;QAAA,IAAE,CAAA,EAAC,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA3BpBpC,KAAE,CAAA,EAAA,sBAAA;AAGF,UAAA;MAAA;AAAA,0BAAA;QAAAA,KAAU,CAAA,EAAA;;UACXA,KAAE,CAAA,EAAA;UAAmBA,KAAS,CAAA,EAAC,KAAK,EAAA;;;UACpCA,KAAE,CAAA,EAAC,0BAA0B;;;;;;;QAG5BA,KAAE,CAAA,EAAA,yBAAA;;;kCAGFoC;;UAAUpC,KAAS,CAAA,EAAC;QAAQ;;;;;;QAG5BA,KAAE,CAAA,EAAA,2BAAA;AAGF,UAAA;MAAA;AAAA,0BAAA;QAAAA,KAAU,CAAA,EAAA;;UACXA,KAAE,CAAA,EAAA,6BAAA;;;UACFA,KAAE,CAAA,EAAC,8BAA8B;;;;AAGhC,UAAA;MAAA;AAAA,0BAAA;QAAAA,KAAE,CAAA,EAAC,sBAAsB;;;AAKzB,UAAA;MAAA;AAAA,0BAAA;QAAAA,KAAE,CAAA,EAAC,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAyBhB,IAAoB,CAAA;;;;;;;;;;;;;;;;;;;QAApBA,KAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QARpB,IAAoB,CAAA;;;;;;;;;;;;;;;;;;;QAApBA,KAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAJpB,IAAoB,CAAA;;;;;;;;;;;;;;;;;;;QAApBA,KAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAJpB,IAAoB,CAAA;;;;;;;;;;;;;;;;;;;QAApBA,KAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAiBC,IAAgB,CAAA;;;;;;;;;;;;;;;;;QAAhBA,KAAgB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;QAN3C,IAAe,CAAA;;;;;;;;;;;;;;;;;;;;;QAAfA,KAAe,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;QANO,IAAY,CAAA;;;;;;;;;;;;;;;;;QAAZA,KAAY,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA7CjD,MAAA,YAAA;EAAA,IAAe,CAAA,EAAC,QAAIa,oBAAA,GAAA;;;;;;MAuCrBb,KAAe,CAAA,EAAC,SAAS;;AAAO,aAAA;;;MAI3BA,KAAe,CAAA,EAAC,SAAS;;AAAO,aAAA;;;MAIhCA,KAAe,CAAA,EAAC,SAAS;;AAAU,aAAA;;;MAQnCA,KAAe,CAAA,EAAC,SAAS;;AAAY,aAAA;;;;;;;;;;;;;;;;;AAxDnD,aA6DK,QAAA,KAAA,MAAA;;;;;;;;;;AA5DK,UAAA;MAAAA,KAAe,CAAA,EAAC,MAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA3EpB,QAAA,iBAAiB,WAAU;;AAE1B,QAAA,EAAA,GAAAjB,IAAG,UAAS,IAAI,WAAqB,MAAM;;WAEzC,gBAAa;AAClB,oBAAA,qBAAA,uBAAuB,OAAK,oBAAA;AAC5B,WAAO,KAAI;AACX,eAAW,IAAI,MAAS;;AAGnB,WAAA,WAAW,MAAY;AAC5B,oBAAA,qBAAA,uBAAuB,OAAK,oBAAA;AAC5B,mBAAe,KAAK,IAAI;AACxB,oBAAA,OAAA,OAAO,WAAW,GAAE,YAAa,IAAI,QAAA,GAAA,MAAA;AACrC,eAAW,IAAG,MAAA;AACV,sBAAA,qBAAA,uBAAuB,OAAK,oBAAA;AAC5B,qBAAe,KAAI;AACnB,iBAAW,IAAI,aAAa;6BAC5B,OAAO,WAAW,QAAS,MAAA;;;AAInC,UAAO,MAAA;AACH,eAAW,IAAI,aAAa;AAC5B,oBAAA,OAAA,OAAO,QAAQ,GAAG,gBAAgB,GAAA,MAAA;2BAClC,OAAO,WAAW,QAAS,MAAA;AAC3B,oBAAA,qBAAA,uBAAuB,OAAK,oBAAA;;AAkCjB,iBAAA,eAAe,QAAc;UAClC,UAAO,MAAS,UAAU,MAAM;QAClC,SAAO;AACPyB,iBAAS,IACF,EAAA,GAAAD,gBAAIC,UAAQ,GACf,UAAU,OAAM,CAAA;AAGpB,sBAAA,OAAA,OAAO,QAAQ,GAAG,gBAAgB,GAAA,MAAA;AAClC,sBAAA,OAAA,OAAO,WAAW,GAAG,yBAAyB,GAAA,MAAA;;;AAWvB,QAAA,OAAA,MAAA,WAAW,OAAO;AAQlB,QAAA,SAAA,MAAA,WAAW,UAAU;AAMrB,QAAA,SAAA,MAAA,WAAW,YAAY;AAQvB,QAAA,SAAA,MAAA,WAAW,OAAO;iBAyB1B,YAAW,eAAe,MAAM;;;;AAlGvD,mBAAA,GAAG,mBAAgB;;UAEX,OAAO,GAAG,6BAA6B;UACvC,OAAO;;;UAGP,OAAO,GAAG,8BAA8B;UACxC,OAAO;;;;;;AASf,mBAAA,GAAG,eAAY;;UAEP,OAAO,GAAG,0BAA0B;UACpC,OAAO;;;UAGP,OAAO,GAAG,sBAAsB;UAChC,OAAO;;;UAGP,OAAO,GAAG,qBAAqB;UAC/B,OAAO;;;;;AAhBd,eAAA,GAAE,kBAAkB,OAAO,KAAK4B,IAAS,EAAE,IAAKC,aAAI,EACjD,OAAOD,KAAUC,MAAI,GACrB,OAAOA,OAAI,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QC9BX;;UAAO,IAAE,CAAA,EAAC,uBAAwB,EAAA,SAAS,4BAA2B,CAAA;;;;;;;;;;;;;;;;;;UAAtE;;YAAOrC,KAAE,CAAA,EAAC,uBAAwB,EAAA,SAAS,4BAA2B,CAAA;;;;;;;;;;;;;;;;;;;;;;UAlBnE,GAAAjB,GAAC,IAAI,WAAqB,MAAM;;AAEtB,wBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACUtC,aAYS,QAAA,SAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAXO,IAAK,CAAA;MAAA;;;;;;;;;;;UAALiB,KAAK,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;MAIE;;QAAA,IAAM,CAAA,EAAC;;;;YAGR;;QAAc,IAAM,CAAA,EAAC;MAAQ;;;;;;;;;;;;;;AAH5B,UAAA;MAAA;AAAA,yBAAA;QAAA,IAAM,CAAA,EAAC;;;;;;gCAGR;;UAAc,IAAM,CAAA,EAAC;QAAQ;;;;;;;;;;;;;;;;;;;;;;;IALpC,IAAO,CAAA;EAAA;;mCAAZ,QAAI1B,MAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAC0B,KAAO,CAAA;QAAA;;qCAAZ,QAAI1B,MAAA,GAAA;;;;;;;;;;;;;6BAAJ,QAAIA,KAAA,YAAA,QAAAA,MAAA,GAAA;;;;;;;;;uCAAJ,QAAIA,MAAA,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;IAJb,IAAO,CAAA,KAAA8B,kBAAA,GAAA;;;;;;;;;;;;;;;;;QAAPJ,KAAO,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QATG,QAAwC,IAAA;QACxC,MAAa,IAAA;AAElB,QAAAW,YAAW,sBAAqB;AAaP,QAAA,OAAA,YAAAA,UAAS,UAAU,OAAO,EAAE;;;;;;;;;;;;;;;;;ACoLvD,MAAA;;IAAA,IAAG,CAAA,EAAA,WAAY,EAAA,SAAS,aAAY,CAAA,IAAA;;;;;;;;;AAAxC,aAA8C,QAAA7B,IAAA,MAAA;;;;AAA1C,UAAA;MAAA,KAAA,eAAA;MAAAkB,KAAG,CAAA,EAAA,WAAY,EAAA,SAAS,aAAY,CAAA,IAAA;AAAA,iBAAA,KAAA,SAAA;;;;;;;;;;;;;;;;;;;;;MA7BnCA,KAAK,CAAA;MAAKA,KAAK,CAAA,EAAC;;AAAY,aAAA;AAUvB;;MAAAA,KAAU,CAAA;MAAAA,KAAM,CAAA,EAAA;MAAeA,KAAO,CAAA;;AAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAcpC;;UAAO,IAAE,CAAA,EAAC,6BAA8B,EAAA,SAAS,mBAAkB,CAAA;;;;;;;;;;;;;;;;;;UAAnE;;YAAOA,KAAE,CAAA,EAAC,6BAA8B,EAAA,SAAS,mBAAkB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;QAbpD,IAAoB,CAAA;;;;;;;;;;;;;;;;;;;QAApBA,KAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAVpB,IAAoB,CAAA;;;;;;;;;;;;;;;;;;;QAApBA,KAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAe3B,IAAO,CAAA;;MACR;;QAAA,IAAG,CAAA,EAAA,gCAAiC,EAAA,SAAS,sBAAqB,CAAA;;;;;;;IAH9D,IAAW,EAAA;EAAA;;;;IACX,IAAa,EAAA;EAAA;;;;;;;;;;;;;;QAChBA,KAAO,CAAA;AACR,UAAA;MAAA;AAAA,2BAAA;QAAAA,KAAG,CAAA,EAAA,gCAAiC,EAAA,SAAS,sBAAqB,CAAA;;;;;;;;;;;;;;;;;;;;;;;MAXhE;;QAAA,IAAuB,CAAA,EAAC;;MAC1B;;QAAA,IAAG,CAAA,EAAA,iCAAkC,EAAA,SAAS,4BAA2B,CAAA;;;;;;;IAHrE,IAAY,EAAA;EAAA;;;;IACZ,IAAM,EAAA;EAAA;;;;;;;;;;;AACR,UAAA;MAAA;AAAA,8BAAA;QAAAA,KAAuB,CAAA,EAAC;AAC1B,UAAA;MAAA;AAAA,8BAAA;QAAAA,KAAG,CAAA,EAAA,iCAAkC,EAAA,SAAS,4BAA2B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAR3F;;MAAAA,KAAM,CAAA;MAAIA,KAAuB,CAAA;;AAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAzJ3B,GAAAjB,GAAC,IAAI,WAAqB,MAAM;;AAEnC,MAAA,YAAY;AAEV,QAAA4B,YAAW,sBAAqB;MAKjC;YAAAO,QAAK;AACN,IAAAA,OAAa,MAAA,IAAA;AACb,IAAAA,OAA6B,cAAA,IAAA;AAC7B,IAAAA,OAA2B,aAAA,IAAA;EAH1B,GAAA,UAAA,QAAK,CAAA,EAAA;QAMJ,gBAA6D,QAC9D,CAAA,wBAAwB,uBAAuB,GAAA,CAAA,CAC9C,iBAAiB,gBAAgB,MAAA;;AAC1B,QAAA,CAAA,mBAAe,CAAK,kBAAgB;aAC9B;;AAEL,UAAA,SAAS,gBAAgB,uBAAuB,KACjD,CAAAoB,YAAWA,QAAO,OAAO,iBAAiB,QAAQ;SAGlD,iBAAiB,WAAS,sCAAQ,OAAO,oBAAf,mBAAgC,YAAW,GAAC;AACvE,uBAAiB,QAAQ,OAAO,OAAO,gBAAgB,CAAC,EAAE;;WAEvD;;MAIX,SAAsC,QACrC,CAAA,wBAAwB,aAAa,GAAA,CAAA,CACpC,iBAAiB,qBAAqB,MAAA;AAChC,QAAA,mBAAmB,uBAAqB;UAEpC,sBAAsB,OAAO,iBAAe;AACxC,YAAA,gBAAgB,QAAM;AACf,iBAAA,gBAAgB,OAAO,OAAQ,WAAK;;;;eAGnC,2BAAsB,OAAO,oBAA7B,mBAA8C,KAAM,CAAAtD,OAChDA,GAAE,GAAG,OAAO,MAAM,EAAE;;;;;eAMjC,iBAAe;AACf,aAAA,gBAAgB;;;;;AAM7B,QAAA,oCAAoC,uBAAuB,UAAW,oBAAc;QAClF,gBAAc;;;QAEd,OAAO,WAAW,GAAG,mBAAiB;UAClC,SAAS,eAAe;UACxB,SAAS;;;;AAIT,UAAA,eAAe,uBAAuB,WAAW,GAAC;iDAClD,yBAAyB,WAAW,eAAe,uBAAuB,CAAC,EAAE,IAAE,wBAAA;;AAI/E,UAAA,eAAe,OAAK;AACpB,wBAAA,yBAAA,yBAAyB,QAAQ,eAAe,MAAM,IAAE,wBAAA;iBACjD,eAAe,UAAU,eAAe,OAAO,WAAW,GAAC;iDAClE,yBAAyB,QAAQ,eAAe,OAAO,CAAC,EAAE,IAAE,wBAAA;;;;AAKxE,UAAO,MAAA;2BAEH,OAAO,QAAQ,GAAG,yBAAuB,EAAG,SAAS,iBAAgB,CAAA,GAAA,MAAA;;AAGzE,YAAU,iCAAiC;QAErC,WAAQ,MAAA;SACL,WAAS;AACV,kBAAY;AAGZ;;AACI,UAAA2B,UAAS,YAAY,wBAAwB;AAC7C,qBAAW,IAAI,MAAS;;QACzB;;;;AAIL,QAAA,OAAO,QAAO,CACf,wBAAwB,yBAAyB,eAAe,MAAM,GAAA,CAAA,CACrE,UAAU,kBAAkB,uBAAuB4B,QAAO,MAAA;AACnD,QAAA,CAAA,0BAAyB,qCAAU,eAAe,uBAAoB;AAChE,aAAA,MAAM;;AAGb,QAAA,sBAAsB,+DAAuB,OAAO;AAGpD,QAAA,wBAAwB,OAAK;AAC7B,4BAAsB,qCAAU,eAAe;;SAI9C,iBAAiB,SAClB,qBAAmB;AAEZ,aAAA,MAAM;;AAIjB,aAAQ;;;AAKV,QAAA,eAAgB,OAAC;AACnB,oBAAA,yBAAA,yBAAyB,WAAW,EAAE,QAAM,wBAAA;AAC5C,oBAAA,YAAA,cAAc,gBAAc,WAAA;AAC5B,oBAAA,qBAAA,uBAAuB,OAAK,oBAAA;;AAG1B,QAAA,iBAAkB,OAAC;6CACrB,yBAAyB,WAAW,QAAS,wBAAA;AAC7C,oBAAA,YAAA,cAAc,QAAS,WAAA;AACvB,oBAAA,qBAAA,uBAAuB,OAAK,oBAAA;;AAG1B,QAAA,cAAe,OAAC;AAClB,oBAAA,yBAAA,yBAAyB,QAAQ,EAAE,QAAM,wBAAA;AACzC,oBAAA,YAAA,cAAc,eAAa,WAAA;AAC3B,oBAAA,qBAAA,uBAAuB,OAAK,oBAAA;;AAG1B,QAAA,gBAAiB,OAAC;6CACpB,yBAAyB,QAAQ,QAAS,wBAAA;AAC1C,oBAAA,YAAA,cAAc,gBAAc,WAAA;AAC5B,oBAAA,qBAAA,uBAAuB,OAAK,oBAAA;;QAG1BnB,UAAM,MAAA;AACR,IAAAT,UAAS,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICjKT,IAAI,CAAA;IAAA,EAAA,CAAA;;;;;;;;;;;QACqB,IAAI,CAAA;MAAA;;;;;;AAH7C,aAIQ,QAAA,QAAA,MAAA;AAHJ,aAA0B,QAAA,KAAA;;;;AAE1B,aAAgD,QAAA,KAAA;;;;;AAHlC,cAAA;;YAAA,IAAO,CAAA;UAAA;AAAP,gBAAO,CAAA,EAAA,MAAA,MAAA,SAAA;;;;;;;;;;;QAET,IAAI,CAAA;;;;;;;UACqB,IAAI,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;QAP9B,QAAO,IAAA;QACP,KAAkC,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BCmEkB,cAAc;;;2BAJ5D,WAAQ,GAAA;;;;;;;0BAgBsC,aAAa;;;2BAJ3D,WAAQ,GAAA;;;;;;;0BAgBsC,cAAc;;;2BAJ5D,WAAQ,GAAA;;;;;qCAvDX,YAAS,IAAA;4CACF,cAAW,IAAA;2CACZ,cAAW,IAAA;4CACV,cAAW,IAAA;4CACX,kBAAe,IAAA;kCACzB,MAAM;4CACI,OAAO;2CACR,OAAO;4CACN,OAAO;kDACD,eAAY,IAAA;iDACb,eAAY,IAAA;kDACX,eAAY,IAAA;4CAClB,eAAY,IAAA;2CACb,eAAY,IAAA;4CACX,eAAY,IAAA;;;AAjBrC,aAiEK,QAAA,MAAA,MAAA;AA7CD,aAQK,MAAA,IAAA;AAPD,aAMM,MAAA,IAAA;AALF,aAIU,MAAA,QAAA;AAHN,aAEC,UAAA,IAAA;;AAKb,aAUK,MAAA,IAAA;AATD,aAQK,MAAA,IAAA;AADD,aAAwE,MAAA,KAAA;;AAIhF,aAUK,MAAA,IAAA;AATD,aAQK,MAAA,IAAA;AADD,aAAuE,MAAA,KAAA;;AAI/E,aAUK,MAAA,IAAA;AATD,aAQK,MAAA,IAAA;AADD,aAAwE,MAAA,KAAA;;;;;;;;;;;;AA5F5E,IAAA,SAAS;AACT,IAAA,YAAY;AAEZ,IAAA,cAAc;AACd,IAAA,cAAc;AACd,IAAA,cAAc;AAEd,IAAA,kBAAkB;AAElB,IAAA,WAAW;AACX,IAAA,WAAW;AACX,IAAA,WAAW;AAEX,IAAA,UAAU;AACV,IAAA,UAAU;AACV,IAAA,UAAU;AAEV,IAAA,eAAe;AACf,IAAA,eAAe;AACf,IAAA,eAAe;AAEf,IAAA,eAAe;AACf,IAAA,eAAe;AACf,IAAA,eAAe;AAEb,IAAA,iBAAiB;AACjB,IAAA,gBAAgB;AAChB,IAAA,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QCZgC,IAAW,CAAA;;;;;;;;;;;;;;;;;QAAXX,KAAW,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;MADrDA,KAAW,CAAA;;AAAA,aAAA;;;MAENA,KAAc,CAAA;;AAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAehB,IAAQ,CAAA;MAAA;;;;AAAZ,aAAgB,QAAAlB,IAAA,MAAA;;;;;;;;;UAAZkB,KAAQ,CAAA;QAAA;;;;;;;;;;;;;;;;IADX,IAAQ,CAAA,KAAAI,kBAAA,GAAA;;;;;;;QADR,IAAK,CAAA;MAAA;;;;;;;;AAAV,aAAe,QAAA,IAAA,MAAA;;;;;;;;;;;;;UAAVJ,KAAK,CAAA;QAAA;;;QACLA,KAAQ,CAAA;QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAnBzB,aA6BK,QAAA,MAAA,MAAA;AA5BD,aAcK,MAAA,IAAA;;;;;AACL,aAOK,MAAA,IAAA;;;;;AACL,aAIK,MAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QApCM,MAAa,IAAA;QACb,SAA4B,IAAA;AAEjC,QAAAW,YAAW,sBAAqB;;AAclB,WAAO,KAAK,UAAU;AACtB,oBAAA,qBAAA,uBAAuB,OAAI,oBAAA;;AAgBG,QAAA,SAAA,MAAAA,UAAS,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCgBpD;;QAAA,IAAM,CAAA,EAAC;;MAAiB;;QAAA,IAAM,CAAA,EAAC;;;;;;;IAAqB,IAAa,CAAA;EAAA;;;;;;;;;;;;;;;;;;;;;AAFpE,WAAA,UAAA,cAAA;MAAA,IAAS,CAAA,EAAC,KAAK;;;;AAH/B,aASQ,QAAA,UAAA,MAAA;;;AAHJ,aAEK,UAAA,GAAA;;;;;;;;;UANgD,IAAe,CAAA;QAAA,CAAA,GAAA,EAAA,SAAA,OAAA,SAAA,KAAA,CAAA;;;;;;AAGrD,UAAA;MAAA;AAAA,uBAAA;QAAAX,KAAM,CAAA,EAAC;AAAiB,UAAA;MAAA;AAAA,uBAAA;QAAAA,KAAM,CAAA,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;AAFlC,UAAA,CAAA,WAAA;MAAA,KAAA,+BAAA;MAAAA,KAAS,CAAA,EAAC,QAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAhDvB;AAGE,QAAA,cAAc,OAAO,UAAW,aAAO;QACrC,QAAM;UACF,WAAO,CAAK,OAAO,MAAI;AACvB,eAAO,UAAS;kBACR,WAAW,OAAO,MAAI;AAC9B,eAAO,MAAK;AACZ,mBAAU;;;;AAKtB,YAAU,WAAW;WAGZ,gBAAa;AAElB,wBAAoB,IAAK,CAAAc,OAAMA,GAAE,gBAAgB,IAAI,CAAA;AAErD,WAAO,IAAI,KAAK;;AAIX,WAAA,gBAAgB,OAAK;QACtB,OAAO,OAAO,sBAAqB;AACnC,QAAA,aACA,KAAK,OAAO,MAAM,WAClB,MAAM,WAAW,KAAK,MAAM,KAAK,UACjC,KAAK,QAAQ,MAAM,WACnB,MAAM,WAAW,KAAK,OAAO,KAAK;SACjC,YAAU;AACX,oBAAa;;;AAKrB,WAAS,iBAAiB,WAAY,WAAK;AACnC,QAAA,MAAM,QAAQ,YAAY,OAAO,MAAI;AACrC,oBAAa;;;;;AAMV,eAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2Bb,aAAoB,QAAAhC,IAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAhBfkB,KAAa,CAAA;;AAAA,aAAA;;;MAERA,KAAO,CAAA;;AAAA,aAAA;;;MAEPA,KAAO,CAAA,EAAC,SAAS;;AAAO,aAAA;;;MAExBA,KAAO,CAAA,EAAC,SAAS;;AAAU,aAAA;;;MAE3BA,KAAO,CAAA,EAAC,SAAS;;AAAU,aAAA;;;MAE3BA,KAAO,CAAA,EAAC,SAAS;;AAAgB,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IACb,IAAM,CAAA;EAAA;;;;IAAe,IAAQ,CAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAFlC,IAAM,CAAA;EAAA;;;;IAAe,IAAQ,CAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAF7B,IAAM,CAAA;EAAA;;;;IAAe,IAAQ,CAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAFhC,IAAM,CAAA;EAAA;;;;IAAe,IAAQ,CAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAF5B,IAAM,CAAA;EAAA;;;;IAAe,IAAQ,CAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAF9B,IAAM,CAAA;EAAA;;;;IAAe,IAAQ,CAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAFlDA,KAAO,CAAA;;AAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA1CD,KAAI,IAAA;AACf,aAAqB,QAAQ,IAAI;AAExB,WAAAoB,QAAM,EAAE,OAAM,GAAA;QAEf,eAAa;AACb,oBAAc,OAAO,MAAM;;QAE3B,SAAO;AACP,cAAQ,OAAO,MAAM;AACrB,aAAO,MAAK;;AAEhB,WAAO,KAAI;;AAGN,WAAA,SAAQ,EAAE,OAAM,GAAA;QAEjB,eAAa;AACb,oBAAc,QAAQ,MAAM;;QAE5B,yBAAuB;AACvB,8BAAwB,QAAQ,MAAM;;QAEtC,SAAO;AACP,cAAQ,QAAQ,MAAM;AACtB,aAAO,MAAK;AACZ,aAAO,KAAI;;;AAIb,QAAA,cAAc,OAAO,UAAW,aAAO;AACrC,QAAA,WAAW,QAAQ,SAAS,SAAO;AACnC,oBAAc,IAAI,IAAI;;AAEtB,oBAAc,IAAI,KAAK;;;AAI/B,YAAU,WAAW;;;;;;;;;;;;;AC1DzB,IAAIoB,MAAE,OAAO;AAAb,IAA4B,IAAE,OAAO;AAAiB,IAAIC,MAAE,OAAO;AAA0B,IAAIC,MAAE,OAAO;AAAsB,IAAIC,MAAE,OAAO,UAAU;AAAvB,IAAsCC,MAAE,OAAO,UAAU;AAAqB,IAAItB,MAAE,CAAC,GAAEvC,IAAE,MAAIA,MAAK,IAAEyD,IAAE,GAAEzD,IAAE,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,IAAE,EAAEA,EAAC,IAAE;AAAtF,IAAwFQ,MAAE,CAAC,GAAER,OAAI;AAAC,WAAQ,KAAKA,OAAIA,KAAE,CAAA;AAAI4D,QAAE,KAAK5D,IAAE,CAAC,KAAGuC,IAAE,GAAE,GAAEvC,GAAE,CAAC,CAAC;AAAE,MAAG2D;AAAE,aAAQ,KAAKA,IAAE3D,EAAC;AAAE6D,UAAE,KAAK7D,IAAE,CAAC,KAAGuC,IAAE,GAAE,GAAEvC,GAAE,CAAC,CAAC;AAAE,SAAO;AAAC;AAA1M,IAA4MiC,MAAE,CAAC,GAAEjC,OAAI,EAAE,GAAE0D,IAAE1D,EAAC,CAAC;AAAE,IAAI8D,MAAE,CAAC,GAAE9D,OAAI;AAAC,MAAI,IAAE,CAAA;AAAG,WAAQ,KAAK;AAAE4D,QAAE,KAAK,GAAE,CAAC,KAAG5D,GAAE,QAAQ,CAAC,IAAE,MAAI,EAAE,CAAC,IAAE,EAAE,CAAC;AAAG,MAAG,KAAG,QAAM2D;AAAE,aAAQ,KAAKA,IAAE,CAAC;AAAE,MAAA3D,GAAE,QAAQ,CAAC,IAAE,KAAG6D,IAAE,KAAK,GAAE,CAAC,MAAI,EAAE,CAAC,IAAE,EAAE,CAAC;AAAG,SAAO;AAAC;AAAgE,IAAIE,MAAE,CAAC,SAAQ,QAAO,OAAO;AAA7B,IAA+BC,MAAE,CAAC,EAAC,QAAO,IAAE,SAAQ,OAAMhE,KAAE+D,IAAE,CAAC,GAAE,QAAO,IAAE,WAAU,MAAIA,IAAE,OAAO,CAAC,GAAE,GAAEE,OAAIhC,IAAEzB,IAAE,CAAA,GAAG,CAAC,GAAE,EAAC,CAAC,CAAC,GAAE,CAAAjB,OAAGwE,IAAE,QAAQ/D,EAAC,KAAGiE,MAAG,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG1E,EAAC,EAAE,EAAC,CAAC,GAAE,CAAA,CAAE;AAAlK,IAAoKe,MAAE0D,IAAE,CAAA,CAAE;AAA1K,IAA4KE,MAAE,OAAG;AAAC5D,QAAE;AAAC;AAAE,IAAI6D,MAAE,CAAAC,OAAG;AAAC,MAAInE,KAAEmE,IAAE,EAAC,QAAO,GAAE,KAAIpE,IAAE,QAAO,GAAE,cAAa,GAAE,QAAO,GAAE,gBAAeiE,GAAC,IAAEhE,IAAEV,KAAEuE,IAAE7D,IAAE,CAAC,UAAS,OAAM,UAAS,gBAAe,UAAS,gBAAgB,CAAC;AAAE,MAAG,CAACD;AAAE,WAAOM,IAAE,KAAK,iCAAiC,CAAC,oCAAoC,GAAE;AAAG,MAAG,CAAC;AAAE,WAAOA,IAAE,KAAK,2BAA2BN,EAAC,gCAAgC,GAAE;AAAG,MAAI+B,MAAG,EAAE,CAAC,KAAG,CAAA,GAAI/B,EAAC;AAAE,SAAOiE,MAAGlC,OAAI,WAASA,MAAG,EAAEkC,EAAC,KAAG,CAAA,GAAIjE,EAAC,IAAGT,GAAE,eAAe,eAAe,KAAGwC,OAAI,SAAOxC,GAAE,gBAAc,EAAE,MAAMwC,IAAE,GAAE,GAAE/B,EAAC;AAAC;AAA/d,IAAieqE,MAAE,IAAI,MAAI,EAAE,SAAO,EAAE,OAAO,CAAArE,OAAG,CAAC,CAACA,EAAC,EAAE,IAAI,CAAAA,OAAG;AAAC,MAAI,IAAE,GAAGA,EAAC,GAAG,YAAW;AAAG,MAAG;AAAC,QAAG,CAAC,CAAC,IAAE,KAAK,SAAS,mBAAmBA,EAAC;AAAE,QAAG,CAAC;AAAE,YAAM,IAAI;AAAM,QAAE;EAAC,SAAO,GAAE;AAACM,QAAE,KAAK,IAAIN,EAAC,2BAA2B;EAAC;AAAC,SAAO;AAAC,CAAC,IAAE,CAAA;AAA9qB,IAAirBV,MAAE,CAAC,GAAEU,IAAE,MAAI,OAAO,KAAK,KAAG,CAAA,CAAE,EAAE,OAAO,CAAC,GAAE,MAAI;AAAC,MAAIiE,KAAE,EAAE,CAAC,GAAE1E,KAAE,IAAE,GAAG,CAAC,IAAI,CAAC,KAAG,GAAG,CAAC;AAAG,SAAOS,MAAG,MAAM,QAAQiE,EAAC,IAAEhC,IAAEzB,IAAE,CAAA,GAAG,CAAC,GAAE,EAAC,CAACjB,EAAC,GAAE0E,GAAE,IAAI,CAAAG,OAAG9E,IAAE8E,IAAEpE,EAAC,CAAC,EAAC,CAAC,IAAEiE,MAAG,OAAOA,MAAG,WAASzD,IAAEA,IAAE,CAAA,GAAG,CAAC,GAAElB,IAAE2E,IAAEjE,IAAET,EAAC,CAAC,IAAE0C,IAAEzB,IAAE,CAAA,GAAG,CAAC,GAAE,EAAC,CAACjB,EAAC,GAAE0E,GAAC,CAAC;AAAC,GAAE,CAAA,CAAE;AAA73B,IAA+3BK,MAAE,OAAG,EAAE,OAAO,CAACtE,IAAE,EAAC,KAAI,GAAE,MAAK,GAAE,QAAO,EAAC,MAAI;AAAC,MAAG,CAAC;AAAE,WAAOA;AAAE,MAAG,CAACiE,EAAC,IAAEI,IAAE,CAAC,GAAE9E,KAAE0C,IAAEzB,IAAE,CAAA,GAAGR,GAAEiE,EAAC,KAAG,CAAA,CAAE,GAAE,EAAC,CAAC,CAAC,GAAE,EAAC,CAAC;AAAE,SAAOhC,IAAEzB,IAAE,CAAA,GAAGR,EAAC,GAAE,EAAC,CAACiE,EAAC,GAAE1E,GAAC,CAAC;AAAC,GAAE,CAAA,CAAE;AAA//B,IAAigCgF,MAAE,OAAM,MAAG;AAAC,MAAG;AAAC,QAAIvE,KAAE,MAAM,QAAQ,IAAI,EAAE,IAAI,OAAG;AAAC,UAAIiE,KAAE,GAAE,EAAC,QAAO,EAAC,IAAEA,IAAE,IAAEH,IAAEG,IAAE,CAAC,QAAQ,CAAC;AAAE,aAAO,IAAI,QAAQ,OAAM1E,OAAG;AAAC,YAAI6E;AAAE,YAAG;AAAC,UAAAA,KAAE,MAAM,EAAC;QAAE,SAAOnE,IAAE;AAACK,cAAE,MAAM,4CAA4C,EAAE,MAAM,QAAQ,EAAE,GAAG,WAAW,GAAEA,IAAE,MAAML,EAAC;QAAC;AAAC,QAAAV,GAAE0C,IAAEzB,IAAE,EAAC,QAAO,EAAC,GAAE,CAAC,GAAE,EAAC,MAAK4D,GAAC,CAAC,CAAC;MAAC,CAAC;IAAC,CAAC,CAAC;AAAE,WAAOE,IAAEtE,EAAC;EAAC,SAAOA,IAAE;AAACM,QAAE,MAAMN,EAAC;EAAC;AAAC,SAAM,CAAA;AAAE;AAAn0C,IAAq0CwE,MAAE,OAAG,CAAAxE,OAAG;AAAC,MAAG;AAAC,QAAG,OAAOA,MAAG;AAAS,aAAOA,OAAI;AAAE,QAAG,OAAOA,MAAG;AAAS,aAAOA,GAAE,KAAK,CAAC;EAAC,SAAO,GAAE;AAACM,QAAE,MAAM,uBAAuB;EAAC;AAAC,SAAM;AAAE;AAA98C,IAAg9CmE,MAAE,CAAC,GAAEzE,OAAI;AAAC,MAAI,IAAE;AAAG,MAAG;AAAC,QAAE,OAAO,KAAK,CAAC,EAAE,OAAO,OAAG,EAAE,CAAC,MAAI,MAAM,EAAE,MAAM,OAAG,EAAE,CAAC,MAAIA,GAAE,CAAC,CAAC;EAAC,SAAO,GAAE;EAAA;AAAE,SAAO;AAAC;AAAE,IAAI0E,MAAE,MAAI,KAAG,KAAG;AAAhB,IAAmB,IAAE,MAAK;EAAC,YAAY1E,IAAE;AAAC,SAAK,WAAS;AAAE,SAAK,aAAW,CAAA;AAAG,SAAK,eAAa2E,SAAC;AAAG,SAAK,SAAOA,SAAC;AAAG,SAAK,YAAUA,SAAE,KAAE;AAAE,SAAK,WAAS,oBAAI;AAAI,SAAK,UAAQ,EAAC,WAAU,KAAK,UAAU,WAAU,WAAU,CAAC3E,IAAE,MAAI;AAAC,UAAG,EAAC,gBAAe,EAAC,IAAE4E,gBAAE,KAAK,MAAM,GAAE,IAAE,MAAM,KAAK,KAAK,QAAQ,EAAE,OAAO,CAAAX,OAAG;AAAC,YAAI1E,KAAEkF,IAAE,EAAC,QAAOJ,IAAErE,EAAC,EAAE,CAAC,GAAE,OAAM,EAAC,GAAEiE,EAAC;AAAE,eAAO,MAAI1E,KAAEA,MAAGkF,IAAE,EAAC,QAAOJ,IAAE,CAAC,EAAE,CAAC,GAAE,OAAM,EAAC,GAAEJ,EAAC,IAAG1E;MAAC,CAAC,EAAE,IAAI,CAAC,EAAC,SAAQ0E,GAAC,MAAIA,EAAC;AAAE,aAAO,QAAQ,IAAI,CAAC;IAAC,GAAE,KAAI,MAAIW,gBAAE,KAAK,SAAS,EAAC;AAAE,SAAK,yBAAuBD,SAAE,CAAA,CAAE;AAAE,SAAK,kBAAgB,EAAC,WAAU,KAAK,uBAAuB,WAAU,KAAI,MAAIC,gBAAE,KAAK,eAAe,EAAC;AAAE,SAAK,sBAAoBD,SAAE,CAAA,CAAE;AAAE,SAAK,eAAa,EAAC,WAAU,KAAK,oBAAoB,WAAU,KAAI,MAAIC,gBAAE,KAAK,YAAY,EAAC;AAAE,SAAK,UAAQ3C,IAAEzB,IAAE,CAAA,GAAGqE,QAAE,CAAC,KAAK,QAAO,KAAK,mBAAmB,GAAE,CAAC,CAAC7E,IAAE,CAAC,MAAI;AAAC,UAAG,CAACA;AAAE,eAAM,CAAA;AAAG,UAAG,EAAC,SAAQ,IAAE,CAAA,EAAE,IAAEA,IAAE,IAAE,EAAE,IAAI,CAAC,EAAC,QAAOT,GAAC,MAAIA,EAAC,GAAE0E,KAAE,OAAO,KAAK,CAAC,EAAE,IAAI,CAAA1E,OAAGA,EAAC;AAAE,aAAO,MAAM,KAAK,oBAAI,IAAI,CAAC,GAAG8E,IAAE,GAAG,CAAC,GAAE,GAAGA,IAAE,GAAGJ,EAAC,CAAC,CAAC,CAAC;IAAC,GAAE,CAAA,CAAE,CAAC,GAAE,EAAC,KAAI,MAAIW,gBAAE,KAAK,OAAO,EAAC,CAAC;AAAE,SAAK,iBAAeD,SAAC;AAAG,SAAK,gBAAcE,QAAE,CAAC,KAAK,gBAAe,KAAK,YAAY,GAAE,CAAC,CAAC7E,IAAE,CAAC,GAAE,MAAI;AAAC,UAAI,GAAEiE;AAAE,MAAAjE,OAAI,UAAQ,MAAI,UAAQ,EAAEA,SAAM,IAAE4E,gBAAE,KAAK,aAAa,MAAI,OAAK,SAAO,EAAE,CAAC,MAAI,QAAMX,KAAEW,gBAAE,KAAK,aAAa,MAAI,OAAK,SAAOX,GAAE,CAAC,QAAM3D,IAAE,MAAM,gCAAgC,GAAE,EAAE,CAACN,IAAE,CAAC,CAAC;IAAE,GAAE,CAAA,CAAE;AAAE,SAAK,eAAa2E,SAAC;AAAG,SAAK,SAAO,EAAC,WAAU,KAAK,aAAa,WAAU,UAAS,KAAK,aAAa,KAAI,KAAI,KAAK,eAAe,KAAI,QAAO,KAAK,eAAe,QAAO,KAAI,MAAIC,gBAAE,KAAK,MAAM,EAAC;AAAE,SAAK,cAAYC,QAAE,CAAC,KAAK,QAAO,KAAK,cAAa,KAAK,mBAAmB,GAAE,CAAC,CAAC7E,IAAE,GAAE,CAAC,GAAE,MAAI;AAAC4E,sBAAE,KAAK,WAAW,KAAG,EAAE5E,OAAI,UAAQ,MAAI,UAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,EAAE,MAAM;IAAC,CAAC;AAAE,SAAK,cAAY6E,QAAE,CAAC,KAAK,qBAAoB,KAAK,QAAO,KAAK,SAAS,GAAE,CAAC,CAAC7E,IAAE,GAAE,CAAC,GAAE,MAAI;AAAC,UAAIiE,KAAEjE,GAAE,CAAC;AAAE,MAAAiE,MAAG,OAAO,KAAKA,EAAC,EAAE,UAAQ,CAAC,KAAG,EAAEA,EAAC;IAAC,GAAE,CAAA,CAAE;AAAE,SAAK,IAAEhC,IAAEzB,IAAE,CAAA,GAAGqE,QAAE,CAAC,KAAK,QAAO,KAAK,WAAW,GAAE,OAAG;AAAC,UAAG,CAACZ,EAAC,IAAE,GAAE1E,KAAE0E,IAAE,EAAC,QAAOjE,IAAE,gBAAe,EAAC,IAAET,IAAE,IAAEuE,IAAEvE,IAAE,CAAC,UAAS,gBAAgB,CAAC;AAAE,aAAM,CAAC6E,OAAKnE,OAAIkE,IAAE3D,IAAE,EAAC,QAAOR,IAAE,KAAIoE,IAAE,QAAOnE,IAAE,cAAa,KAAK,aAAa,IAAG,GAAG,QAAO,KAAK,OAAO,IAAG,GAAG,gBAAe,EAAC,GAAE,EAAE,eAAe,eAAe,IAAE,EAAC,eAAc,EAAE,cAAa,IAAE,CAAA,CAAE,CAAC;IAAC,CAAC,CAAC,GAAE,EAAC,KAAI,CAACD,OAAK,MAAI4E,gBAAE,KAAK,CAAC,EAAE5E,IAAE,GAAG,CAAC,EAAC,CAAC;AAAE,SAAK,IAAEiC,IAAEzB,IAAE,CAAA,GAAGqE,QAAE,CAAC,KAAK,QAAO,KAAK,YAAY,GAAE,CAAAZ,OAAG;AAAC,UAAG,CAAC1E,IAAE,GAAG6E,EAAC,IAAEH,IAAEhE,KAAEV,IAAE,EAAC,QAAOS,IAAE,gBAAe,EAAC,IAAEC,IAAE,IAAE6D,IAAE7D,IAAE,CAAC,UAAS,gBAAgB,CAAC,GAAE,CAAC,CAAC,IAAEmE;AAAE,aAAM,CAACrC,IAAE,MAAK+C,OAAIX,IAAE3D,IAAE,EAAC,QAAOR,IAAE,KAAI,GAAE,QAAO8E,IAAE,cAAa,GAAE,QAAO/C,IAAE,gBAAe,EAAC,GAAE,EAAE,eAAe,eAAe,IAAE,EAAC,eAAc,EAAE,cAAa,IAAE,CAAA,CAAE,CAAC;IAAC,CAAC,CAAC,GAAE,EAAC,KAAI,CAAC/B,IAAE,MAAK,MAAI4E,gBAAE,KAAK,CAAC,EAAE5E,IAAE,GAAE,GAAG,CAAC,EAAC,CAAC;AAAE,SAAK,YAAU,CAAAA,OAAG;AAAC,UAAG,EAAC,gBAAe,EAAC,IAAE4E,gBAAE,KAAK,MAAM,KAAG,CAAA,GAAG,IAAE5E,MAAG;AAAE,UAAG,CAAC;AAAE;AAAO,UAAI,IAAE,KAAK,QAAQ,IAAG;AAAG,aAAO,EAAE,KAAK,CAAAT,OAAG8E,IAAE,CAAC,EAAE,SAAS9E,EAAC,CAAC,KAAG,EAAE,KAAK,CAAAA,OAAG8E,IAAE,CAAC,EAAE,SAAS9E,EAAC,CAAC;IAAC;AAAE,SAAK,YAAU,CAAAS,OAAG;AAAC,UAAGA,MAAGA,OAAI4E,gBAAE,KAAK,cAAc;AAAE,eAAOtE,IAAE,MAAM,YAAYN,EAAC,WAAW,GAAE,KAAK,eAAe,IAAIA,EAAC,GAAE,KAAK,QAAQ,UAAUA,IAAE4E,gBAAE,KAAK,YAAY,CAAC;IAAC;AAAE,SAAK,WAAS,CAAA5E,OAAG;AAAC,UAAGA,OAAI4E,gBAAE,KAAK,YAAY,GAAE;AAACtE,YAAE,MAAM,YAAYN,EAAC,UAAU,GAAE,KAAK,aAAa,IAAIA,EAAC;AAAE,YAAI,IAAE4E,gBAAE,KAAK,cAAc;AAAE,eAAO,KAAK,QAAQ,UAAU,GAAE5E,EAAC;MAAC;IAAC;AAAE,SAAK,aAAW,OAAMA,OAAG;AAAC,YAAM,KAAK,aAAaA,EAAC;IAAC;AAAE,SAAK,sBAAoB,OAAMA,KAAE,KAAK,OAAO,IAAG,GAAG,IAAE4E,gBAAE,KAAK,YAAY,MAAI;AAAC,UAAI,IAAEA,gBAAE,KAAK,MAAM;AAAE,UAAG,CAAC,KAAG,CAAC5E;AAAE,eAAM,CAAA;AAAG,UAAI,IAAE,KAAK,aAAa,IAAG,GAAG,EAAC,SAAQiE,IAAE,gBAAe1E,KAAE,IAAG,OAAM6E,KAAEM,IAAC,IAAE,KAAG,CAAA,GAAGzE,KAAE,OAAO,MAAM,CAACmE,EAAC,IAAEM,MAAE,CAACN;AAAE,WAAK,WAAS,KAAK,IAAG,IAAGnE,KAAE,KAAK,aAAWK,IAAE,MAAM,mBAAmB,GAAE,KAAK,aAAW,CAAA,GAAG,KAAK,WAAS,MAAIA,IAAE,MAAM,0BAA0B,GAAE,KAAK,WAAS,KAAK,IAAG;AAAI,UAAG,CAACyB,IAAE,CAAC,IAAEsC,IAAErE,IAAET,EAAC,GAAEuF,KAAE,EAAE/C,EAAC,GAAE,IAAE,EAAE,CAAC,GAAE,KAAGkC,MAAG,CAAA,GAAI,IAAI,CAAAc,OAAG;AAAC,YAAIC,KAAED,IAAE,EAAC,QAAOhF,GAAC,IAAEiF,IAAE,IAAElB,IAAEkB,IAAE,CAAC,QAAQ,CAAC;AAAE,eAAO/C,IAAEzB,IAAE,CAAA,GAAG,CAAC,GAAE,EAAC,QAAO6D,IAAEtE,EAAC,EAAE,CAAC,EAAC,CAAC;MAAC,CAAC,EAAE,OAAO,CAAC,EAAC,QAAOA,GAAC,MAAI,CAACA,OAAIA,MAAG,CAAA,GAAI,KAAKyE,IAAE,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAC,KAAIzE,IAAE,QAAO,EAAC,MAAI,MAAIgC,OAAI,CAAC+C,MAAG,EAAE,KAAK,WAAW/C,EAAC,KAAG,CAAA,GAAI,SAAShC,EAAC,MAAIR,MAAG,MAAI,MAAI,CAAC,KAAG,EAAE,KAAK,WAAW,CAAC,KAAG,CAAA,GAAI,SAASQ,EAAC,EAAE;AAAE,UAAG,EAAE,QAAO;AAAC,aAAK,UAAU,IAAI,IAAE,GAAEO,IAAE,MAAM,0BAA0B;AAAE,YAAIP,KAAE,MAAMwE,IAAE,CAAC;AAAE,aAAK,UAAU,IAAI,KAAE;AAAE,YAAI,IAAE,OAAO,KAAKxE,EAAC,EAAE,OAAO,CAACiF,IAAEC,OAAIhD,IAAEzB,IAAE,CAAA,GAAGwE,EAAC,GAAE,EAAC,CAACC,EAAC,GAAE,OAAO,KAAKlF,GAAEkF,EAAC,CAAC,EAAC,CAAC,GAAE,CAAA,CAAE,GAAEF,KAAE,EAAE,OAAO,CAAC,EAAC,KAAIC,IAAE,QAAOC,GAAC,OAAK,EAAEA,EAAC,KAAG,CAAA,GAAI,KAAK,OAAG,GAAG,CAAC,GAAG,WAAWD,EAAC,CAAC,CAAC,EAAE,OAAO,CAACA,IAAE,EAAC,KAAIC,IAAE,QAAO,EAAC,MAAIhD,IAAEzB,IAAE,CAAA,GAAGwE,EAAC,GAAE,EAAC,CAAC,CAAC,GAAE,CAAC,GAAGA,GAAE,CAAC,KAAG,CAAA,GAAGC,EAAC,EAAC,CAAC,GAAE,CAAA,CAAE;AAAE,eAAM,CAAClF,IAAEgF,EAAC;MAAC;AAAC,aAAM,CAAA;IAAE;AAAE,SAAK,kBAAgB,CAAC/E,IAAE,MAAI;AAAC,UAAG,CAACA;AAAE;AAAO,UAAI,IAAE4E,gBAAE,KAAK,MAAM,GAAE,EAAC,YAAW,EAAC,IAAE,KAAG,CAAA;AAAGtE,UAAE,MAAM,wBAAwB;AAAE,UAAI2D,KAAE,OAAO,KAAKjE,MAAG,CAAA,CAAE;AAAE,WAAK,uBAAuB,OAAO,CAAAT,OAAG0E,GAAE,OAAO,CAACG,IAAEnE,OAAIgC,IAAEzB,IAAE,CAAA,GAAG4D,EAAC,GAAE,EAAC,CAACnE,EAAC,GAAEO,IAAEA,IAAE,CAAA,GAAG4D,GAAEnE,EAAC,KAAG,CAAA,CAAE,GAAED,GAAEC,EAAC,CAAC,EAAC,CAAC,GAAEV,EAAC,CAAC,GAAE,KAAK,oBAAoB,OAAO,CAAAA,OAAG0E,GAAE,OAAO,CAACG,IAAEnE,OAAI;AAAC,YAAI8B,KAAE,MAAG,IAAE/B,GAAEC,EAAC;AAAE,eAAO,OAAO,KAAG,eAAa,IAAE,EAAE,CAAC,KAAI,OAAO,KAAG,cAAY,MAAI,YAAU8B,KAAE,QAAIE,IAAEzB,IAAE,CAAA,GAAG4D,EAAC,GAAE,EAAC,CAACnE,EAAC,GAAEO,IAAEA,IAAE,CAAA,GAAG4D,GAAEnE,EAAC,KAAG,CAAA,CAAE,GAAE8B,KAAEzC,IAAE,GAAE,MAAI,gBAAgB,IAAE,CAAC,EAAC,CAAC;MAAC,GAAEC,EAAC,CAAC,GAAE0E,GAAE,QAAQ,CAAA1E,OAAG;AAAC,YAAI6E,KAAE,OAAO,KAAKpE,GAAET,EAAC,CAAC,EAAE,IAAI,CAAAU,OAAG,GAAGA,EAAC,GAAG,MAAM,GAAG,EAAE,CAAC,CAAC;AAAE,cAAImE,KAAE,EAAE7E,EAAC,IAAG,KAAK,WAAWA,EAAC,IAAE,MAAM,KAAK,oBAAI,IAAI,CAAC,GAAG,KAAK,WAAWA,EAAC,KAAG,CAAA,GAAG,GAAG6E,MAAG,CAAA,CAAE,CAAC,CAAC;MAAC,CAAC;IAAC;AAAE,SAAK,SAAO,OAAM,CAACpE,IAAE,CAAC,MAAI;AAAC,UAAI,IAAE,KAAK,UAAUA,EAAC,KAAG;AAAOM,UAAE,MAAM,8BAA8B,CAAC,iBAAiB,CAAC,UAAU;AAAE,UAAI,KAAG,YAAS;AAAC,YAAI2D,KAAE,MAAM,KAAK,oBAAoB,GAAE,CAAC;AAAE,QAAAA,GAAE,UAAQ,KAAK,gBAAgB,GAAGA,EAAC;MAAC,GAAC;AAAI,WAAK,SAAS,IAAI,EAAC,QAAO,GAAE,OAAM,GAAE,SAAQ,EAAC,CAAC,GAAE,EAAE,KAAK,MAAI;AAAC,aAAG,KAAK,OAAO,IAAG,MAAK,KAAG,KAAK,OAAO,SAAS,CAAC;MAAC,CAAC;IAAC;AAAE,SAAK,mBAAiB,CAACjE,IAAE,IAAE4E,gBAAE,KAAK,YAAY,KAAG,OAAK;AAAC,UAAI,IAAE,KAAK,UAAU5E,EAAC;AAAE,UAAG;AAAE,eAAO,KAAK,SAAS,CAAC,GAAE,KAAK,UAAU,CAAC,GAAE,KAAK,QAAQ,UAAU,GAAE,CAAC;IAAC;AAAE,SAAK,cAAc,UAAU,KAAK,MAAM,GAAE,KAAK,UAAU,UAAU,OAAM,MAAG;AAAC,WAAG,KAAK,SAAS,SAAO,MAAM,KAAK,QAAQ,UAAS,GAAG,KAAK,SAAS,MAAK,GAAGM,IAAE,MAAM,mCAAmC;IAAE,CAAC,GAAEN,MAAG,KAAK,WAAWA,EAAC;EAAC;EAAC,MAAM,aAAaA,IAAE;AAAC,QAAG,CAACA;AAAE,aAAOM,IAAE,MAAM,qBAAqB;AAAE,QAAI8D,KAAEpE,IAAE,EAAC,YAAW,GAAE,gBAAe,GAAE,cAAa,GAAE,KAAIiE,GAAC,IAAEG,IAAE7E,KAAEuE,IAAEM,IAAE,CAAC,cAAa,kBAAiB,gBAAe,KAAK,CAAC;AAAE,IAAAH,MAAGC,IAAEF,IAAEC,EAAC,CAAC,GAAE,CAAC,CAAC,IAAEI,IAAE,CAAC,GAAE,CAAC,CAAC,IAAEA,IAAE,CAAC,GAAE/D,IAAE,MAAM,iBAAiB,GAAE,KAAK,OAAO,IAAIE,IAAE,EAAC,YAAW,GAAE,gBAAe,GAAE,cAAa,EAAC,GAAEjB,EAAC,CAAC,GAAE,KAAG,KAAK,gBAAgB,CAAC,GAAE,KAAG,MAAM,KAAK,iBAAiB,CAAC;EAAC;AAAC;ACA3gQ,IAAI,IAAE,OAAO;AAAb,IAA4B,IAAE,OAAO;AAAiB,IAAI,IAAE,OAAO;AAA0B,IAAI,IAAE,OAAO;AAAsB,IAAI,IAAE,OAAO,UAAU;AAAvB,IAAsC,IAAE,OAAO,UAAU;AAAqB,IAAIQ,MAAE,CAACC,IAAE,GAAE,MAAI,KAAKA,KAAE,EAAEA,IAAE,GAAE,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,IAAEA,GAAE,CAAC,IAAE;AAAtF,IAAwFiC,MAAE,CAACjC,IAAE,MAAI;AAAC,WAAQ,KAAK,MAAI,IAAE,CAAA;AAAI,MAAE,KAAK,GAAE,CAAC,KAAGD,IAAEC,IAAE,GAAE,EAAE,CAAC,CAAC;AAAE,MAAG;AAAE,aAAQ,KAAK,EAAE,CAAC;AAAE,QAAE,KAAK,GAAE,CAAC,KAAGD,IAAEC,IAAE,GAAE,EAAE,CAAC,CAAC;AAAE,SAAOA;AAAC;AAA1M,IAA4M,IAAE,CAACA,IAAE,MAAI,EAAEA,IAAE,EAAE,CAAC,CAAC;AAAE,IAAI,IAAE,CAACA,IAAE,MAAI;AAAC,MAAI,IAAE,CAAA;AAAG,WAAQT,MAAKS;AAAE,MAAE,KAAKA,IAAET,EAAC,KAAG,EAAE,QAAQA,EAAC,IAAE,MAAI,EAAEA,EAAC,IAAES,GAAET,EAAC;AAAG,MAAGS,MAAG,QAAM;AAAE,aAAQT,MAAK,EAAES,EAAC;AAAE,QAAE,QAAQT,EAAC,IAAE,KAAG,EAAE,KAAKS,IAAET,EAAC,MAAI,EAAEA,EAAC,IAAES,GAAET,EAAC;AAAG,SAAO;AAAC;AAAE,IAAI,IAAE,CAACS,IAAE,MAAI;AAAC,WAAQ,KAAK;AAAE,MAAEA,IAAE,GAAE,EAAC,KAAI,EAAE,CAAC,GAAE,YAAW,KAAE,CAAC;AAAC;AAAE,IAAI,IAAE,CAAA;AAAG,EAAE,GAAE,EAAC,KAAI,MAAI,GAAE,UAAS,MAAI,GAAE,MAAK,MAAI,GAAE,IAAG,MAAI,GAAE,IAAG,MAAI,GAAE,KAAI,MAAI,GAAE,IAAG,MAAI,GAAE,KAAI,MAAI,GAAE,IAAG,MAAI,GAAE,QAAO,MAAI,EAAC,CAAC;AAAE,IAAI,IAAE,CAACA,IAAE,MAAI;AAAC,MAAG,EAAC,kBAAiB,EAAC,IAAE,KAAG,CAAA,GAAG,EAAC,CAACA,EAAC,GAAET,GAAC,IAAE,KAAG,CAAA;AAAG,SAAOA,MAAG,CAAA;AAAE;AAAE,IAAI,IAAE,CAAC,EAAC,OAAMS,IAAE,SAAQ,IAAE,CAAA,GAAG,cAAa,IAAE,GAAE,OAAK,EAAE,KAAK,CAAC,EAAC,KAAIT,GAAC,MAAI,GAAGA,EAAC,GAAG,YAAW,MAAK,GAAGS,EAAC,GAAG,YAAW,CAAE,KAAG,CAAA,GAAI,SAAO;AAA9H,IAAgI,IAAE,CAAC,EAAC,OAAMA,IAAE,SAAQ,IAAE,CAAA,GAAG,cAAa,IAAE,GAAE,OAAK,EAAE,KAAK,CAAC,EAAC,KAAIT,GAAC,MAAI,GAAGA,EAAC,GAAG,YAAW,MAAK,GAAGS,EAAC,GAAG,YAAW,CAAE,KAAG,CAAA,GAAI,SAAO;AAA1P,IAA4P,IAAE,CAAC,EAAC,OAAMA,IAAE,SAAQ,IAAE,CAAA,GAAG,cAAa,IAAE,GAAE,OAAK,EAAE,KAAK,CAAC,GAAEQ,OAAI,CAAC,EAAE,MAAI,CAACA,GAAE,GAAG,EAAE,KAAK,CAAC,EAAC,KAAI,EAAC,MAAI,CAACR,KAAE,CAAC,CAAC,KAAG,CAAA,GAAI,SAAO;AAA3W,IAA6W,IAAE,CAAC,EAAC,OAAMA,IAAE,SAAQ,IAAE,CAAA,GAAG,cAAa,IAAE,GAAE,OAAK,EAAE,KAAK,CAAC,GAAEQ,OAAI,CAACA,GAAE,MAAI,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,EAAC,KAAI,EAAC,MAAI,CAACR,KAAE,CAAC,CAAC,KAAG,CAAA,GAAI,SAAO;AAA5d,IAA8d,IAAE,CAAC,EAAC,OAAMA,IAAE,SAAQ,IAAE,CAAA,GAAG,cAAa,IAAE,GAAE,MAAI,EAAE,EAAC,OAAMA,IAAE,SAAQ,GAAE,cAAa,EAAE,EAAC,OAAMA,IAAE,SAAQ,GAAE,cAAa,EAAC,CAAC,EAAC,CAAC;AAAplB,IAAslB,IAAE,CAAC,EAAC,OAAMA,IAAE,SAAQ,IAAE,CAAA,GAAG,cAAa,IAAE,GAAE,MAAI,EAAE,EAAC,OAAMA,IAAE,SAAQ,GAAE,cAAa,EAAE,EAAC,OAAMA,IAAE,SAAQ,GAAE,cAAa,EAAC,CAAC,EAAC,CAAC;AAA5sB,IAA8sB,IAAE,CAAC,EAAC,OAAMA,IAAE,OAAM,GAAE,cAAa,IAAE,IAAG,QAAOT,KAAE,IAAG,eAAc,EAAC,MAAI;AAAC,MAAG,CAACA;AAAE,WAAM;AAAG,MAAI0E,KAAE,EAAE,UAAS,CAAC,GAAE,EAAC,uBAAsBzD,GAAC,IAAEyD,IAAEU,KAAE,EAAEV,IAAE,CAAC,uBAAuB,CAAC,GAAE3D,MAAG,KAAG,OAAK,SAAO,EAAE,WAAS,CAAA,GAAG,EAAC,uBAAsByB,KAAEvB,MAAG,EAAC,IAAEF,IAAE,IAAE,EAAEA,IAAE,CAAC,uBAAuB,CAAC;AAAE,SAAO,IAAI,KAAK,aAAaf,IAAE0C,IAAE,EAAEA,IAAE,CAAA,GAAG0C,EAAC,GAAE,EAAC,uBAAsB5C,GAAC,CAAC,GAAE,CAAC,CAAC,EAAE,OAAO,CAAC/B,MAAG,CAAC,CAAC;AAAC;AAA9iC,IAAgjC,IAAE,CAAC,EAAC,OAAMA,IAAE,OAAM,GAAE,cAAa,IAAE,IAAG,QAAOT,KAAE,IAAG,eAAc,EAAC,MAAI;AAAC,MAAG,CAACA;AAAE,WAAM;AAAG,MAAIiB,KAAE,EAAE,EAAE,QAAO,CAAC,GAAE,CAAA,CAAE,GAAEmE,KAAE,GAAG,KAAG,OAAK,SAAO,EAAE,SAAO,CAAA,GAAG,CAAA,CAAE;AAAE,SAAO,IAAI,KAAK,eAAepF,IAAE0C,IAAEA,IAAE,CAAA,GAAGzB,EAAC,GAAEmE,EAAC,CAAC,EAAE,OAAO,CAAC3E,MAAG,CAAC,CAAC;AAAC;AAA9vC,IAAgwC0E,MAAE,CAAC,EAAC,KAAI,UAAS,YAAW,IAAG,GAAE,EAAC,KAAI,UAAS,YAAW,GAAE,GAAE,EAAC,KAAI,QAAO,YAAW,GAAE,GAAE,EAAC,KAAI,OAAM,YAAW,GAAE,GAAE,EAAC,KAAI,QAAO,YAAW,EAAC,GAAE,EAAC,KAAI,SAAQ,YAAW,KAAG,EAAC,GAAE,EAAC,KAAI,QAAO,YAAW,GAAE,CAAC;AAAr8C,IAAu8C,IAAE,CAAC1E,KAAE,IAAG,IAAE,OAAK,IAAI,OAAO,IAAIA,EAAC,KAAK,EAAE,KAAK,CAAC;AAAn/C,IAAq/C,IAAE,CAAAA,OAAG0E,IAAE,QAAQA,IAAE,KAAK,CAAC,EAAC,KAAI,EAAC,MAAI,EAAE,GAAE1E,EAAC,CAAC,CAAC;AAA7hD,IAA+hD,IAAE,CAACA,IAAE,MAAI0E,IAAE,OAAO,CAAC,CAAC,GAAEnF,EAAC,GAAE,EAAC,KAAI,GAAE,YAAWiB,GAAC,GAAEmE,OAAI;AAAC,MAAG,EAAEpF,IAAE,CAAC;AAAE,WAAM,CAAC,GAAEA,EAAC;AAAE,MAAG,CAACA,MAAGoF,OAAI,EAAEpF,EAAC,IAAE,GAAE;AAAC,QAAIwC,KAAE,KAAK,MAAM,IAAEvB,EAAC;AAAE,QAAG,CAACjB,MAAG,KAAK,IAAIwC,EAAC,KAAG,KAAG,MAAI;AAAO,aAAM,CAACA,IAAE,CAAC;EAAC;AAAC,SAAM,CAAC,GAAExC,EAAC;AAAC,GAAE,CAACS,IAAE,EAAE,CAAC;AAAltD,IAAotD,IAAE,CAAC,EAAC,OAAMA,IAAE,cAAa,IAAE,IAAG,QAAO,IAAE,IAAG,OAAMT,IAAE,eAAc,EAAC,MAAI;AAAC,MAAG,CAAC;AAAE,WAAM;AAAG,MAAI,IAAE,EAAE,OAAM,CAAC,GAAE,EAAC,QAAOiB,IAAE,SAAQmE,GAAC,IAAE,GAAE5C,KAAE,EAAE,GAAE,CAAC,UAAS,SAAS,CAAC,GAAE,KAAGxC,MAAG,OAAK,SAAOA,GAAE,QAAM,CAAA,GAAG,EAAC,QAAO,IAAEiB,MAAG,QAAO,SAAQyD,KAAEU,MAAG,OAAM,IAAE,GAAErE,KAAE,EAAE,GAAE,CAAC,UAAS,SAAS,CAAC,GAAE4E,KAAE,CAAClF,MAAG,CAAC,GAAEoE,KAAE,EAAEc,IAAE,CAAC;AAAE,SAAO,IAAI,KAAK,mBAAmB,GAAEjD,IAAE,EAAEA,IAAE,CAAA,GAAGF,EAAC,GAAE,EAAC,SAAQkC,GAAC,CAAC,GAAE3D,EAAC,CAAC,EAAE,OAAO,GAAG8D,EAAC;AAAC;AAAjjE,IAAmjE,IAAE,CAAC,EAAC,OAAMpE,IAAE,cAAa,IAAE,IAAG,QAAO,IAAE,IAAG,OAAMT,IAAE,eAAc,EAAC,MAAI;AAAC,MAAG,CAAC;AAAE,WAAM;AAAG,MAAI2F,KAAE,EAAE,YAAW,CAAC,GAAE,EAAC,OAAM1E,IAAE,UAASmE,GAAC,IAAEO,IAAEnD,KAAE,EAAEmD,IAAE,CAAC,SAAQ,UAAU,CAAC,GAAEd,MAAG7E,MAAG,OAAK,SAAOA,GAAE,aAAW,CAAA,GAAG,EAAC,OAAM,IAAEiB,MAAG,GAAE,UAASyD,KAAEU,GAAC,IAAEP,IAAE9D,KAAE,EAAE8D,IAAE,CAAC,SAAQ,UAAU,CAAC;AAAE,SAAO,IAAI,KAAK,aAAa,GAAEnC,IAAE,EAAEA,IAAE,CAAA,GAAGF,EAAC,GAAE,EAAC,OAAM,YAAW,UAASkC,GAAC,CAAC,GAAE3D,EAAC,CAAC,EAAE,OAAO,KAAGN,MAAG,EAAE;AAAC;AAAE,IAAI,IAAE,CAAAA,OAAG,OAAOA,MAAG,YAAU,sBAAsB,KAAKA,EAAC;AAAzD,IAA2D,IAAE,CAAAA,OAAG,OAAOA,MAAG,WAASA,GAAE,QAAQ,kBAAiB,EAAE,IAAEA;AAAlH,IAAoH,IAAE,CAAC,EAAC,OAAMA,IAAE,OAAM,GAAE,SAAQ,GAAE,eAAcT,IAAE,QAAO,EAAC,MAAI,GAAGS,EAAC,GAAG,QAAQ,8BAA6B,CAAAQ,OAAG;AAAC,MAAImE,KAAE,EAAE,GAAGnE,GAAE,MAAM,2CAA2C,CAAC,EAAE,GAAEuB,KAAE,KAAG,OAAK,SAAO,EAAE4C,EAAC,GAAE,CAAA,EAAE,IAAE,EAAE,IAAEnE,GAAE,MAAM,6EAA6E,KAAG,CAAA;AAAG,MAAE,MAAI,KAAG,OAAK,SAAO,EAAE,YAAU;AAAG,MAAG,CAAA,EAAEyD,KAAE,EAAE,IAAEzD,GAAE,MAAM,sFAAsF,KAAG,CAAA;AAAG,MAAGuB,OAAI,UAAQkC,OAAI;AAAK,WAAO;AAAE,MAAI3D,KAAE,CAAC,CAAC2D,IAAE,EAAC,iBAAgBiB,GAAC,IAAE3F,MAAG,CAAA,GAAG6E,KAAEnC,IAAEA,IAAE,CAAA,GAAG,CAAC,GAAEiD,MAAG,CAAA,CAAE;AAAE,EAAAjB,KAAE,OAAO,KAAKG,EAAC,EAAE,SAASH,EAAC,IAAEA,KAAE;AAAK,MAAI,IAAEG,GAAEH,EAAC,GAAE,KAAGzD,GAAE,MAAM,iCAAiC,KAAG,CAAA,GAAI,OAAO,CAAC,GAAE,GAAE2E,OAAI;AAAC,QAAGA,KAAE,GAAE;AAAC,UAAIF,KAAE,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC,GAAG,KAAI,CAAE,GAAE,IAAE,GAAG,EAAE,MAAM,oBAAoB,CAAC,GAAG,UAAS;AAAG,UAAGA,MAAGA,OAAI,aAAW;AAAE,eAAM,CAAC,GAAG,GAAE,EAAC,KAAIA,IAAE,OAAM,EAAC,CAAC;IAAC;AAAC,WAAO;EAAC,GAAE,CAAA,CAAE;AAAE,SAAM,CAAC3E,MAAG,CAAC,EAAE,SAAOyB,KAAE,EAAE,EAAC,OAAMA,IAAE,SAAQ,GAAE,OAAM,GAAE,cAAa,GAAE,QAAO,GAAE,eAAcxC,GAAC,CAAC;AAAC,CAAC;AAAlgC,IAAogC,IAAE,CAAC,EAAC,OAAMS,IAAE,OAAM,GAAE,SAAQ,GAAE,eAAcT,IAAE,QAAO,EAAC,MAAI;AAAC,MAAG,EAAES,EAAC,GAAE;AAAC,QAAIQ,KAAE,EAAE,EAAC,OAAMR,IAAE,SAAQ,GAAE,OAAM,GAAE,eAAcT,IAAE,QAAO,EAAC,CAAC;AAAE,WAAO,EAAE,EAAC,OAAMiB,IAAE,SAAQ,GAAE,OAAM,GAAE,eAAcjB,IAAE,QAAO,EAAC,CAAC;EAAC;AAAM,WAAO,EAAES,EAAC;AAAC;AAArtC,IAAutC,IAAE,CAAAA,QAAI,EAAC,OAAM,CAAC,GAAE,CAAC,GAAET,EAAC,GAAE,GAAEiB,QAAK,KAAG,QAAM,EAAE,WAAS,MAAI,WAAS,IAAE,EAAE,UAAS,MAAI,WAAS,IAAEA,KAAG,EAAE,EAAC,OAAM,GAAE,SAAQ,GAAE,OAAMjB,IAAE,eAAcS,IAAE,QAAO,EAAC,CAAC,GAAE;AAA52C,IAA+2C,KAAG;ACA7kJ,IAAI,IAAE,OAAO;AAAb,IAA4B,IAAE,OAAO;AAAiB,IAAI,IAAE,OAAO;AAA0B,IAAI,IAAE,OAAO;AAAsB,IAAI,IAAE,OAAO,UAAU;AAAvB,IAAsC,IAAE,OAAO,UAAU;AAAqB,IAAI,IAAE,CAAC,GAAE,GAAE,MAAI,KAAK,IAAE,EAAE,GAAE,GAAE,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,IAAE,EAAE,CAAC,IAAE;AAAtF,IAAwF,IAAE,CAAC,GAAE,MAAI;AAAC,WAAQ,KAAK,MAAI,IAAE,CAAA;AAAI,MAAE,KAAK,GAAE,CAAC,KAAG,EAAE,GAAE,GAAE,EAAE,CAAC,CAAC;AAAE,MAAG;AAAE,aAAQ,KAAK,EAAE,CAAC;AAAE,QAAE,KAAK,GAAE,CAAC,KAAG,EAAE,GAAE,GAAE,EAAE,CAAC,CAAC;AAAE,SAAO;AAAC;AAA1M,IAA4M,IAAE,CAAC,GAAE,MAAI,EAAE,GAAE,EAAE,CAAC,CAAC;AAAE,IAAI,IAAE,CAAC,GAAE,MAAI;AAAC,MAAI,IAAE,CAAA;AAAG,WAAQ,KAAK;AAAE,MAAE,KAAK,GAAE,CAAC,KAAG,EAAE,QAAQ,CAAC,IAAE,MAAI,EAAE,CAAC,IAAE,EAAE,CAAC;AAAG,MAAG,KAAG,QAAM;AAAE,aAAQ,KAAK,EAAE,CAAC;AAAE,QAAE,QAAQ,CAAC,IAAE,KAAG,EAAE,KAAK,GAAE,CAAC,MAAI,EAAE,CAAC,IAAE,EAAE,CAAC;AAAG,SAAO;AAAC;AAAoF,IAAI,IAAE,OAAG;AAAC,MAAI,IAAE,GAAE,EAAC,eAAc,IAAE,CAAA,EAAE,IAAE,GAAE,IAAE,EAAE,GAAE,CAAC,eAAe,CAAC;AAAE,SAAO,EAAE,EAAE,CAAA,GAAG,CAAC,GAAE,EAAC,QAAO+D,GAAE,CAAC,EAAC,CAAC;AAAC;AAAjG,IAAmG,IAAE,cAAca,EAAC;EAAC,YAAY,GAAE;AAAC,UAAM,KAAG,EAAE,CAAC,CAAC;AAAE,SAAK,aAAW,CAAAQ,OAAG,MAAM,aAAa,EAAEA,EAAC,CAAC;EAAC;AAAC;AAA/L,IAAiM,IAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACWp2B,IAAM,eAAe;EACjB,IAAI,EAAC,GAAG,MAAM,GAAG,GAAE;EACnB,IAAI,EAAC,GAAG,MAAM,GAAG,GAAE;EACnB,IAAI,EAAC,GAAG,MAAM,GAAG,QAAO;EACxB,WAAW,EAAC,GAAG,MAAM,GAAG,QAAO;EAC/B,WAAW,EAAC,GAAG,MAAM,GAAG,QAAO;EAC/B,IAAI,EAAC,GAAG,MAAM,GAAG,GAAE;;AASvB,IAAM,SAAyB;EAC3B,YAAY;EACZ;;AASG,IAAM,mBAAmB,CAAC,UAA4C,CAAA,MAAgB;AACzF,QAAM,SAAyB;IAC3B,GAAG;IACH,GAAG;;AAEP,SAAO,IAAIC,EAAK,MAAM;AAC1B;AAEoE,IAAIA,EAAK,MAAM;ACJtE,IAAA,4BAA4B;EACrC,IAAI;EACJ,SAAS;;AAGb,IAAM,uBAAuB,OACxB,UAAU,aAAa,UAAU,UAAU,SACtC,UAAU,UAAU,CAAC,IACrB,UAAU,YAAY,MAC1B,MAAM,GAAG,EAAE,CAAC;AAEZ,IAAO,cAAP,cAA2B,sBAAqB;EAalD,YAAY,UAA8B,2BAAyB;AAC/D,UAAK;AAXF,SAAS,YAAG;AAMZ,SAAW,cAAG;AACd,SAAO,UAAG;AACV,SAAO,UAAG;AA+DjB,SAAoB,uBAAG,CAACC,aACpB,mBAAmB,OAAO,CAAC,YAAY,CAAC,GAAG,SAASA,QAAO,CAAC;AA5D5D,SAAK,UAAU;AACf,QAAI,OAAO,aAAa,aAAa;AACjC,WAAK,WAAU;IAClB;;EAGL,aAAU;AAEN,QAAI,KAAK,aAAa;AAClB;IACH;AACD,UAAM,EAAC,QAAO,IAAI;AAElB,SAAK,UAAU,SAAS,cAAc,KAAK;AAC3C,SAAK,YAAY,QAAQ,MAAM,0BAA0B;AACzD,SAAK,QAAQ,KAAK,KAAK;AACvB,SAAK,SAAS,KAAK,QAAQ,aAAa,EAAC,MAAM,SAAQ,CAAC;AAExD,SAAK,OAAO,iBAAgB;AAC5B,QAAIhC,QAAO,qBAAoB;AAC/B,SAAK,UAAU,QAAQ,WAAW;AAClC,UAAM,mBAAmB9B,gBAAIC,UAAQ,EAAE;AACvC,QAAI,kBAAkB;AAClB,MAAA6B,QAAO;IACV;AACD,QAAI,QAAQ,YAAY,QAAW;AAC/B,WAAK,UAAU,QAAQ;IAC1B;AACD,SAAK,IAAI,uBAAuBA,KAAI,EAAE;AACtC7B,eAAS,OAAO,CAAC,aAAa,EAAC,GAAG,SAAS,UAAU6B,MAAI,EAAE;AAC3D,SAAK,KAAK,iBAAiBA,KAAI;AAC/B,QAAI,SAAS,eAAe,cAAc,SAAS,eAAe,eAAe;AAE7E,WAAK,oBAAmB;IAC3B,OAAM;AAEH,eAAS,iBAAiB,oBAAoB,MAAM,KAAK,oBAAmB,CAAE;IACjF;AACD,SAAK,cAAc;;EAGvB,sBAAmB;AACf,UAAM,WAAW,SAAS,eAAe,KAAK,SAAS;AACvD,QAAI,CAAC,KAAK,WAAW,CAAC,KAAK,QAAQ;AAC/B,YAAM,IAAI,MAAM,uEAAuE;IAC1F;AACD,QAAI,CAAC,UAAU;AACX,eAAS,KAAK,OAAO,KAAK,OAAO;AACjC,eAAS,oBAAoB,oBAAoB,MAAM,KAAK,oBAAmB,CAAE;AACjF,UAAI,IAAI;QACJ,QAAQ,KAAK;QACb,OAAO;UACH,MAAM,KAAK;QACd;MACJ,CAAA;IACJ;;EAOL,OAAO,MAAW;AACd,QAAI,KAAK,SAAS;AAEd,cAAQ,IAAI,qBAAqB,GAAG,IAAI;IAC3C;;EAGL,MAAM,MAAM,SAAqB;AAC7B,SAAK,IAAI,SAAS,OAAO;AACzB,WAAO,IAAI,MAAS;AACpB,WAAO,KAAK,OAAO;AACnB,UAAMgC,WAAU,WACZ,IAAI,QAAoC,CAAC,SAAS,WAC9C,aAAa,IAAI;MACb;MACA;KACH,CAAC,CACL;AAEL,SAAK,qBAAqBA,SAAQ,MAAM;AACxC,iBAAa,IAAI,OAAO;AACxB,UAAMA;AACN,QAAI,KAAK,SAAS;AACd,aAAO,IAAI,KAAK;IACnB;AACD,WAAOA;;EAGX,MAAM,QAAQzD,QAAY;AAEtB,UAAM,eAAeA,kBAAiB;AACtC,UAAM,WAAW,gBAAgBA,OAAM,WAAW;AAClD,SAAK,IAAI,WAAW;MAChB;MACA;MACA,OAAAA;IACH,CAAA;AAED,QAAI,UAAU;AACV;IACH;AACD,QAAI,KAAK,SAAS;AACd,aAAO,IAAI,KAAK;IACnB,OAAM;AAEH,aAAO,IAAI,IAAI;AAEf,mBAAa,IAAI,OAAOA,MAAK,CAAC;AAE9B,aAAO,KAAK,OAAO;IACtB;;EAGL,MAAM,gBACF,SAA6B;AAE7B,SAAK,IAAI,mBAAmB,OAAO;AAGnC,WAAO,IAAI,IAAI;AAGf,WAAO,KAAK,gBAAgB;AAE5B,UAAMyD,WAAU,WACZ,IAAI,QAA8C,CAAC,SAAS,WACxD,uBAAuB,IAAI;MACvB;MACA;KACH,CAAC,CACL;AAEL,SAAK,qBAAqBA,SAAQ,MAAM;AACxC,2BAAuB,IAAI,OAAO;AAElC,WAAOA;;EAGX,MAAM,0BAAuB;AACzB,SAAK,IAAI,yBAAyB;AAGlC,WAAO,IAAI,KAAK;AAEhB,eAAU;;EAGd,MAAM,UAAO;AACT,SAAK,IAAI,SAAS;AAElB,WAAO,IAAI,IAAI;AAEf,UAAM,OAAO,CAAC,aAAa;MACvB,GAAG;MACH,OAAO,KAAK,KAAK,EAAE,IAAI,eAAe,EAAC,SAAS,QAAO,CAAC;MACxD,UAAU;IACb,EAAC;AAEF,WAAO,KAAK,OAAO;;EAGvB,MAAM,kBAAe;AACjB,SAAK,IAAI,eAAe;AAExB,WAAO,IAAI,KAAK;AAEhB,eAAU;;EAGd,MAAM,aAAU;AACZ,SAAK,IAAI,YAAY;AAErB,QAAI,CAAC,KAAK,SAAS;AACf,aAAO,IAAI,IAAI;IAClB;AAED,UAAM,OAAO,CAACrF,QAAO;MACjB,GAAGA;MACH,OAAO,KAAK,KAAK,EAAE,IAAI,kBAAkB,EAAC,SAAS,WAAU,CAAC;MAC9D,UAAU;IACb,EAAC;AAEF,WAAO,KAAK,UAAU;;EAG1B,MAAM,qBAAkB;AACpB,SAAK,IAAI,kBAAkB;AAE3B,eAAU;AAEV,WAAO,IAAI,KAAK;;EAGpB,MAAM,SAAM;AACR,SAAK,IAAI,QAAQ;;EAGrB,MAAM,iBAAc;AAChB,SAAK,IAAI,gBAAgB;;EAG7B,MAAM,cAAW;AACb,SAAK,IAAI,aAAa;;EAG1B,MAAM,sBAAmB;AACrB,SAAK,IAAI,qBAAqB;;EAGlC,OAAO,MAAgB;AACnB,SAAK,IAAI,UAAU,IAAI;AAEvB,QAAI,CAAC,KAAK,WAAY,KAAK,WAAW,CAAC,KAAK,UAAW;AACnD,aAAO,IAAI,IAAI;AAEf,aAAO,KAAK,QAAQ;IACvB;AAED,UAAMqF,WAAU,WACZ,IAAI,QAAoC,CAAC,SAAS,WAAU;AACxD,aAAO,IAAI;QACP;QACA;QACA;MACH,CAAA;IACL,CAAC,GACD,CAAC,aAAY;AACT,YAAM;IACV,CAAC;AAGL,SAAK,qBAAqBA,SAAQ,MAAM;AAExC,WAAOA;;EAGX,OAAO,SAAe;AAElB,QAAI,CAAC,KAAK,SAAS;AACf,aAAO,IAAI,IAAI;IAClB;AAED,UAAM,OAAO,CAAC,aAAa;MACvB,GAAG;MACH,UAAU;IACb,EAAC;;;EAIN,UAAU,KAAa,SAAyC,WAAkB;AAC9E,QAAI,WAAW;AACX,aAAO,KAAK,KAAK,EAAE,IAAI,GAAG,SAAS,IAAI,GAAG,IAAI,OAAO;IACxD;AACD,WAAO,KAAK,KAAK,EAAE,IAAI,KAAK,OAAO;;EAGvC,gBAAgBC,eAAY;AACxB,SAAK,KAAK,gBAAgBA,aAAY;;;AAjRnC,YAAA,UAAU;", "names": ["x", "i", "now", "element", "text", "cancelable", "props", "active", "hash", "p", "t", "c", "detach", "config", "linear", "init", "d", "update", "n", "instance", "create_fragment", "append_styles", "Close", "Error", "<PERSON><PERSON>", "Settings", "Wallet", "ctx", "size", "create_if_block_2", "create_if_block_1", "create_if_block", "subscribe", "run", "get", "settings", "create_if_block_6", "create_if_block_5", "dispatch", "error", "create_if_block_4", "f", "loading", "u", "direction", "Steps", "chain", "cancel", "mode", "j", "math", "ECL", "Polynomial", "BitByte", "RSBlock", "BitBuffer", "util", "version", "create_if_block_3", "generateQr", "data", "_a", "$prompt", "languages", "lang", "plugin", "$chains", "H", "B", "S", "A", "N", "T", "C", "z", "s", "V", "F", "l", "h", "G", "E", "W", "O", "D", "m", "g", "v", "R", "$", "L", "P", "M", "q", "e", "i18n", "promise", "translations"]}