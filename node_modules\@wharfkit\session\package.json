{"name": "@wharfkit/session", "description": "Create account-based sessions, perform transactions, and allow users to login using Antelope-based blockchains.", "version": "1.6.0", "homepage": "https://github.com/wharfkit/session", "license": "BSD-3-<PERSON><PERSON>", "main": "lib/session.js", "module": "lib/session.m.js", "types": "lib/session.d.ts", "unpkg": "lib/session.bundle.js", "sideEffects": false, "files": ["lib/*", "src/*"], "scripts": {"prepare": "make"}, "dependencies": {"@wharfkit/abicache": "^1.2.1", "@wharfkit/antelope": "^1.0.11", "@wharfkit/common": "^1.2.0", "@wharfkit/signing-request": "^3.1.0", "pako": "^2.0.4", "tslib": "^2.1.0"}, "devDependencies": {"@babel/preset-env": "^7.20.2", "@rollup/plugin-alias": "^3.1.4", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-replace": "^5.0.1", "@rollup/plugin-typescript": "^10.0.1", "@rollup/plugin-virtual": "^2.0.3", "@types/chai": "^4.3.1", "@types/mocha": "^9.0.0", "@types/node": "^18.7.18", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "@wharfkit/contract": "^1.0.0", "@wharfkit/mock-data": "^1.2.1", "@wharfkit/transact-plugin-resource-provider": "^1.1.0", "@wharfkit/wallet-plugin-privatekey": "^1.1.0", "chai": "^4.3.4", "eslint": "^8.13.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0", "gh-pages": "^4.0.0", "mocha": "^10.0.0", "node-fetch": "^2.6.1", "nyc": "^15.1.0", "prettier": "^2.2.1", "rollup": "^2.70.2", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-terser": "^7.0.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.1.1", "typedoc": "^0.23.14", "typedoc-plugin-mermaid": "^1.10.0", "typescript": "^4.1.2", "yarn-deduplicate": "^6.0.2"}}