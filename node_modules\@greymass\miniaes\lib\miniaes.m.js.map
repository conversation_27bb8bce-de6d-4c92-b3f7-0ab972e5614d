{"version": 3, "file": "miniaes.m.js", "sources": ["../src/aes.asm.js", "../src/utils.ts", "../src/aes.ts", "../src/cbc.ts"], "sourcesContent": ["/* eslint-disable */\n/**\n * @file {@link http://asmjs.org Asm.js} implementation of the {@link https://en.wikipedia.org/wiki/Advanced_Encryption_Standard Advanced Encryption Standard}.\n * <AUTHOR> <PERSON> <v<PERSON><PERSON><PERSON>@gmail.com>\n * @license MIT\n */\n export var AES_asm = function () {\n  \"use strict\";\n\n  /**\n   * Galois Field stuff init flag\n   */\n  var ginit_done = false;\n\n  /**\n   * Galois Field exponentiation and logarithm tables for 3 (the generator)\n   */\n  var gexp3, glog3;\n\n  /**\n   * Init Galois Field tables\n   */\n  function ginit() {\n    gexp3 = [],\n      glog3 = [];\n\n    var a = 1, c, d;\n    for (c = 0; c < 255; c++) {\n      gexp3[c] = a;\n\n      // Multiply by three\n      d = a & 0x80, a <<= 1, a &= 255;\n      if (d === 0x80) a ^= 0x1b;\n      a ^= gexp3[c];\n\n      // Set the log table value\n      glog3[gexp3[c]] = c;\n    }\n    gexp3[255] = gexp3[0];\n    glog3[0] = 0;\n\n    ginit_done = true;\n  }\n\n  /**\n   * Galois Field multiplication\n   * @param {number} a\n   * @param {number} b\n   * @return {number}\n   */\n  function gmul(a, b) {\n    var c = gexp3[(glog3[a] + glog3[b]) % 255];\n    if (a === 0 || b === 0) c = 0;\n    return c;\n  }\n\n  /**\n   * Galois Field reciprocal\n   * @param {number} a\n   * @return {number}\n   */\n  function ginv(a) {\n    var i = gexp3[255 - glog3[a]];\n    if (a === 0) i = 0;\n    return i;\n  }\n\n  /**\n   * AES stuff init flag\n   */\n  var aes_init_done = false;\n\n  /**\n   * Encryption, Decryption, S-Box and KeyTransform tables\n   *\n   * @type {number[]}\n   */\n  var aes_sbox;\n\n  /**\n   * @type {number[]}\n   */\n  var aes_sinv;\n\n  /**\n   * @type {number[][]}\n   */\n  var aes_enc;\n\n  /**\n   * @type {number[][]}\n   */\n  var aes_dec;\n\n  /**\n   * Init AES tables\n   */\n  function aes_init() {\n    if (!ginit_done) ginit();\n\n    // Calculates AES S-Box value\n    function _s(a) {\n      var c, s, x;\n      s = x = ginv(a);\n      for (c = 0; c < 4; c++) {\n        s = ((s << 1) | (s >>> 7)) & 255;\n        x ^= s;\n      }\n      x ^= 99;\n      return x;\n    }\n\n    // Tables\n    aes_sbox = [],\n      aes_sinv = [],\n      aes_enc = [[], [], [], []],\n      aes_dec = [[], [], [], []];\n\n    for (var i = 0; i < 256; i++) {\n      var s = _s(i);\n\n      // S-Box and its inverse\n      aes_sbox[i] = s;\n      aes_sinv[s] = i;\n\n      // Ecryption and Decryption tables\n      aes_enc[0][i] = (gmul(2, s) << 24) | (s << 16) | (s << 8) | gmul(3, s);\n      aes_dec[0][s] = (gmul(14, i) << 24) | (gmul(9, i) << 16) | (gmul(13, i) << 8) | gmul(11, i);\n      // Rotate tables\n      for (var t = 1; t < 4; t++) {\n        aes_enc[t][i] = (aes_enc[t - 1][i] >>> 8) | (aes_enc[t - 1][i] << 24);\n        aes_dec[t][s] = (aes_dec[t - 1][s] >>> 8) | (aes_dec[t - 1][s] << 24);\n      }\n    }\n\n    aes_init_done = true;\n  }\n\n  /**\n   * Asm.js module constructor.\n   *\n   * <p>\n   * Heap buffer layout by offset:\n   * <pre>\n   * 0x0000   encryption key schedule\n   * 0x0400   decryption key schedule\n   * 0x0800   sbox\n   * 0x0c00   inv sbox\n   * 0x1000   encryption tables\n   * 0x2000   decryption tables\n   * 0x3000   reserved (future GCM multiplication lookup table)\n   * 0x4000   data\n   * </pre>\n   * Don't touch anything before <code>0x400</code>.\n   * </p>\n   *\n   * @alias AES_asm\n   * @class\n   * @param foreign - <i>ignored</i>\n   * @param buffer - heap buffer to link with\n   * @type any\n   */\n  var wrapper = function (foreign, buffer) {\n    // Init AES stuff for the first time\n    if (!aes_init_done) aes_init();\n\n    // Fill up AES tables\n    var heap = new Uint32Array(buffer);\n    heap.set(aes_sbox, 0x0800 >> 2);\n    heap.set(aes_sinv, 0x0c00 >> 2);\n    for (var i = 0; i < 4; i++) {\n      heap.set(aes_enc[i], (0x1000 + 0x400 * i) >> 2);\n      heap.set(aes_dec[i], (0x2000 + 0x400 * i) >> 2);\n    }\n\n    /**\n     * Calculate AES key schedules.\n     * @instance\n     * @memberof AES_asm\n     * @param {number} ks - key size, 4/6/8 (for 128/192/256-bit key correspondingly)\n     * @param {number} k0 - key vector components\n     * @param {number} k1 - key vector components\n     * @param {number} k2 - key vector components\n     * @param {number} k3 - key vector components\n     * @param {number} k4 - key vector components\n     * @param {number} k5 - key vector components\n     * @param {number} k6 - key vector components\n     * @param {number} k7 - key vector components\n     */\n    function set_key(ks, k0, k1, k2, k3, k4, k5, k6, k7) {\n      var ekeys = heap.subarray(0x000, 60),\n        dkeys = heap.subarray(0x100, 0x100 + 60);\n\n      // Encryption key schedule\n      ekeys.set([k0, k1, k2, k3, k4, k5, k6, k7]);\n      for (var i = ks, rcon = 1; i < 4 * ks + 28; i++) {\n        var k = ekeys[i - 1];\n        if ((i % ks === 0) || (ks === 8 && i % ks === 4)) {\n          k = aes_sbox[k >>> 24] << 24 ^ aes_sbox[k >>> 16 & 255] << 16 ^ aes_sbox[k >>> 8 & 255] << 8 ^ aes_sbox[k & 255];\n        }\n        if (i % ks === 0) {\n          k = (k << 8) ^ (k >>> 24) ^ (rcon << 24);\n          rcon = (rcon << 1) ^ ((rcon & 0x80) ? 0x1b : 0);\n        }\n        ekeys[i] = ekeys[i - ks] ^ k;\n      }\n\n      // Decryption key schedule\n      for (var j = 0; j < i; j += 4) {\n        for (var jj = 0; jj < 4; jj++) {\n          var k = ekeys[i - (4 + j) + (4 - jj) % 4];\n          if (j < 4 || j >= i - 4) {\n            dkeys[j + jj] = k;\n          } else {\n            dkeys[j + jj] = aes_dec[0][aes_sbox[k >>> 24]]\n              ^ aes_dec[1][aes_sbox[k >>> 16 & 255]]\n              ^ aes_dec[2][aes_sbox[k >>> 8 & 255]]\n              ^ aes_dec[3][aes_sbox[k & 255]];\n          }\n        }\n      }\n\n      // Set rounds number\n      asm.set_rounds(ks + 5);\n    }\n\n    // create library object with necessary properties\n    var stdlib = {Uint8Array: Uint8Array, Uint32Array: Uint32Array};\n\n    var asm = function (stdlib, foreign, buffer) {\n      \"use asm\";\n\n      var S0 = 0, S1 = 0, S2 = 0, S3 = 0,\n        I0 = 0, I1 = 0, I2 = 0, I3 = 0,\n        N0 = 0, N1 = 0, N2 = 0, N3 = 0,\n        M0 = 0, M1 = 0, M2 = 0, M3 = 0,\n        H0 = 0, H1 = 0, H2 = 0, H3 = 0,\n        R = 0;\n\n      var HEAP = new stdlib.Uint32Array(buffer),\n        DATA = new stdlib.Uint8Array(buffer);\n\n      /**\n       * AES core\n       * @param {number} k - precomputed key schedule offset\n       * @param {number} s - precomputed sbox table offset\n       * @param {number} t - precomputed round table offset\n       * @param {number} r - number of inner rounds to perform\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      function _core(k, s, t, r, x0, x1, x2, x3) {\n        k = k | 0;\n        s = s | 0;\n        t = t | 0;\n        r = r | 0;\n        x0 = x0 | 0;\n        x1 = x1 | 0;\n        x2 = x2 | 0;\n        x3 = x3 | 0;\n\n        var t1 = 0, t2 = 0, t3 = 0,\n          y0 = 0, y1 = 0, y2 = 0, y3 = 0,\n          i = 0;\n\n        t1 = t | 0x400, t2 = t | 0x800, t3 = t | 0xc00;\n\n        // round 0\n        x0 = x0 ^ HEAP[(k | 0) >> 2],\n          x1 = x1 ^ HEAP[(k | 4) >> 2],\n          x2 = x2 ^ HEAP[(k | 8) >> 2],\n          x3 = x3 ^ HEAP[(k | 12) >> 2];\n\n        // round 1..r\n        for (i = 16; (i | 0) <= (r << 4); i = (i + 16) | 0) {\n          y0 = HEAP[(t | x0 >> 22 & 1020) >> 2] ^ HEAP[(t1 | x1 >> 14 & 1020) >> 2] ^ HEAP[(t2 | x2 >> 6 & 1020) >> 2] ^ HEAP[(t3 | x3 << 2 & 1020) >> 2] ^ HEAP[(k | i | 0) >> 2],\n            y1 = HEAP[(t | x1 >> 22 & 1020) >> 2] ^ HEAP[(t1 | x2 >> 14 & 1020) >> 2] ^ HEAP[(t2 | x3 >> 6 & 1020) >> 2] ^ HEAP[(t3 | x0 << 2 & 1020) >> 2] ^ HEAP[(k | i | 4) >> 2],\n            y2 = HEAP[(t | x2 >> 22 & 1020) >> 2] ^ HEAP[(t1 | x3 >> 14 & 1020) >> 2] ^ HEAP[(t2 | x0 >> 6 & 1020) >> 2] ^ HEAP[(t3 | x1 << 2 & 1020) >> 2] ^ HEAP[(k | i | 8) >> 2],\n            y3 = HEAP[(t | x3 >> 22 & 1020) >> 2] ^ HEAP[(t1 | x0 >> 14 & 1020) >> 2] ^ HEAP[(t2 | x1 >> 6 & 1020) >> 2] ^ HEAP[(t3 | x2 << 2 & 1020) >> 2] ^ HEAP[(k | i | 12) >> 2];\n          x0 = y0, x1 = y1, x2 = y2, x3 = y3;\n        }\n\n        // final round\n        S0 = HEAP[(s | x0 >> 22 & 1020) >> 2] << 24 ^ HEAP[(s | x1 >> 14 & 1020) >> 2] << 16 ^ HEAP[(s | x2 >> 6 & 1020) >> 2] << 8 ^ HEAP[(s | x3 << 2 & 1020) >> 2] ^ HEAP[(k | i | 0) >> 2],\n          S1 = HEAP[(s | x1 >> 22 & 1020) >> 2] << 24 ^ HEAP[(s | x2 >> 14 & 1020) >> 2] << 16 ^ HEAP[(s | x3 >> 6 & 1020) >> 2] << 8 ^ HEAP[(s | x0 << 2 & 1020) >> 2] ^ HEAP[(k | i | 4) >> 2],\n          S2 = HEAP[(s | x2 >> 22 & 1020) >> 2] << 24 ^ HEAP[(s | x3 >> 14 & 1020) >> 2] << 16 ^ HEAP[(s | x0 >> 6 & 1020) >> 2] << 8 ^ HEAP[(s | x1 << 2 & 1020) >> 2] ^ HEAP[(k | i | 8) >> 2],\n          S3 = HEAP[(s | x3 >> 22 & 1020) >> 2] << 24 ^ HEAP[(s | x0 >> 14 & 1020) >> 2] << 16 ^ HEAP[(s | x1 >> 6 & 1020) >> 2] << 8 ^ HEAP[(s | x2 << 2 & 1020) >> 2] ^ HEAP[(k | i | 12) >> 2];\n      }\n\n      /**\n       * ECB mode encryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      // function _ecb_enc(x0, x1, x2, x3) {\n      //   x0 = x0 | 0;\n      //   x1 = x1 | 0;\n      //   x2 = x2 | 0;\n      //   x3 = x3 | 0;\n\n      //   _core(\n      //     0x0000, 0x0800, 0x1000,\n      //     R,\n      //     x0,\n      //     x1,\n      //     x2,\n      //     x3\n      //   );\n      // }\n\n      /**\n       * ECB mode decryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      // function _ecb_dec(x0, x1, x2, x3) {\n      //   x0 = x0 | 0;\n      //   x1 = x1 | 0;\n      //   x2 = x2 | 0;\n      //   x3 = x3 | 0;\n\n      //   var t = 0;\n\n      //   _core(\n      //     0x0400, 0x0c00, 0x2000,\n      //     R,\n      //     x0,\n      //     x3,\n      //     x2,\n      //     x1\n      //   );\n\n      //   t = S1, S1 = S3, S3 = t;\n      // }\n\n\n      /**\n       * CBC mode encryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      function _cbc_enc(x0, x1, x2, x3) {\n        x0 = x0 | 0;\n        x1 = x1 | 0;\n        x2 = x2 | 0;\n        x3 = x3 | 0;\n\n        _core(\n          0x0000, 0x0800, 0x1000,\n          R,\n          I0 ^ x0,\n          I1 ^ x1,\n          I2 ^ x2,\n          I3 ^ x3\n        );\n\n        I0 = S0,\n          I1 = S1,\n          I2 = S2,\n          I3 = S3;\n      }\n\n      /**\n       * CBC mode decryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      function _cbc_dec(x0, x1, x2, x3) {\n        x0 = x0 | 0;\n        x1 = x1 | 0;\n        x2 = x2 | 0;\n        x3 = x3 | 0;\n\n        var t = 0;\n\n        _core(\n          0x0400, 0x0c00, 0x2000,\n          R,\n          x0,\n          x3,\n          x2,\n          x1\n        );\n\n        t = S1, S1 = S3, S3 = t;\n\n        S0 = S0 ^ I0,\n          S1 = S1 ^ I1,\n          S2 = S2 ^ I2,\n          S3 = S3 ^ I3;\n\n        I0 = x0,\n          I1 = x1,\n          I2 = x2,\n          I3 = x3;\n      }\n\n      /**\n       * CFB mode encryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      // function _cfb_enc(x0, x1, x2, x3) {\n      //   x0 = x0 | 0;\n      //   x1 = x1 | 0;\n      //   x2 = x2 | 0;\n      //   x3 = x3 | 0;\n\n      //   _core(\n      //     0x0000, 0x0800, 0x1000,\n      //     R,\n      //     I0,\n      //     I1,\n      //     I2,\n      //     I3\n      //   );\n\n      //   I0 = S0 = S0 ^ x0,\n      //     I1 = S1 = S1 ^ x1,\n      //     I2 = S2 = S2 ^ x2,\n      //     I3 = S3 = S3 ^ x3;\n      // }\n\n\n      /**\n       * CFB mode decryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      // function _cfb_dec(x0, x1, x2, x3) {\n      //   x0 = x0 | 0;\n      //   x1 = x1 | 0;\n      //   x2 = x2 | 0;\n      //   x3 = x3 | 0;\n\n      //   _core(\n      //     0x0000, 0x0800, 0x1000,\n      //     R,\n      //     I0,\n      //     I1,\n      //     I2,\n      //     I3\n      //   );\n\n      //   S0 = S0 ^ x0,\n      //     S1 = S1 ^ x1,\n      //     S2 = S2 ^ x2,\n      //     S3 = S3 ^ x3;\n\n      //   I0 = x0,\n      //     I1 = x1,\n      //     I2 = x2,\n      //     I3 = x3;\n      // }\n\n      /**\n       * OFB mode encryption / decryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      // function _ofb(x0, x1, x2, x3) {\n      //   x0 = x0 | 0;\n      //   x1 = x1 | 0;\n      //   x2 = x2 | 0;\n      //   x3 = x3 | 0;\n\n      //   _core(\n      //     0x0000, 0x0800, 0x1000,\n      //     R,\n      //     I0,\n      //     I1,\n      //     I2,\n      //     I3\n      //   );\n\n      //   I0 = S0,\n      //     I1 = S1,\n      //     I2 = S2,\n      //     I3 = S3;\n\n      //   S0 = S0 ^ x0,\n      //     S1 = S1 ^ x1,\n      //     S2 = S2 ^ x2,\n      //     S3 = S3 ^ x3;\n      // }\n\n      /**\n       * CTR mode encryption / decryption\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      // function _ctr(x0, x1, x2, x3) {\n      //   x0 = x0 | 0;\n      //   x1 = x1 | 0;\n      //   x2 = x2 | 0;\n      //   x3 = x3 | 0;\n\n      //   _core(\n      //     0x0000, 0x0800, 0x1000,\n      //     R,\n      //     N0,\n      //     N1,\n      //     N2,\n      //     N3\n      //   );\n\n      //   N3 = (~M3 & N3) | M3 & (N3 + 1);\n      //     N2 = (~M2 & N2) | M2 & (N2 + ((N3 | 0) == 0));\n      //     N1 = (~M1 & N1) | M1 & (N1 + ((N2 | 0) == 0));\n      //     N0 = (~M0 & N0) | M0 & (N0 + ((N1 | 0) == 0));\n\n      //   S0 = S0 ^ x0;\n      //     S1 = S1 ^ x1;\n      //     S2 = S2 ^ x2;\n      //     S3 = S3 ^ x3;\n      // }\n\n      /**\n       * GCM mode MAC calculation\n       * @param {number} x0 - 128-bit input block vector\n       * @param {number} x1 - 128-bit input block vector\n       * @param {number} x2 - 128-bit input block vector\n       * @param {number} x3 - 128-bit input block vector\n       */\n      function _gcm_mac(x0, x1, x2, x3) {\n        x0 = x0 | 0;\n        x1 = x1 | 0;\n        x2 = x2 | 0;\n        x3 = x3 | 0;\n\n        var y0 = 0, y1 = 0, y2 = 0, y3 = 0,\n          z0 = 0, z1 = 0, z2 = 0, z3 = 0,\n          i = 0, c = 0;\n\n        x0 = x0 ^ I0,\n          x1 = x1 ^ I1,\n          x2 = x2 ^ I2,\n          x3 = x3 ^ I3;\n\n        y0 = H0 | 0,\n          y1 = H1 | 0,\n          y2 = H2 | 0,\n          y3 = H3 | 0;\n\n        for (; (i | 0) < 128; i = (i + 1) | 0) {\n          if (y0 >>> 31) {\n            z0 = z0 ^ x0,\n              z1 = z1 ^ x1,\n              z2 = z2 ^ x2,\n              z3 = z3 ^ x3;\n          }\n\n          y0 = (y0 << 1) | (y1 >>> 31),\n            y1 = (y1 << 1) | (y2 >>> 31),\n            y2 = (y2 << 1) | (y3 >>> 31),\n            y3 = (y3 << 1);\n\n          c = x3 & 1;\n\n          x3 = (x3 >>> 1) | (x2 << 31),\n            x2 = (x2 >>> 1) | (x1 << 31),\n            x1 = (x1 >>> 1) | (x0 << 31),\n            x0 = (x0 >>> 1);\n\n          if (c) x0 = x0 ^ 0xe1000000;\n        }\n\n        I0 = z0,\n          I1 = z1,\n          I2 = z2,\n          I3 = z3;\n      }\n\n      /**\n       * Set the internal rounds number.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} r - number if inner AES rounds\n       */\n      function set_rounds(r) {\n        r = r | 0;\n        R = r;\n      }\n\n      /**\n       * Populate the internal state of the module.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} s0 - state vector\n       * @param {number} s1 - state vector\n       * @param {number} s2 - state vector\n       * @param {number} s3 - state vector\n       */\n      function set_state(s0, s1, s2, s3) {\n        s0 = s0 | 0;\n        s1 = s1 | 0;\n        s2 = s2 | 0;\n        s3 = s3 | 0;\n\n        S0 = s0,\n          S1 = s1,\n          S2 = s2,\n          S3 = s3;\n      }\n\n      /**\n       * Populate the internal iv of the module.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} i0 - iv vector\n       * @param {number} i1 - iv vector\n       * @param {number} i2 - iv vector\n       * @param {number} i3 - iv vector\n       */\n      function set_iv(i0, i1, i2, i3) {\n        i0 = i0 | 0;\n        i1 = i1 | 0;\n        i2 = i2 | 0;\n        i3 = i3 | 0;\n\n        I0 = i0,\n          I1 = i1,\n          I2 = i2,\n          I3 = i3;\n      }\n\n      /**\n       * Set nonce for CTR-family modes.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} n0 - nonce vector\n       * @param {number} n1 - nonce vector\n       * @param {number} n2 - nonce vector\n       * @param {number} n3 - nonce vector\n       */\n      function set_nonce(n0, n1, n2, n3) {\n        n0 = n0 | 0;\n        n1 = n1 | 0;\n        n2 = n2 | 0;\n        n3 = n3 | 0;\n\n        N0 = n0,\n          N1 = n1,\n          N2 = n2,\n          N3 = n3;\n      }\n\n      /**\n       * Set counter mask for CTR-family modes.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} m0 - counter mask vector\n       * @param {number} m1 - counter mask vector\n       * @param {number} m2 - counter mask vector\n       * @param {number} m3 - counter mask vector\n       */\n      function set_mask(m0, m1, m2, m3) {\n        m0 = m0 | 0;\n        m1 = m1 | 0;\n        m2 = m2 | 0;\n        m3 = m3 | 0;\n\n        M0 = m0,\n          M1 = m1,\n          M2 = m2,\n          M3 = m3;\n      }\n\n      /**\n       * Set counter for CTR-family modes.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} c0 - counter vector\n       * @param {number} c1 - counter vector\n       * @param {number} c2 - counter vector\n       * @param {number} c3 - counter vector\n       */\n      function set_counter(c0, c1, c2, c3) {\n        c0 = c0 | 0;\n        c1 = c1 | 0;\n        c2 = c2 | 0;\n        c3 = c3 | 0;\n\n        N3 = (~M3 & N3) | M3 & c3,\n          N2 = (~M2 & N2) | M2 & c2,\n          N1 = (~M1 & N1) | M1 & c1,\n          N0 = (~M0 & N0) | M0 & c0;\n      }\n\n      /**\n       * Store the internal state vector into the heap.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} pos - offset where to put the data\n       * @return {number} The number of bytes have been written into the heap, always 16.\n       */\n      function get_state(pos) {\n        pos = pos | 0;\n\n        if (pos & 15) return -1;\n\n        DATA[pos | 0] = S0 >>> 24,\n          DATA[pos | 1] = S0 >>> 16 & 255,\n          DATA[pos | 2] = S0 >>> 8 & 255,\n          DATA[pos | 3] = S0 & 255,\n          DATA[pos | 4] = S1 >>> 24,\n          DATA[pos | 5] = S1 >>> 16 & 255,\n          DATA[pos | 6] = S1 >>> 8 & 255,\n          DATA[pos | 7] = S1 & 255,\n          DATA[pos | 8] = S2 >>> 24,\n          DATA[pos | 9] = S2 >>> 16 & 255,\n          DATA[pos | 10] = S2 >>> 8 & 255,\n          DATA[pos | 11] = S2 & 255,\n          DATA[pos | 12] = S3 >>> 24,\n          DATA[pos | 13] = S3 >>> 16 & 255,\n          DATA[pos | 14] = S3 >>> 8 & 255,\n          DATA[pos | 15] = S3 & 255;\n\n        return 16;\n      }\n\n      /**\n       * Store the internal iv vector into the heap.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} pos - offset where to put the data\n       * @return {number} The number of bytes have been written into the heap, always 16.\n       */\n      function get_iv(pos) {\n        pos = pos | 0;\n\n        if (pos & 15) return -1;\n\n        DATA[pos | 0] = I0 >>> 24,\n          DATA[pos | 1] = I0 >>> 16 & 255,\n          DATA[pos | 2] = I0 >>> 8 & 255,\n          DATA[pos | 3] = I0 & 255,\n          DATA[pos | 4] = I1 >>> 24,\n          DATA[pos | 5] = I1 >>> 16 & 255,\n          DATA[pos | 6] = I1 >>> 8 & 255,\n          DATA[pos | 7] = I1 & 255,\n          DATA[pos | 8] = I2 >>> 24,\n          DATA[pos | 9] = I2 >>> 16 & 255,\n          DATA[pos | 10] = I2 >>> 8 & 255,\n          DATA[pos | 11] = I2 & 255,\n          DATA[pos | 12] = I3 >>> 24,\n          DATA[pos | 13] = I3 >>> 16 & 255,\n          DATA[pos | 14] = I3 >>> 8 & 255,\n          DATA[pos | 15] = I3 & 255;\n\n        return 16;\n      }\n\n      /**\n       * GCM initialization.\n       * @instance\n       * @memberof AES_asm\n       */\n      // function gcm_init() {\n      //   _ecb_enc(0, 0, 0, 0);\n      //   H0 = S0,\n      //     H1 = S1,\n      //     H2 = S2,\n      //     H3 = S3;\n      // }\n\n      /**\n       * Perform ciphering operation on the supplied data.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} mode - block cipher mode (see {@link AES_asm} mode constants)\n       * @param {number} pos - offset of the data being processed\n       * @param {number} len - length of the data being processed\n       * @return {number} Actual amount of data have been processed.\n       */\n      function cipher(mode, pos, len) {\n        mode = mode | 0;\n        pos = pos | 0;\n        len = len | 0;\n\n        var ret = 0;\n\n        if (pos & 15) return -1;\n\n        while ((len | 0) >= 16) {\n          _cipher_modes[mode & 7](\n            DATA[pos | 0] << 24 | DATA[pos | 1] << 16 | DATA[pos | 2] << 8 | DATA[pos | 3],\n            DATA[pos | 4] << 24 | DATA[pos | 5] << 16 | DATA[pos | 6] << 8 | DATA[pos | 7],\n            DATA[pos | 8] << 24 | DATA[pos | 9] << 16 | DATA[pos | 10] << 8 | DATA[pos | 11],\n            DATA[pos | 12] << 24 | DATA[pos | 13] << 16 | DATA[pos | 14] << 8 | DATA[pos | 15]\n          );\n\n          DATA[pos | 0] = S0 >>> 24,\n            DATA[pos | 1] = S0 >>> 16 & 255,\n            DATA[pos | 2] = S0 >>> 8 & 255,\n            DATA[pos | 3] = S0 & 255,\n            DATA[pos | 4] = S1 >>> 24,\n            DATA[pos | 5] = S1 >>> 16 & 255,\n            DATA[pos | 6] = S1 >>> 8 & 255,\n            DATA[pos | 7] = S1 & 255,\n            DATA[pos | 8] = S2 >>> 24,\n            DATA[pos | 9] = S2 >>> 16 & 255,\n            DATA[pos | 10] = S2 >>> 8 & 255,\n            DATA[pos | 11] = S2 & 255,\n            DATA[pos | 12] = S3 >>> 24,\n            DATA[pos | 13] = S3 >>> 16 & 255,\n            DATA[pos | 14] = S3 >>> 8 & 255,\n            DATA[pos | 15] = S3 & 255;\n\n          ret = (ret + 16) | 0,\n            pos = (pos + 16) | 0,\n            len = (len - 16) | 0;\n        }\n\n        return ret | 0;\n      }\n\n      /**\n       * Calculates MAC of the supplied data.\n       * @instance\n       * @memberof AES_asm\n       * @param {number} mode - block cipher mode (see {@link AES_asm} mode constants)\n       * @param {number} pos - offset of the data being processed\n       * @param {number} len - length of the data being processed\n       * @return {number} Actual amount of data have been processed.\n       */\n      function mac(mode, pos, len) {\n        mode = mode | 0;\n        pos = pos | 0;\n        len = len | 0;\n\n        var ret = 0;\n\n        if (pos & 15) return -1;\n\n        while ((len | 0) >= 16) {\n          _mac_modes[mode & 1](\n            DATA[pos | 0] << 24 | DATA[pos | 1] << 16 | DATA[pos | 2] << 8 | DATA[pos | 3],\n            DATA[pos | 4] << 24 | DATA[pos | 5] << 16 | DATA[pos | 6] << 8 | DATA[pos | 7],\n            DATA[pos | 8] << 24 | DATA[pos | 9] << 16 | DATA[pos | 10] << 8 | DATA[pos | 11],\n            DATA[pos | 12] << 24 | DATA[pos | 13] << 16 | DATA[pos | 14] << 8 | DATA[pos | 15]\n          );\n\n          ret = (ret + 16) | 0,\n            pos = (pos + 16) | 0,\n            len = (len - 16) | 0;\n        }\n\n        return ret | 0;\n      }\n\n      /**\n       * AES cipher modes table (virual methods)\n       */\n      var _cipher_modes = [_cbc_enc, _cbc_enc, _cbc_enc, _cbc_dec, _cbc_dec, _cbc_dec, _cbc_dec, _cbc_dec];\n\n      /**\n       * AES MAC modes table (virual methods)\n       */\n      var _mac_modes = [_cbc_enc, _cbc_enc];\n\n      /**\n       * Asm.js module exports\n       */\n      return {\n        set_rounds: set_rounds,\n        set_state: set_state,\n        set_iv: set_iv,\n        set_nonce: set_nonce,\n        set_mask: set_mask,\n        set_counter: set_counter,\n        get_state: get_state,\n        get_iv: get_iv,\n        // gcm_init: gcm_init,\n        cipher: cipher,\n        mac: mac,\n      };\n    }(stdlib, foreign, buffer);\n\n    asm.set_key = set_key;\n\n    return asm;\n  };\n\n  /**\n   * AES enciphering mode constants\n   * @enum {number}\n   * @const\n   */\n  wrapper.ENC = {\n    //ECB: 0,\n    CBC: 2,\n    //CFB: 4,\n    //OFB: 6,\n    // CTR: 7,\n  },\n\n    /**\n     * AES deciphering mode constants\n     * @enum {number}\n     * @const\n     */\n    wrapper.DEC = {\n      //ECB: 1,\n      CBC: 3,\n      //CFB: 5,\n      //OFB: 6,\n      // CTR: 7,\n    },\n\n    /**\n     * AES MAC mode constants\n     * @enum {number}\n     * @const\n     */\n    wrapper.MAC = {\n      CBC: 0,\n      //GCM: 1,\n    };\n\n  /**\n   * Heap data offset\n   * @type {number}\n   * @const\n   */\n  wrapper.HEAP_DATA = 0x4000;\n\n  return wrapper;\n}();\n", null, null, null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,CAAQ,IAAI,OAAO,GAAG,YAAY;AAElC;AACA;AACA;AACA;AACA,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC;AACzB;AACA;AACA;AACA;AACA,EAAE,IAAI,KAAK,EAAE,KAAK,CAAC;AACnB;AACA;AACA;AACA;AACA,EAAE,SAAS,KAAK,GAAG;AACnB,IAAI,KAAK,GAAG,EAAE;AACd,MAAM,KAAK,GAAG,EAAE,CAAC;AACjB;AACA,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACpB,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC9B,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACnB;AACA;AACA,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC;AACtC,MAAM,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC;AAChC,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACpB;AACA;AACA,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1B,KAAK;AACL,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1B,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACjB;AACA,IAAI,UAAU,GAAG,IAAI,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;AACtB,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AAClC,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE;AACnB,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AACvB,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,QAAQ,CAAC;AACf;AACA;AACA;AACA;AACA,EAAE,IAAI,QAAQ,CAAC;AACf;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,CAAC;AACd;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,CAAC;AACd;AACA;AACA;AACA;AACA,EAAE,SAAS,QAAQ,GAAG;AACtB,IAAI,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;AAC7B;AACA;AACA,IAAI,SAAS,EAAE,CAAC,CAAC,EAAE;AACnB,MAAM,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClB,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9B,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC;AACzC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACf,OAAO;AACP,MAAM,CAAC,IAAI,EAAE,CAAC;AACd,MAAM,OAAO,CAAC,CAAC;AACf,KAAK;AACL;AACA;AACA,IAAI,QAAQ,GAAG,EAAE;AACjB,MAAM,QAAQ,GAAG,EAAE;AACnB,MAAM,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAChC,MAAM,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACjC;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClC,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACpB;AACA;AACA,MAAM,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACtB,MAAM,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACtB;AACA;AACA,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7E,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAClG;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAClC,QAAQ,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAC9E,QAAQ,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAC9E,OAAO;AACP,KAAK;AACL;AACA,IAAI,aAAa,GAAG,IAAI,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG,UAAU,OAAO,EAAE,MAAM,EAAE;AAC3C;AACA,IAAI,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,CAAC;AACnC;AACA;AACA,IAAI,IAAI,IAAI,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;AACpC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAChC,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACtD,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACtD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACzD,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC;AAC1C,QAAQ,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC;AACjD;AACA;AACA,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAClD,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACvD,QAAQ,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE;AAC1D,UAAU,CAAC,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAC3H,SAAS;AACT,QAAQ,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE;AAC1B,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC;AACnD,UAAU,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;AAC1D,SAAS;AACT,QAAQ,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;AACrC,OAAO;AACP;AACA;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AACrC,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE;AACvC,UAAU,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AACpD,UAAU,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AACnC,YAAY,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;AAC9B,WAAW,MAAM;AACjB,YAAY,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;AAC1D,gBAAgB,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC;AACpD,gBAAgB,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AACnD,gBAAgB,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC9C,WAAW;AACX,SAAS;AACT,OAAO;AACP;AACA;AACA,MAAM,GAAG,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC7B,KAAK;AACL;AACA;AACA,IAAI,IAAI,MAAM,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;AACpE;AACA,IAAI,IAAI,GAAG,GAAG,UAAU,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE;AACjD,MAAM,SAAS,CAAC;AAChB;AACA,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;AACxC,QAAQ,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;AACtC,QAAQ,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;AACtC,QAAQ,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;AACtC,QAAQ,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;AACtC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACd;AACA,MAAM,IAAI,IAAI,GAAG,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAC/C,QAAQ,IAAI,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACjD,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAClB,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAClB,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAClB,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAClB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB;AACA,QAAQ,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;AAClC,UAAU,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;AACxC,UAAU,CAAC,GAAG,CAAC,CAAC;AAChB;AACA,QAAQ,EAAE,GAAG,CAAC,GAAG,KAAK,EAAE,EAAE,GAAG,CAAC,GAAG,KAAK,EAAE,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;AACvD;AACA;AACA,QAAQ,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACpC,UAAU,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACtC,UAAU,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACtC,UAAU,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AACxC;AACA;AACA,QAAQ,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;AAC5D,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAClL,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACpL,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACpL,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AACtL,UAAU,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC;AAC7C,SAAS;AACT;AACA;AACA,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9L,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChM,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChM,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AAClM,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACxC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB;AACA,QAAQ,KAAK;AACb,UAAU,MAAM,EAAE,MAAM,EAAE,MAAM;AAChC,UAAU,CAAC;AACX,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE;AACjB,SAAS,CAAC;AACV;AACA,QAAQ,EAAE,GAAG,EAAE;AACf,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE,CAAC;AAClB,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACxC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB;AACA,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC;AAClB;AACA,QAAQ,KAAK;AACb,UAAU,MAAM,EAAE,MAAM,EAAE,MAAM;AAChC,UAAU,CAAC;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC;AACV;AACA,QAAQ,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;AAChC;AACA,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE;AACpB,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE;AACtB,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE;AACtB,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACvB;AACA,QAAQ,EAAE,GAAG,EAAE;AACf,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE,CAAC;AAClB,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACxC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB;AACA,QAAQ,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;AAC1C,UAAU,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;AACxC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AACvB;AACA,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE;AACpB,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE;AACtB,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE;AACtB,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACvB;AACA,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC;AACnB,UAAU,EAAE,GAAG,EAAE,GAAG,CAAC;AACrB,UAAU,EAAE,GAAG,EAAE,GAAG,CAAC;AACrB,UAAU,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACtB;AACA,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC/C,UAAU,IAAI,EAAE,KAAK,EAAE,EAAE;AACzB,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE;AACxB,cAAc,EAAE,GAAG,EAAE,GAAG,EAAE;AAC1B,cAAc,EAAE,GAAG,EAAE,GAAG,EAAE;AAC1B,cAAc,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAC3B,WAAW;AACX;AACA,UAAU,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;AACtC,YAAY,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;AACxC,YAAY,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;AACxC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAC3B;AACA,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACrB;AACA,UAAU,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;AACtC,YAAY,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;AACxC,YAAY,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;AACxC,YAAY,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AAC5B;AACA,UAAU,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;AACtC,SAAS;AACT;AACA,QAAQ,EAAE,GAAG,EAAE;AACf,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE,CAAC;AAClB,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,UAAU,CAAC,CAAC,EAAE;AAC7B,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAClB,QAAQ,CAAC,GAAG,CAAC,CAAC;AACd,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACzC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB;AACA,QAAQ,EAAE,GAAG,EAAE;AACf,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE,CAAC;AAClB,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACtC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB;AACA,QAAQ,EAAE,GAAG,EAAE;AACf,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE,CAAC;AAClB,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACzC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB;AACA,QAAQ,EAAE,GAAG,EAAE;AACf,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE,CAAC;AAClB,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACxC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB;AACA,QAAQ,EAAE,GAAG,EAAE;AACf,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE;AACjB,UAAU,EAAE,GAAG,EAAE,CAAC;AAClB,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAC3C,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpB;AACA,QAAQ,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE;AACjC,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE;AACnC,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE;AACnC,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACpC,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,SAAS,CAAC,GAAG,EAAE;AAC9B,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACtB;AACA,QAAQ,IAAI,GAAG,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AAChC;AACA,QAAQ,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE;AACjC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAG;AACzC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG;AACxC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG;AAClC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE;AACnC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAG;AACzC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG;AACxC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG;AAClC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE;AACnC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAG;AACzC,UAAU,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG;AACzC,UAAU,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG;AACnC,UAAU,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE;AACpC,UAAU,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAG;AAC1C,UAAU,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG;AACzC,UAAU,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC;AACpC;AACA,QAAQ,OAAO,EAAE,CAAC;AAClB,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AAC3B,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACtB;AACA,QAAQ,IAAI,GAAG,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AAChC;AACA,QAAQ,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE;AACjC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAG;AACzC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG;AACxC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG;AAClC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE;AACnC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAG;AACzC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG;AACxC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG;AAClC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE;AACnC,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAG;AACzC,UAAU,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG;AACzC,UAAU,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG;AACnC,UAAU,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE;AACpC,UAAU,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAG;AAC1C,UAAU,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG;AACzC,UAAU,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC;AACpC;AACA,QAAQ,OAAO,EAAE,CAAC;AAClB,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,QAAQ,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AACxB,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACtB,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACtB;AACA,QAAQ,IAAI,GAAG,GAAG,CAAC,CAAC;AACpB;AACA,QAAQ,IAAI,GAAG,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AAChC;AACA,QAAQ,OAAO,CAAC,GAAG,GAAG,CAAC,KAAK,EAAE,EAAE;AAChC,UAAU,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC;AACjC,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1F,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1F,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AAC5F,YAAY,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AAC9F,WAAW,CAAC;AACZ;AACA,UAAU,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE;AACnC,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAG;AAC3C,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG;AAC1C,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG;AACpC,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE;AACrC,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAG;AAC3C,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG;AAC1C,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG;AACpC,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE;AACrC,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAG;AAC3C,YAAY,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG;AAC3C,YAAY,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG;AACrC,YAAY,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE;AACtC,YAAY,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAG;AAC5C,YAAY,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,GAAG;AAC3C,YAAY,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC;AACtC;AACA,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC;AAC9B,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC;AAChC,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC;AACjC,SAAS;AACT;AACA,QAAQ,OAAO,GAAG,GAAG,CAAC,CAAC;AACvB,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;AACnC,QAAQ,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AACxB,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACtB,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACtB;AACA,QAAQ,IAAI,GAAG,GAAG,CAAC,CAAC;AACpB;AACA,QAAQ,IAAI,GAAG,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AAChC;AACA,QAAQ,OAAO,CAAC,GAAG,GAAG,CAAC,KAAK,EAAE,EAAE;AAChC,UAAU,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC;AAC9B,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1F,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1F,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AAC5F,YAAY,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AAC9F,WAAW,CAAC;AACZ;AACA,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC;AAC9B,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC;AAChC,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC;AACjC,SAAS;AACT;AACA,QAAQ,OAAO,GAAG,GAAG,CAAC,CAAC;AACvB,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM,IAAI,aAAa,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC3G;AACA;AACA;AACA;AACA,MAAM,IAAI,UAAU,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC5C;AACA;AACA;AACA;AACA,MAAM,OAAO;AACb,QAAQ,UAAU,EAAE,UAAU;AAC9B,QAAQ,SAAS,EAAE,SAAS;AAC5B,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,SAAS,EAAE,SAAS;AAC5B,QAAQ,QAAQ,EAAE,QAAQ;AAC1B,QAAQ,WAAW,EAAE,WAAW;AAChC,QAAQ,SAAS,EAAE,SAAS;AAC5B,QAAQ,MAAM,EAAE,MAAM;AACtB;AACA,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,GAAG,EAAE,GAAG;AAChB,OAAO,CAAC;AACR,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAC/B;AACA,IAAI,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC;AAC1B;AACA,IAAI,OAAO,GAAG,CAAC;AACf,GAAG,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,GAAG,GAAG;AAChB;AACA,IAAI,GAAG,EAAE,CAAC;AACV;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,CAAC,GAAG,GAAG;AAClB;AACA,MAAM,GAAG,EAAE,CAAC;AACZ;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,CAAC,GAAG,GAAG;AAClB,MAAM,GAAG,EAAE,CAAC;AACZ;AACA,KAAK,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC;AAC7B;AACA,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC,EAAE;;ACl7Ba,SAAA,UAAU,CAAC,IAAiB,EAAE,QAAiB,EAAA;AAC3D,IAAA,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,UAAU,GAAG,QAAQ,IAAI,KAAK,CAAA;AAEvD,IAAA,IAAI,IAAI,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC;AACzB,QAAA,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAA;AAElF,IAAA,IAAI,GAAG,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;AAEpD,IAAA,OAAO,IAAI,CAAA;AACf,CAAC;AAEK,SAAU,WAAW,CACvB,IAAgB,EAChB,IAAY,EACZ,IAAgB,EAChB,IAAY,EACZ,IAAY,EAAA;AAEZ,IAAA,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;AAC/B,IAAA,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAA;AAEtC,IAAA,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA;AAEhD,IAAA,OAAO,IAAI,CAAA;AACf,CAAC;AAMK,SAAU,QAAQ,CAAC,CAAa,EAAA;IAClC,OAAO,CAAC,YAAY,UAAU,CAAA;AAClC,CAAC;AAEe,SAAA,SAAS,CAAC,GAAG,GAAiB,EAAA;IAC1C,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;AACnE,IAAA,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAA;IAEvC,IAAI,MAAM,GAAG,CAAC,CAAA;AACd,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;AACvB,QAAA,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;AAC1B,KAAA;AACD,IAAA,OAAO,GAAG,CAAA;AACd;;MCzCa,GAAG,CAAA;IAQZ,WAAY,CAAA,GAAe,EAAE,EAA0B,EAAE,OAAO,GAAG,IAAI,EAAE,IAAW,EAAA;QAH7E,IAAG,CAAA,GAAA,GAAG,CAAC,CAAA;QACP,IAAG,CAAA,GAAA,GAAG,CAAC,CAAA;AAGV,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;;AAGhB,QAAA,IAAI,CAAC,IAAI,GAAG,UAAU,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;AACpD,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;;AAG9C,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;AACZ,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;;AAGZ,QAAA,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;QACzB,IAAI,MAAM,KAAK,EAAE,IAAI,MAAM,KAAK,EAAE,IAAI,MAAM,KAAK,EAAE;AAAE,YAAA,MAAM,IAAI,SAAS,CAAC,kBAAkB,CAAC,CAAA;AAE5F,QAAA,MAAM,OAAO,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAA;QACxE,IAAI,CAAC,GAAG,CAAC,OAAO,CACZ,MAAM,IAAI,CAAC,EACX,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EACpB,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EACpB,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EACpB,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,EACrB,MAAM,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,EACvC,MAAM,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,EACvC,MAAM,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,EACvC,MAAM,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,CAC1C,CAAA;;QAGD,IAAI,EAAE,KAAK,SAAS,EAAE;AAClB,YAAA,IAAI,EAAE,CAAC,MAAM,KAAK,EAAE;AAAE,gBAAA,MAAM,IAAI,SAAS,CAAC,iBAAiB,CAAC,CAAA;AAE5D,YAAA,MAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;AAEpE,YAAA,IAAI,CAAC,GAAG,CAAC,MAAM,CACX,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EACnB,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EACnB,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EACnB,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CACvB,CAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;AAC9B,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;KACzB;AAED,IAAA,mBAAmB,CAAC,IAAgB,EAAA;AAChC,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AAAE,YAAA,MAAM,IAAI,SAAS,CAAC,6BAA6B,CAAC,CAAA;AAEvE,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;AACpB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACpC,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAA;AAC9B,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;AAClB,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QAClB,IAAI,IAAI,GAAG,CAAC,CAAA;AACZ,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,CAAA;QAC3B,IAAI,IAAI,GAAG,CAAC,CAAA;QACZ,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,EAAE,CAAA;QAC/B,IAAI,IAAI,GAAG,CAAC,CAAA;AAEZ,QAAA,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAA;QAEnC,OAAO,IAAI,GAAG,CAAC,EAAE;AACb,YAAA,IAAI,GAAG,WAAW,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;YACrD,GAAG,IAAI,IAAI,CAAA;YACX,IAAI,IAAI,IAAI,CAAA;YACZ,IAAI,IAAI,IAAI,CAAA;AAEZ,YAAA,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC,CAAA;AAEzC,YAAA,IAAI,IAAI;AAAE,gBAAA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA;YAC1D,IAAI,IAAI,IAAI,CAAA;YAEZ,IAAI,IAAI,GAAG,GAAG,EAAE;gBACZ,GAAG,IAAI,IAAI,CAAA;gBACX,GAAG,IAAI,IAAI,CAAA;AACd,aAAA;AAAM,iBAAA;gBACH,GAAG,GAAG,CAAC,CAAA;gBACP,GAAG,GAAG,CAAC,CAAA;AACV,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;AACd,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;AAEd,QAAA,OAAO,MAAM,CAAA;KAChB;IAED,kBAAkB,GAAA;AACd,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;AACpB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACpC,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAA;AAC9B,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;AACpB,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QAClB,MAAM,IAAI,GAAG,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC,CAAA;QAC5B,IAAI,IAAI,GAAG,GAAG,CAAA;;QAGd,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE;gBAC3B,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAA;AAC7B,aAAA;YACD,GAAG,IAAI,IAAI,CAAA;YACX,IAAI,GAAG,GAAG,CAAA;AACb,SAAA;aAAM,IAAI,GAAG,GAAG,EAAE,EAAE;AACjB,YAAA,MAAM,IAAI,SAAS,CAAC,kDAAkD,CAAC,CAAA;AAC1E,SAAA;;;;AAKD,QAAA,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAA;AAEnC,QAAA,IAAI,GAAG;YAAE,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC,CAAA;AAE3C,QAAA,IAAI,IAAI;AAAE,YAAA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC,CAAA;AAEpD,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;AACZ,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;AAEZ,QAAA,OAAO,MAAM,CAAA;KAChB;AAED,IAAA,mBAAmB,CAAC,IAAgB,EAAA;AAChC,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AAAE,YAAA,MAAM,IAAI,SAAS,CAAC,6BAA6B,CAAC,CAAA;AAEvE,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;AACpB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACpC,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAA;AAC9B,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;AAClB,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QAClB,IAAI,IAAI,GAAG,CAAC,CAAA;AACZ,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,CAAA;QAC3B,IAAI,IAAI,GAAG,CAAC,CAAA;QACZ,IAAI,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,EAAE,CAAA;QAC7B,IAAI,IAAI,GAAG,CAAC,CAAA;QACZ,IAAI,IAAI,GAAG,CAAC,CAAA;QAEZ,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,EAAE,CAAA;YAC9B,IAAI,IAAI,IAAI,CAAA;AACf,SAAA;AAED,QAAA,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAA;QAEnC,OAAO,IAAI,GAAG,CAAC,EAAE;AACb,YAAA,IAAI,GAAG,WAAW,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;YACrD,GAAG,IAAI,IAAI,CAAA;YACX,IAAI,IAAI,IAAI,CAAA;YACZ,IAAI,IAAI,IAAI,CAAA;YAEZ,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAA;AAE9D,YAAA,IAAI,IAAI;AAAE,gBAAA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA;YAC1D,IAAI,IAAI,IAAI,CAAA;YAEZ,IAAI,IAAI,GAAG,GAAG,EAAE;gBACZ,GAAG,IAAI,IAAI,CAAA;gBACX,GAAG,IAAI,IAAI,CAAA;AACd,aAAA;AAAM,iBAAA;gBACH,GAAG,GAAG,CAAC,CAAA;gBACP,GAAG,GAAG,CAAC,CAAA;AACV,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;AACd,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;AAEd,QAAA,OAAO,MAAM,CAAA;KAChB;IAED,kBAAkB,GAAA;AACd,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;AACpB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACpC,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAA;AAC9B,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;AACpB,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACpB,IAAI,IAAI,GAAG,GAAG,CAAA;QAEd,IAAI,GAAG,GAAG,CAAC,EAAE;YACT,IAAI,GAAG,GAAG,EAAE,EAAE;;AAEV,gBAAA,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAA;;;;AAItE,aAAA;YAED,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC,CAAA;AAElC,YAAA,2CAA2C,IAAI,CAAC,OAAO,EAAE;gBACrD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,CAAA;gBAChC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,IAAI,GAAG,GAAG,IAAI;AAAE,oBAAA,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAA;gBAErE,IAAI,MAAM,GAAG,CAAC,CAAA;gBACd,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;oBAAE,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,CAAA;AAClE,gBAAA,IAAI,MAAM;AAAE,oBAAA,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAA;gBAE1C,IAAI,IAAI,GAAG,CAAA;AACd,aAAA;AACJ,SAAA;AAED,QAAA,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAA;QAEnC,IAAI,IAAI,GAAG,CAAC,EAAE;AACV,YAAA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC,CAAA;AAC7C,SAAA;AAED,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;AACZ,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;AAEZ,QAAA,OAAO,MAAM,CAAA;KAChB;AACJ;;MClOY,OAAO,CAAA;IAWhB,WAAY,CAAA,GAAe,EAAE,EAAe,EAAE,OAAO,GAAG,IAAI,EAAE,GAAS,EAAA;QACnE,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;KAC1D;IAVD,OAAO,OAAO,CAAC,IAAgB,EAAE,GAAe,EAAE,OAAO,GAAG,IAAI,EAAE,EAAe,EAAA;AAC7E,QAAA,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;KACrD;IAED,OAAO,OAAO,CAAC,IAAgB,EAAE,GAAe,EAAE,OAAO,GAAG,IAAI,EAAE,EAAe,EAAA;AAC7E,QAAA,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;KACrD;AAMD,IAAA,OAAO,CAAC,IAAgB,EAAA;QACpB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;QAC7C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAA;AAExC,QAAA,OAAO,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;KAC3B;AAED,IAAA,OAAO,CAAC,IAAgB,EAAA;QACpB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;QAC7C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAA;AAExC,QAAA,OAAO,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;KAC3B;AACJ;;;;"}