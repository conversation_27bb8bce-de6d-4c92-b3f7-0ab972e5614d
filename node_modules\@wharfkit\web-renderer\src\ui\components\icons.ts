const Alert = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
class="lucide lucide-alert-circle"
><circle cx="12" cy="12" r="10" /><line x1="12" x2="12" y1="8" y2="12" /><line
    x1="12"
    x2="12.01"
    y1="16"
    y2="16"
/></svg
>
`

const Check = `<svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"><polyline points="20 6 9 17 4 12" /></svg
>
`

const ChevronLeft = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"><polyline points="15 18 9 12 15 6" /></svg
>
`

const ChevronRight = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"><polyline points="9 18 15 12 9 6" /></svg
>
`

const Close = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
><line x1="18" y1="6" x2="6" y2="18" /><line x1="6" y1="6" x2="18" y2="18" /></svg
>
`

const Copy = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
><rect x="9" y="9" width="13" height="13" rx="2" ry="2" /><path
    d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
/></svg
>
`

const Error = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
class="lucide lucide-x-octagon"
><polygon
    points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"
/><line x1="15" x2="9" y1="9" y2="15" /><line x1="9" x2="15" y1="9" y2="15" /></svg
>
`

const ExternalLink = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
class="lucide lucide-external-link"
><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" /><polyline
    points="15 3 21 3 21 9"
/><line x1="10" x2="21" y1="14" y2="3" /></svg
>
`

const Expand = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
><polyline points="15 3 21 3 21 9" /><polyline points="9 21 3 21 3 15" /><line
    x1="21"
    y1="3"
    x2="14"
    y2="10"
/><line x1="3" y1="21" x2="10" y2="14" /></svg
>
`

const FileCode = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
><path d="M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v4" /><polyline
    points="14 2 14 8 20 8"
/><path d="m9 18 3-3-3-3" /><path d="m5 12-3 3 3 3" /></svg
>
`

const Github = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
class="lucide lucide-github"
><path
    d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"
/><path d="M9 18c-4.51 2-5-2-7-2" /></svg
>
`

const Globe = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
class="lucide lucide-globe"
><circle cx="12" cy="12" r="10" /><line x1="2" x2="22" y1="12" y2="12" /><path
    d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"
/></svg
>
`

const Info = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
class="lucide lucide-info"
><circle cx="12" cy="12" r="10" /><line x1="12" x2="12" y1="16" y2="12" /><line
    x1="12"
    x2="12.01"
    y1="8"
    y2="8"
/></svg
>
`

const Login = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
><path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4" /><polyline
    points="10 17 15 12 10 7"
/><line x1="15" y1="12" x2="3" y2="12" /></svg
>
`

const Settings = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
class="lucide lucide-settings-2"
><path d="M20 7h-9" /><path d="M14 17H5" /><circle cx="17" cy="17" r="3" /><circle
    cx="7"
    cy="7"
    r="3"
/></svg
>
`

const Signal = `<svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
    class="lucide lucide-radio"
    ><path d="M4.9 19.1C1 15.2 1 8.8 4.9 4.9" /><path d="M7.8 16.2c-2.3-2.3-2.3-6.1 0-8.5" /><circle
        cx="12"
        cy="12"
        r="2"
    /><path d="M16.2 7.8c2.3 2.3 2.3 6.1 0 8.5" /><path d="M19.1 4.9C23 8.8 23 15.1 19.1 19" /></svg
>
`

const Theme = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
class="lucide lucide-sun-moon"
><path d="M12 16a4 4 0 1 0 0-8 4 4 0 0 0 0 8z" /><path d="M12 8a2.828 2.828 0 1 0 4 4" /><path
    d="M12 2v2"
/><path d="M12 20v2" /><path d="m4.93 4.93 1.41 1.41" /><path d="m17.66 17.66 1.41 1.41" /><path
    d="M2 12h2"
/><path d="M20 12h2" /><path d="m6.34 17.66-1.41 1.41" /><path d="m19.07 4.93-1.41 1.41" /></svg
>
`

const Wallet = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
><path d="M20 12V8H6a2 2 0 0 1-2-2c0-1.1.9-2 2-2h12v4" /><path
    d="M4 6v12c0 1.1.9 2 2 2h14v-4"
/><path d="M18 12a2 2 0 0 0-2 2c0 1.1.9 2 2 2h4v-4h-4z" /></svg
>
`

const Waves = `<svg
xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 24 24"
fill="none"
stroke="currentColor"
stroke-width="2"
stroke-linecap="round"
stroke-linejoin="round"
class="lucide lucide-waves"
><path
    d="M2 6c.6.5 1.2 1 2.5 1C7 7 7 5 9.5 5c2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1"
/><path
    d="M2 12c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1"
/><path
    d="M2 18c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1"
/></svg
>
`

const Wharf = `<svg width="36" height="31" fill="none" xmlns="http://www.w3.org/2000/svg"
><path
    d="M35.54 18.77c-.019.989-.673 1.676-1.319 2.048l-1.388.802c-.663.383-1.438.531-2.132.531-.695 0-1.47-.148-2.132-.531l-1.362-.802v2.722c0 1.008-.664 1.711-1.318 2.089l-1.389.801c-.663.383-1.437.531-2.132.531-.694 0-1.469-.148-2.131-.53l-2.035-1.175-2.034 1.174c-.663.383-1.438.531-2.132.531-.694 0-1.469-.148-2.131-.53l-9.722-5.613c-.645-.372-1.3-1.06-1.317-2.049v-.061 3.626c.018.989.672 1.676 1.317 2.049l9.722 5.612c.662.382 1.437.53 2.131.53.694 0 1.47-.148 2.132-.53l2.034-1.175 2.035 1.175c.662.382 1.437.53 2.131.53.695 0 1.47-.148 2.132-.53l1.389-.802c.654-.378 1.318-1.08 1.318-2.088v-2.722l1.362.801c.663.383 1.437.531 2.132.531.694 0 1.469-.148 2.132-.53l1.388-.802c.646-.373 1.3-1.06 1.318-2.05V18.77Z"
    fill="#7BE7CE"
/><path
    d="M.874 15.362a1.5 1.5 0 0 0-.009.161v3.246c.019.99.673 1.677 1.318 2.05l9.722 5.612c.662.382 1.437.53 2.131.53.694 0 1.47-.148 2.132-.53l2.034-1.175 2.035 1.175c.662.382 1.437.53 2.131.53.695 0 1.47-.148 2.132-.53l1.389-.802c.654-.378 1.318-1.08 1.318-2.089V20.82l1.362.801c.663.383 1.437.531 2.132.531.694 0 1.469-.148 2.132-.53l1.388-.802c.646-.373 1.3-1.06 1.318-2.05v-3.564c-.018.99-.672 1.677-1.318 2.049l-1.388.802c-.663.382-1.438.53-2.132.53-.695 0-1.47-.148-2.132-.53l-1.388-.802a2.943 2.943 0 0 1-.067-.04 2.168 2.168 0 0 1 .085.834c.005.05.008.101.008.152v1.776c0 1.008-.664 1.71-1.318 2.088l-1.389.802c-.663.383-1.437.531-2.132.531-.694 0-1.469-.148-2.131-.53l-2.035-1.175-2.034 1.174c-.663.383-1.438.531-2.132.531-.694 0-1.469-.148-2.131-.53l-9.722-5.613c-.61-.352-1.229-.987-1.31-1.892ZM17.54 5.749l.004.043c.07.6.368 1.074.74 1.424a4.94 4.94 0 0 0-.75.045V5.875c0-.043.002-.085.006-.126Z"
    fill="#B2F2E1"
/><path
    d="m3.573 10.152-1.389.801c-.618.358-1.246 1.006-1.312 1.928a1.364 1.364 0 0 0-.007.136V15.204c.019.99.673 1.677 1.318 2.05l9.722 5.611c.662.383 1.437.532 2.131.532.694 0 1.47-.149 2.132-.532l2.034-1.174 2.035 1.174c.662.383 1.437.532 2.131.532.695 0 1.47-.149 2.132-.532l1.389-.801c.654-.378 1.318-1.08 1.318-2.089v-1.775c0-.052-.003-.102-.008-.153a2.25 2.25 0 0 0-.085-.833l.067.04 1.388.801c.663.383 1.437.531 2.132.531.694 0 1.469-.148 2.132-.53l1.388-.802c.646-.373 1.3-1.06 1.318-2.05v-2.092a2.359 2.359 0 0 0 0-.142V8.29l.001-.058c0-1.009-.664-1.711-1.318-2.089L24.501.531C23.838.15 23.064 0 22.369 0c-.694 0-1.469.149-2.131.531l-1.389.802c-.654.377-1.318 1.08-1.318 2.088 0 .048.001.095.004.14v1.847a2.069 2.069 0 0 0 .01.385c.069.6.367 1.073.739 1.423h-.08c-.695 0-1.47.148-2.133.53l-1.388.802c-.654.378-1.318 1.08-1.318 2.089 0 .065.002.13.008.193a1.485 1.485 0 0 0-.002.073v1.682l-.003.041-.002.045a2.298 2.298 0 0 0 .021.405c.017.112.041.22.072.322l-5.623-3.246C7.174 9.769 6.4 9.62 5.705 9.62c-.695 0-1.47.148-2.132.53Z"
    fill="#F4FAF4"
/><path
    d="M23.758 1.818c-.767-.442-2.01-.442-2.778 0l-1.389.802c-.766.443-.766 1.16 0 1.604l9.553 5.514c.369.213.575.501.575.802v.195c0 .3-.207.589-.575.801l-1.22.705c-.767.443-.767 1.16 0 1.603l1.389.802c.767.443 2.01.443 2.777 0l1.389-.802c.767-.443.767-1.16 0-1.603l-1.22-.705c-.369-.212-.576-.5-.576-.801v-.195c0-.3.207-.59.576-.802l1.22-.704c.767-.443.767-1.16 0-1.604l-9.721-5.612ZM7.093 11.439c-.767-.443-2.01-.443-2.777 0l-1.39.802c-.766.443-.766 1.16 0 1.603l9.722 5.612c.767.443 2.01.443 2.777 0l2.778-1.603-11.11-6.414Z"
    fill="#494E62"
/><path
    d="M23.351 15.545c0 .3.207.589.575.801l1.22.705c.767.443.767 1.16 0 1.603l-1.388.802c-.767.443-2.01.443-2.778 0l-2.777-1.603 2.609-1.507c.368-.212.575-.5.575-.801v-.195c0-.3-.207-.589-.575-.801l-5.387-3.11c-.767-.443-.767-1.16 0-1.603l1.39-.802c.766-.443 2.01-.443 2.776 0l5.555 3.207c.767.443.767 1.16 0 1.603l-1.22.705c-.368.212-.575.5-.575.801v.195Z"
    fill="#494E62"
/></svg
>
`

const icons = {
    copy: Copy,
    check: Check,
    close: Close,
    'file-code': FileCode,
    wharf: Wharf,
    login: Login,
    'chevron-right': ChevronRight,
    'chevron-left': ChevronLeft,
    wallet: Wallet,
    expand: Expand,
    signal: Signal,
    settings: Settings,
    globe: Globe,
    github: Github,
    info: Info,
    theme: Theme,
    waves: Waves,
    'external-link': ExternalLink,
    error: Error,
    alert: Alert,
}

export type Icon = keyof typeof icons
export default icons
