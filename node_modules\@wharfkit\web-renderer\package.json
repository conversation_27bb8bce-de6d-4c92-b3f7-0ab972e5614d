{"name": "@wharfkit/web-renderer", "version": "1.4.1", "description": "", "license": "BSD-3-<PERSON><PERSON>", "main": "lib/web-renderer.js", "module": "lib/web-renderer.m.js", "types": "lib/web-renderer.d.ts", "unpkg": "lib/web-renderer.bundle.js", "scripts": {"build": "rollup -c --bundleConfigAsCjs", "build-test": "rollup -c test/rollup.config.js --bundleConfigAsCjs", "dev": "rollup -c test/rollup.config.js --bundleConfigAsCjs -w", "serve": "sirv test/public --no-clear --cors --host"}, "directories": {"lib": "lib"}, "repository": {"type": "git", "url": "git+https://github.com/wharfkit/web-renderer.git"}, "authors": ["<PERSON> <<EMAIL>>"], "bugs": {"url": "https://github.com/wharfkit/web-renderer/issues"}, "homepage": "https://github.com/wharfkit/web-renderer", "files": ["lib/*", "src/*"], "browser": {"buffer": false, "crypto": false}, "peerDependencies": {"@wharfkit/session": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.18.6", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^11.1.2", "@sveltejs/vite-plugin-svelte": "^2.4.6", "@tsconfig/svelte": "^5.0.0", "@typescript-eslint/eslint-plugin": "^5.61.0", "@typescript-eslint/parser": "^5.61.0", "@wharfkit/account-creation-plugin-anchor": "^1.3.1", "@wharfkit/account-creation-plugin-jungle4": "^1.2.0", "@wharfkit/session": "^1.4.0", "@wharfkit/transact-plugin-autocorrect": "^1.1.0", "@wharfkit/transact-plugin-cosigner": "^1.1.0", "@wharfkit/transact-plugin-explorerlink": "^1.0.1", "@wharfkit/transact-plugin-mock": "^1.1.0", "@wharfkit/transact-plugin-resource-provider": "^1.1.0", "@wharfkit/wallet-plugin-anchor": "^1.3.5", "@wharfkit/wallet-plugin-cleos": "^1.1.0", "@wharfkit/wallet-plugin-cloudwallet": "^1.3.3", "@wharfkit/wallet-plugin-metamask": "^1.0.0-rc3", "@wharfkit/wallet-plugin-mock": "^1.1.0", "@wharfkit/wallet-plugin-privatekey": "^1.1.0", "@wharfkit/wallet-plugin-scatter": "^1.1.0", "@wharfkit/wallet-plugin-tokenpocket": "^1.1.0", "@wharfkit/wallet-plugin-wombat": "^1.1.0", "core-js": "^3.31.0", "eslint": "^8.44.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.3", "rollup": "^2.70.2", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-gzip": "^3.1.0", "rollup-plugin-livereload": "^2.0.0", "rollup-plugin-svelte": "^7.1.5", "rollup-plugin-terser": "^7.0.0", "sass": "^1.58.3", "sirv-cli": "^2.0.0", "svelte": "^4.0.3", "svelte-preprocess": "^5.0.4", "sveltekit-i18n": "^2.2.2", "tslib": "^2.6.0", "typescript": "^5.0.0", "vite": "^4.1.4", "yarn-deduplicate": "^6.0.2"}, "dependencies": {}}