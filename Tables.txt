collections
(Single Scope: atomicassets)

Type	Name	Description
name	collection_name	Name of the collection
name	author	Author of the collection
bool	allow_notify	If this is set to true, it isn't possible to add accounts to the notify_accounts vector.
vector<name>	authorized_accounts	Vector of account names. Only these accounts can create new templates of this collection, mint assets of this collection and edit the mutable data of templates/ assets of this collection.
vector<name>	notify_accounts	Vector of account names. These accounts get notified on the blockchain of any actions related to this collection. (More Info)
double	market_fee	3rd party marketplaces are encouraged to take this fee out of every asset sale of this collection and make it available to the collection's author
vector<uint8_t>	serialized_data	Serialized data of collection info. The "collection_format" in the config table is used for serialization.
schemas
(Scope: <collection_name>)

Type	Name	Description
name	schema_name	Name of the schema
vector<FORMAT>	format	Vector of FORMATs. Each FORMAT describes one attribute that can be serialized by this schema.
templates
(Scope: <collection_name>)

Type	Name	Description
int32_t	template_id	Unique auto incrementing id
name	schema_name	Schema that is used for data serialization for this template and any child assets
bool	transferable	Determines if child assets are transferable
bool	burnable	Determines if child assets are burnable
uint32_t	max_supply	Maximum amount of child assets that can be minted . 0 if there is no max supply.
uint32_t	issued_supply	Amount of child assets that have been minted so far
vector<uint8_t>	immutable_serialized_data	Serialized data that can not be changed after creating the template
assets
(Scope: <owner>)

Type	Name	Description
uint64_t	asset_id	Unique auto incrementing id (starting with 2^40)
name	collection_name	Collection that this asset belongs to
name	schema_name	Schema that this asset belongs to
int32_t	template_id	ID of the parent template. -1 if no template is referenced.
name	ram_payer	Account that currently pays for the RAM of this table entry
vector	backed_tokens	Vector of tokens that have been backed to this asset (More Info)
vector<uint8_t>	immutable_serialized_data	Serialized data that can not be changed after creating the asset
vector<uint8_t>	mutable_serialized_data	Serialized data that can be changed after creating the asset
offers
(Single Scope: atomicassets)

Note: An offer's validity is checked when it is created, but it is not automatically deleted when it becomes invalid (because one of the sides loses ownership of at least one of the items included in the offer).

Type	Name	Description
uint64_t	offer_id	Unique auto incrementing id
name	offer_sender	Account name of the offer sender (creator)
name	offer_recipient	Account name of the offer recipient
vector<uint64_t>	sender_asset_ids	Vector of asset ids that the sender will give to the recipient
vector<uint64_t>	recipient_asset_ids	Vector of asset ids that the recipient will give to the sender
string	memo	A memo that can be used to attach messages to trade offers
name	ram_payer	The account paying for the RAM of this table row
balances
(Single Scope: atomicassets)

Note: The tokens that can be deposited to the balance are defined in the supported_tokens field of the config singleton.

Type	Name	Description
name	owner	Account of the owner
vector<asset>	quantities	vector containing the assets that were deposited by owner
config
Singleton

Type	Name	Description
uint64_t	asset_counter	ID that the next asset created will have
int32_t	template_counter	ID that the next template created will have
uint64_t	offer_counter	ID that the next offer created will have
vector<FORMAT>	collection_format	Vector of FORMATs that are used to serialize collection data. Each FORMAT describes one attribute that can be serialized by this schema.
vector<extended_symbol>	supported_tokens	A vector that includes all tokens that can be deposited and backed to assets
tokenconfigs
Singleton

This singleton is needed to comply with the tokenconfigs standard: https://github.com/eosio-standards-wg/tokenconfigs

Type	Name	Description
name	standard	Name of the standard (= atomicassets)
string	version	Version of the standard