import { Cancelable, LocaleDefinitions, ChainDefinition, Fetch, Logo, ChainDefinitionType } from '@wharfkit/common';
export * from '@wharfkit/common';
import { Contract } from '@wharfkit/contract';
import { Checksum256Type, PermissionLevelType, Signature, NameType, APIClient, PermissionLevel, API, Name, AnyTransaction, AnyAction, ActionType, ABIDef, Struct, Checksum256, PublicKey, TransactionType, FetchProviderOptions } from '@wharfkit/antelope';
export * from '@wharfkit/antelope';
import { SigningRequest, SigningRequestEncodingOptions, ResolvedSigningRequest, ResolvedTransaction, IdentityProof } from '@wharfkit/signing-request';
export * from '@wharfkit/signing-request';
import { ABICacheInterface } from '@wharfkit/abicache';
export * from '@wharfkit/abicache';

/**
 * The arguments for a [[UserInterface.prompt]] call.
 */
interface PromptArgs {
    title: string;
    body?: string;
    optional?: boolean;
    elements: PromptElement[];
}
/**
 * The different types of elements that can be used in a [[PromptArgs]].
 */
interface PromptElement {
    type: 'accept' | 'asset' | 'button' | 'close' | 'countdown' | 'link' | 'qr' | 'textarea';
    label?: string;
    data?: unknown;
}
/**
 * The response for a [[UserInterface.prompt]] call.
 */
interface PromptResponse {
}
/**
 * The response for a login call of a [[UserInterface]].
 */
interface UserInterfaceLoginResponse {
    chainId?: Checksum256Type;
    permissionLevel?: PermissionLevelType;
    walletPluginIndex: number;
}
/**
 * The response for an account creation call of a [[UserInterface]].
 */
type UserInterfaceAccountCreationResponse = {
    chain?: Checksum256Type;
    pluginId?: string;
};
/**
 * The options to pass to [[UserInterface.translate]].
 */
interface UserInterfaceTranslateOptions {
    default: string;
    [key: string]: unknown;
}
/**
 * The translate function the UserInterface expects and uses.
 */
type UserInterfaceTranslateFunction = (key: string, options?: UserInterfaceTranslateOptions, namespace?: string) => string;
/**
 * Interface which all 3rd party user interface plugins must implement.
 */
interface UserInterface {
    /** Interact with the user to collect the data needed for a [[UserInterfaceLoginResponse]]. */
    login(context: LoginContext): Promise<UserInterfaceLoginResponse>;
    /** Inform the UI that an error has occurred */
    onError: (error: Error) => Promise<void>;
    /** Inform the UI that an account creation process has started */
    onAccountCreate: (context: CreateAccountContext) => Promise<UserInterfaceAccountCreationResponse>;
    /** Inform the UI that a account creation call has completed **/
    onAccountCreateComplete: () => Promise<void>;
    /** Inform the UI that a login call has started **/
    onLogin: () => Promise<void>;
    /** Inform the UI that a login call has completed **/
    onLoginComplete: () => Promise<void>;
    /** Inform the UI that a transact call has started **/
    onTransact: () => Promise<void>;
    /** Inform the UI that a transact call has completed **/
    onTransactComplete: () => Promise<void>;
    /** Inform the UI that a transact call has started signing the transaction **/
    onSign: () => Promise<void>;
    /** Inform the UI that a transact call has completed signing the transaction **/
    onSignComplete: () => Promise<void>;
    /** Inform the UI that a transact call has started broadcasting the transaction **/
    onBroadcast: () => Promise<void>;
    /** Inform the UI that a transact call has completed broadcasting the transaction **/
    onBroadcastComplete: () => Promise<void>;
    /** Prompt the user with a custom UI element **/
    prompt: (args: PromptArgs) => Cancelable<PromptResponse>;
    /** Update the displayed modal status from a TransactPlugin **/
    status: (message: string) => void;
    /** Translate a string using the UI's language **/
    translate: UserInterfaceTranslateFunction;
    /** Returns a translator for a specific namespace */
    getTranslate: (namespace?: string) => UserInterfaceTranslateFunction;
    /** Programmatically add new localization strings to the  user interface */
    addTranslations: (translations: LocaleDefinitions) => void;
}
/**
 * Abstract class which all 3rd party [[UserInterface]] implementations may extend.
 */
declare abstract class AbstractUserInterface implements UserInterface {
    abstract login(context: LoginContext): Promise<UserInterfaceLoginResponse>;
    abstract onError(error: Error): Promise<void>;
    abstract onAccountCreate(context: CreateAccountContext): Promise<UserInterfaceAccountCreationResponse>;
    abstract onAccountCreateComplete(): Promise<void>;
    abstract onLogin(options?: LoginOptions): Promise<void>;
    abstract onLoginComplete(): Promise<void>;
    abstract onTransact(): Promise<void>;
    abstract onTransactComplete(): Promise<void>;
    abstract onSign(): Promise<void>;
    abstract onSignComplete(): Promise<void>;
    abstract onBroadcast(): Promise<void>;
    abstract onBroadcastComplete(): Promise<void>;
    abstract prompt(args: PromptArgs): Cancelable<PromptResponse>;
    abstract status(message: string): void;
    translate(key: string, options?: UserInterfaceTranslateOptions, namespace?: string): string;
    getTranslate(namespace?: string | undefined): UserInterfaceTranslateFunction;
    addTranslations(translations: LocaleDefinitions): void;
}

/**
 * Interface storage adapters should implement.
 *
 * Storage adapters are responsible for persisting [[Session]]s and can optionally be
 * passed to the [[SessionKit]] constructor to auto-persist sessions.
 */
interface SessionStorage {
    /** Write string to storage at key. Should overwrite existing values without error. */
    write(key: string, data: string): Promise<void>;
    /** Read key from storage. Should return `null` if key can not be found. */
    read(key: string): Promise<string | null>;
    /** Delete key from storage. Should not error if deleting non-existing key. */
    remove(key: string): Promise<void>;
}
declare class BrowserLocalStorage implements SessionStorage {
    readonly keyPrefix: string;
    constructor(keyPrefix?: string);
    write(key: string, data: string): Promise<void>;
    read(key: string): Promise<string | null>;
    remove(key: string): Promise<void>;
    storageKey(key: string): string;
}

type TransactPluginsOptions = Record<string, unknown>;
declare enum TransactHookTypes {
    beforeSign = "beforeSign",
    afterSign = "afterSign",
    afterBroadcast = "afterBroadcast"
}
type TransactHookMutable = (request: SigningRequest, context: TransactContext) => Promise<TransactHookResponse | void>;
type TransactHookImmutable = (result: TransactResult, context: TransactContext) => Promise<TransactHookResponse | void>;
interface TransactHooks {
    afterSign: TransactHookImmutable[];
    beforeSign: TransactHookMutable[];
    afterBroadcast: TransactHookImmutable[];
}
interface TransactHookResponse {
    request: SigningRequest;
    signatures?: Signature[];
}
type TransactHookResponseType = TransactHookResponse | void;
/**
 * Options for creating a new context for a [[Session.transact]] call.
 */
interface TransactContextOptions {
    abiCache: ABICacheInterface;
    appName?: NameType;
    chain: ChainDefinition;
    client: APIClient;
    createRequest: (args: TransactArgs) => Promise<SigningRequest>;
    fetch: Fetch;
    permissionLevel: PermissionLevel;
    storage?: SessionStorage;
    transactPlugins?: AbstractTransactPlugin[];
    transactPluginsOptions?: TransactPluginsOptions;
    ui?: UserInterface;
}
/**
 * Temporary context created for the duration of a [[Session.transact]] call.
 *
 * This context is used to store the state of the transact request and
 * provide a way for plugins to add hooks into the process.
 */
declare class TransactContext {
    readonly abiCache: ABICacheInterface;
    readonly appName?: string;
    readonly chain: ChainDefinition;
    readonly client: APIClient;
    readonly createRequest: (args: TransactArgs) => Promise<SigningRequest>;
    readonly fetch: Fetch;
    readonly hooks: TransactHooks;
    info: API.v1.GetInfoResponse | undefined;
    readonly permissionLevel: PermissionLevel;
    readonly storage?: SessionStorage;
    readonly transactPluginsOptions: TransactPluginsOptions;
    readonly ui?: UserInterface;
    constructor(options: TransactContextOptions);
    get accountName(): Name;
    get permissionName(): Name;
    get esrOptions(): SigningRequestEncodingOptions;
    addHook(t: TransactHookTypes, hook: TransactHookMutable | TransactHookImmutable): void;
    getInfo(): Promise<API.v1.GetInfoResponse>;
    resolve(request: SigningRequest, expireSeconds?: number): Promise<ResolvedSigningRequest>;
}
/**
 * Payload accepted by the [[Session.transact]] method.
 * Note that one of `action`, `actions` or `transaction` must be set.
 */
interface TransactArgs {
    /** Full transaction to sign. */
    transaction?: AnyTransaction;
    /** Action to sign. */
    action?: AnyAction;
    /** Actions to sign. */
    actions?: AnyAction[];
    /** An ESR payload */
    request?: SigningRequest | string;
    /** Context free actions to include in the transaction */
    context_free_actions?: ActionType[];
    /** Context free data to include in the transaction */
    context_free_data?: string[];
}
/**
 * Options for the [[Session.transact]] method.
 */
interface TransactOptions {
    /**
     * An array of ABIs to use when resolving the transaction.
     */
    abis?: TransactABIDef[];
    /**
     * An optional ABICacheInterface to control how ABIs are loaded.
     */
    abiCache?: ABICacheInterface;
    /**
     * Whether to allow the signer to make modifications to the request
     * (e.g. applying a cosigner action to pay for resources).
     *
     * Defaults to true if [[broadcast]] is true or unspecified; otherwise false.
     */
    allowModify?: boolean;
    /**
     * Whether to broadcast the transaction or just return the signature.
     * Defaults to true.
     */
    broadcast?: boolean;
    /**
     * Chain to use when configured with multiple chains.
     */
    chain?: Checksum256Type;
    /**
     * An array of Contract instances to cache for this call
     */
    contracts?: Contract[];
    /**
     * The number of seconds in the future this transaction will expire.
     */
    expireSeconds?: number;
    /**
     * Specific transact plugins to use for this transaction.
     */
    transactPlugins?: AbstractTransactPlugin[];
    /**
     * Optional parameters passed in to the various transact plugins.
     */
    transactPluginsOptions?: TransactPluginsOptions;
    /**
     * Optional parameter to control whether signatures returned from plugins are validated.
     */
    validatePluginSignatures?: boolean;
}
interface TransactABIDef {
    account: NameType;
    abi: ABIDef;
}
interface TransactRevision {
    /**
     * Whether or not the context allowed any modification to take effect.
     */
    allowModify: boolean;
    /**
     * The string representation of the code executed.
     */
    code: string;
    /**
     * If the request was modified by this code.
     */
    modified: boolean;
    /**
     * The response from the code that was executed.
     */
    response: {
        request: string;
        signatures: string[];
    };
}
declare class TransactRevisions {
    readonly revisions: TransactRevision[];
    constructor(request: SigningRequest);
    addRevision(response: TransactHookResponse, code: string, allowModify: boolean): void;
}
/**
 * An interface to define a return type
 */
interface TransactResultReturnType {
    name: NameType;
    result_type: string;
}
/**
 * The return values from a [[Session.transact]] call that have been processed and decoded.
 */
interface TransactResultReturnValue {
    contract: Name;
    action: Name;
    hex: string;
    data: any;
    returnType: TransactResultReturnType;
}
/**
 * The response from a [[Session.transact]] call.
 */
interface TransactResult {
    /** The chain that was used. */
    chain: ChainDefinition;
    /** The SigningRequest representation of the transaction. */
    request: SigningRequest;
    /** The ResolvedSigningRequest of the transaction */
    resolved: ResolvedSigningRequest | undefined;
    /** The response from the API after sending the transaction, only present if transaction was broadcast. */
    response?: {
        [key: string]: any;
    };
    /** The return values provided by the transaction */
    returns: TransactResultReturnValue[];
    /** An array containing revisions of the transaction as modified by plugins as ESR payloads */
    revisions: TransactRevisions;
    /** The transaction signatures. */
    signatures: Signature[];
    /** The signer authority. */
    signer: PermissionLevel;
    /** The resulting transaction. */
    transaction: ResolvedTransaction | undefined;
}
/**
 * Interface which a [[Session.transact]] plugin must implement.
 */
interface TransactPlugin {
    /** A URL friendly (lower case, no spaces, etc) ID for this plugin - Used in serialization */
    get id(): string;
    /** Any translations this plugin requires */
    translations?: LocaleDefinitions;
    /** A function that registers hooks into the transaction flow */
    register: (context: TransactContext) => void;
}
/**
 * Abstract class for [[Session.transact]] plugins to extend.
 */
declare abstract class AbstractTransactPlugin implements TransactPlugin {
    translations?: LocaleDefinitions;
    abstract register(context: TransactContext): void;
    abstract get id(): string;
}
declare class BaseTransactPlugin extends AbstractTransactPlugin {
    get id(): string;
    register(): void;
}

/**
 * The static configuration of a [[WalletPlugin]].
 */
interface WalletPluginConfig {
    /**
     * Indicates if the pp requires the user to manually select the blockchain to authorize against.
     */
    requiresChainSelect: boolean;
    /**
     * Indicates if the [[WalletPlugin]] requires the user to select a permission to use from a list.
     */
    requiresPermissionSelect: boolean;
    /**
     * Indicates if the [[WalletPlugin]] requires the user to manually enter a permission to use.
     */
    requiresPermissionEntry?: boolean;
    /**
     * If set, indicates which blockchains are compatible with this [[WalletPlugin]].
     */
    supportedChains?: Checksum256Type[];
}
/**
 * The metadata of a [[WalletPlugin]] for display purposes.
 */
declare class WalletPluginMetadata extends Struct {
    /**
     * A display name for the wallet that is presented to users.
     */
    name?: string;
    /**
     * A wallet description to further identify the wallet for users.
     */
    description?: string;
    /**
     * Wallet branding
     */
    logo?: Logo;
    /**
     * Link to the homepage for the wallet
     */
    homepage?: string;
    /**
     * Link to the download page for the wallet
     */
    download?: string;
    /**
     * The public key being used by the wallet plugin
     */
    publicKey?: string;
    static from(data: any): WalletPluginMetadata;
}
/**
 * The response for a login call of a [[WalletPlugin]].
 */
interface WalletPluginLoginResponse {
    chain: Checksum256;
    permissionLevel: PermissionLevel;
    identityProof?: IdentityProof;
}
/**
 * The response for a sign call of a [[WalletPlugin]].
 */
interface WalletPluginSignResponse {
    resolved?: ResolvedSigningRequest;
    signatures: Signature[];
}
/**
 * Persistent storage format for wallet specified data.
 */
type WalletPluginData = Record<string, any>;
/**
 * The serialized form of a [[WalletPlugin]] instance.
 */
interface SerializedWalletPlugin {
    id: string;
    data: WalletPluginData;
}
/**
 * Interface which all 3rd party wallet plugins must implement.
 */
interface WalletPlugin {
    /** A URL friendly (lower case, no spaces, etc) ID for this plugin - Used in serialization */
    get id(): string;
    /** A method to return the data that needs to persist for the plguin - Used in serialization */
    get data(): WalletPluginData;
    set data(data: WalletPluginData);
    /** The [[SessionKit]] configuration parameters for this [[WalletPlugin]]. */
    config: WalletPluginConfig;
    /** The metadata for the [[WalletPlugin]] itself. */
    metadata: WalletPluginMetadata;
    /** Any translations this plugin requires */
    translations?: LocaleDefinitions;
    /**
     * Request the [[WalletPlugin]] to log in a user and return a [[WalletPluginLoginResponse]].
     *
     * @param context The [[LoginContext]] for the [[WalletPlugin]] to use.
     */
    login(context: LoginContext): Promise<WalletPluginLoginResponse>;
    /**
     * Requests the [[WalletPlugin]] to sign a transaction and return a [[WalletPluginSignResponse]]]
     *
     * @param transaction The transaction to sign.
     * @param context The [[TransactContext]] for the [[WalletPlugin]] to use.
     */
    sign(transaction: ResolvedSigningRequest, context: TransactContext): Promise<WalletPluginSignResponse>;
    /**
     * Optional method that is used to signal to the wallet plugin to logout the user.
     *
     * @param session The [[Session]] instance that is being logged out.
     * @returns A promise that resolves when the wallet plugin logout process is complete.
     * @throws An error if the logout could not happen.
     */
    logout?(context: LogoutContext): Promise<void>;
    /**
     * Serialize the [[WalletPlugin]] ID and data into a plain object.
     */
    serialize(): WalletPluginData;
    /**
     * Requests a public key from the wallet plugin.
     */
    retrievePublicKey?(chainId: Checksum256Type): Promise<PublicKey>;
}
/**
 * Abstract class which all 3rd party [[WalletPlugin]] implementations may extend.
 */
declare abstract class AbstractWalletPlugin implements WalletPlugin {
    _data: WalletPluginData;
    config: WalletPluginConfig;
    metadata: WalletPluginMetadata;
    translations?: LocaleDefinitions;
    abstract get id(): string;
    abstract login(context: LoginContext): Promise<WalletPluginLoginResponse>;
    abstract sign(transaction: ResolvedSigningRequest, context: TransactContext): Promise<WalletPluginSignResponse>;
    get data(): WalletPluginData;
    set data(data: WalletPluginData);
    serialize(): SerializedWalletPlugin;
}

declare enum LoginHookTypes {
    beforeLogin = "beforeLogin",
    afterLogin = "afterLogin"
}
type LoginHook = (context: LoginContext) => Promise<void>;
interface LoginHooks {
    afterLogin: LoginHook[];
    beforeLogin: LoginHook[];
}
/**
 * Options for creating a new context for a [[Kit.login]] call.
 */
interface LoginContextOptions {
    appName?: NameType;
    arbitrary?: Record<string, any>;
    chain?: ChainDefinition;
    chains?: ChainDefinition[];
    fetch: Fetch;
    loginPlugins?: AbstractLoginPlugin[];
    permissionLevel?: PermissionLevel;
    walletPlugins?: UserInterfaceWalletPlugin[];
    ui: UserInterface;
}
interface UserInterfaceRequirements {
    requiresChainSelect: boolean;
    requiresPermissionSelect: boolean;
    requiresPermissionEntry: boolean;
    requiresWalletSelect: boolean;
}
interface UserInterfaceWalletPlugin {
    config: WalletPluginConfig;
    metadata: WalletPluginMetadata;
    retrievePublicKey: ((chainId: Checksum256Type) => Promise<PublicKey>) | undefined;
}
/**
 * Temporary context created for the duration of a [[Kit.login]] call.
 *
 * This context is used to store the state of the login request and
 * provide a way for plugins to add hooks into the process.
 */
declare class LoginContext {
    appName?: string;
    arbitrary: Record<string, any>;
    chain?: ChainDefinition;
    chains: ChainDefinition[];
    fetch: Fetch;
    hooks: LoginHooks;
    permissionLevel?: PermissionLevel;
    ui: UserInterface;
    uiRequirements: UserInterfaceRequirements;
    walletPluginIndex?: number;
    walletPlugins: UserInterfaceWalletPlugin[];
    constructor(options: LoginContextOptions);
    addHook(t: LoginHookTypes, hook: LoginHook): void;
    getClient(chain: ChainDefinition): APIClient;
    get esrOptions(): SigningRequestEncodingOptions;
}
/**
 * Payload accepted by the [[Kit.login]] method.
 */
interface LoginPlugin {
    register: (context: LoginContext) => void;
}
/**
 * Abstract class for [[Kit.login]] plugins to extend.
 */
declare abstract class AbstractLoginPlugin implements LoginPlugin {
    abstract register(context: LoginContext): void;
}
declare class BaseLoginPlugin extends AbstractLoginPlugin {
    register(): void;
}

/**
 * Arguments required to create a new [[Session]].
 */
interface SessionArgs {
    actor?: NameType;
    chain: ChainDefinitionType;
    permission?: NameType;
    permissionLevel?: PermissionLevelType | string;
    walletPlugin: WalletPlugin;
}
/**
 * Options for creating a new [[Session]].
 */
interface SessionOptions {
    abis?: TransactABIDef[];
    abiCache?: ABICacheInterface;
    allowModify?: boolean;
    appName?: NameType;
    broadcast?: boolean;
    contracts?: Contract[];
    expireSeconds?: number;
    fetch?: Fetch;
    storage?: SessionStorage;
    transactPlugins?: AbstractTransactPlugin[];
    transactPluginsOptions?: TransactPluginsOptions;
    ui?: UserInterface;
}
interface SerializedSession {
    actor: NameType;
    chain: Checksum256Type;
    default?: boolean;
    permission: NameType;
    walletPlugin: SerializedWalletPlugin;
    data?: Record<string, any>;
}
/**
 * A representation of a session to interact with a specific blockchain account.
 */
declare class Session {
    readonly appName?: string;
    readonly abis: TransactABIDef[];
    readonly abiCache: ABICacheInterface;
    readonly allowModify: boolean;
    readonly broadcast: boolean;
    readonly chain: ChainDefinition;
    readonly expireSeconds: number;
    readonly fetch: Fetch;
    readonly permissionLevel: PermissionLevel;
    readonly storage?: SessionStorage;
    readonly transactPlugins: TransactPlugin[];
    readonly transactPluginsOptions: TransactPluginsOptions;
    readonly ui?: UserInterface;
    readonly walletPlugin: WalletPlugin;
    private _data;
    /**
     * Get the data stored in this session instance.
     */
    get data(): Record<string, any>;
    /**
     * Set the data stored in this session instance.
     */
    set data(data: Record<string, any>);
    /**
     * The constructor of the `Session` class.
     *
     * @param options SessionOptions
     */
    constructor(args: SessionArgs, options?: SessionOptions);
    /**
     * Returns the name of the actor that is being used for this session.
     */
    get actor(): Name;
    /**
     * Returns the name of the permission that is being used for this session.
     */
    get permission(): Name;
    /**
     * Returns an APIClient configured for this session.
     */
    get client(): APIClient;
    /**
     * Alters the session config to change the API endpoint in use
     */
    setEndpoint(url: string): void;
    /**
     * Templates in any missing fields from partial transactions.
     *
     * @param args TransactArgs
     * @returns TransactArgs
     */
    upgradeTransaction(args: TransactArgs): TransactArgs;
    /**
     * Lifted from @wharfkit/antelope-signing-request.
     *
     * Copy of: https://github.com/greymass/@wharfkit/signing-request/blob/6fc84b2355577d6461676bff417c76e4f6f2f5c3/src/signing-request.ts#L305
     *
     * TODO: Remove. This will no longer be needed once the `clone` functionality in ESR is updated
     */
    private storageType;
    /**
     * Create a clone of the given SigningRequest
     *
     * Overrides: https://github.com/greymass/@wharfkit/signing-request/blob/6fc84b2355577d6461676bff417c76e4f6f2f5c3/src/signing-request.ts#L1112
     *
     * @param {SigningRequest} request The SigningRequest to clone
     * @param {ABICacheInterface} abiCache The ABICacheInterface to use for the clone
     * @returns Returns a cloned SigningRequest with updated abiCache and zlib
     */
    cloneRequest(request: SigningRequest, abiCache: ABICacheInterface): SigningRequest;
    /**
     * Convert any provided form of TransactArgs to a SigningRequest
     *
     * @param {TransactArgs} args
     * @param {ABICacheInterface} abiCache
     * @returns Returns a SigningRequest
     */
    createRequest(args: TransactArgs, abiCache: ABICacheInterface): Promise<SigningRequest>;
    /**
     * Update a SigningRequest, ensuring its old metadata is retained.
     *
     * @param {SigningRequest} previous
     * @param {SigningRequest} modified
     * @param abiCache
     * @returns
     */
    updateRequest(previous: SigningRequest, modified: SigningRequest, abiCache: ABICacheInterface): Promise<SigningRequest>;
    /**
     * Perform a transaction using this session.
     *
     * @param {TransactArgs} args
     * @param {TransactOptions} options
     * @returns {TransactResult} The status and data gathered during the operation.
     * @mermaid - Transaction sequence diagram
     * flowchart LR
     *   A((Transact)) --> B{{"Hook(s): beforeSign"}}
     *   B --> C[Wallet Plugin]
     *   C --> D{{"Hook(s): afterSign"}}
     *   D --> E[Broadcast Plugin]
     *   E --> F{{"Hook(s): afterBroadcast"}}
     *   F --> G[TransactResult]
     */
    transact(args: TransactArgs, options?: TransactOptions): Promise<TransactResult>;
    /**
     * Request a signature for a given transaction.
     *
     * This function will NOT use plugins and will NOT broadcast the transaction.
     *
     * @param {TransactionType} transaction A full transaction-like object
     * @returns {Promise<Signature[]>} The signature(s) for the transaction
     */
    signTransaction(transaction: TransactionType): Promise<Signature[]>;
    serialize: () => SerializedSession;
    getPluginTranslations(transactPlugin: TransactPlugin | WalletPlugin): LocaleDefinitions;
    getMergedAbiCache(args: TransactArgs, options?: TransactOptions): ABICacheInterface;
}

/**
 * The static configuration of an [[AccountCreationPlugin]].
 */
interface AccountCreationPluginConfig {
    /**
     * Indicates if the plugin requires the user to manually select the blockchain to create an account on.
     */
    requiresChainSelect: boolean;
    /**
     * If set, indicates which blockchains are compatible with this [[AccountCreationPlugin]].
     */
    supportedChains?: ChainDefinition[];
}
/**
 * The metadata of an [[AccountCreationPlugin]].
 */
declare class AccountCreationPluginMetadata extends Struct {
    /**
     * A display name for the account creation service that is presented to users.
     */
    name: string;
    /**
     * A description to further identify the account creation service for users.
     */
    description?: string;
    /**
     * Account creation service branding.
     */
    logo?: Logo;
    /**
     * Link to the homepage for the account creation service.
     */
    homepage?: string;
    static from(data: any): AccountCreationPluginMetadata;
}
/**
 * Options for createAccount call.
 **/
interface CreateAccountOptions {
    accountName?: NameType;
    chain?: ChainDefinition;
    pluginId?: string;
}
/**
 * The response for a createAccount call.
 */
interface CreateAccountResponse {
    chain: ChainDefinition;
    accountName: NameType;
}
interface CreateAccountContextOptions {
    accountCreationPlugins?: AccountCreationPlugin[];
    appName?: NameType;
    chain?: ChainDefinition;
    chains?: ChainDefinition[];
    fetch: Fetch;
    ui: UserInterface;
    uiRequirements?: UserInterfaceAccountCreationRequirements;
}
interface UserInterfaceAccountCreationRequirements {
    requiresChainSelect: boolean;
    requiresPluginSelect: boolean;
}
declare class CreateAccountContext {
    accountCreationPlugins: AccountCreationPlugin[];
    appName?: string;
    chain?: ChainDefinition;
    chains?: ChainDefinition[];
    fetch: Fetch;
    ui: UserInterface;
    uiRequirements: UserInterfaceAccountCreationRequirements;
    constructor(options: CreateAccountContextOptions);
    getClient(chain: ChainDefinition): APIClient;
}
/**
 * Interface which all 3rd party account creation plugins must implement.
 */
interface AccountCreationPlugin {
    /** A URL friendly (lower case, no spaces, etc) ID for this plugin - Used in serialization */
    get id(): string;
    /** A display name for the account creation service that is presented to users. */
    get name(): string;
    /** The [[SessionKit]] configuration parameters for this [[WalletPlugin]]. */
    config: AccountCreationPluginConfig;
    /** Any translations this plugin requires */
    translations?: LocaleDefinitions;
    /**
     * Request the [[AccountCreationPlugin]] to create a new account.
     *
     * @param context The [[AccountCreationContext]] for the [[WalletPlugin]] to use.
     */
    create(options: CreateAccountContext): Promise<CreateAccountResponse>;
}
/**
 * Abstract class which all 3rd party [[AccountCreation]] implementations may extend.
 */
declare abstract class AbstractAccountCreationPlugin implements AccountCreationPlugin {
    config: AccountCreationPluginConfig;
    metadata: AccountCreationPluginMetadata;
    translations?: LocaleDefinitions;
    abstract get id(): string;
    abstract get name(): string;
    abstract create(options: CreateAccountOptions): Promise<CreateAccountResponse>;
}

interface LoginOptions {
    arbitrary?: Record<string, any>;
    chain?: ChainDefinition | Checksum256Type;
    chains?: Checksum256Type[];
    loginPlugins?: LoginPlugin[];
    setAsDefault?: boolean;
    transactPlugins?: TransactPlugin[];
    transactPluginsOptions?: TransactPluginsOptions;
    permissionLevel?: PermissionLevelType | string;
    walletPlugin?: string;
}
interface LoginResult {
    context: LoginContext;
    response: WalletPluginLoginResponse;
    session: Session;
}
interface LogoutContext {
    session: Session;
    appName: string;
}
interface RestoreArgs {
    chain: Checksum256Type | ChainDefinition;
    actor?: NameType;
    permission?: NameType;
    walletPlugin?: Record<string, any>;
    data?: Record<string, any>;
}
interface SessionKitArgs {
    appName: NameType;
    chains: ChainDefinitionType[];
    ui: UserInterface;
    walletPlugins: WalletPlugin[];
}
interface SessionKitOptions {
    abis?: TransactABIDef[];
    allowModify?: boolean;
    contracts?: Contract[];
    expireSeconds?: number;
    fetch?: Fetch;
    loginPlugins?: LoginPlugin[];
    storage?: SessionStorage;
    transactPlugins?: TransactPlugin[];
    transactPluginsOptions?: TransactPluginsOptions;
    accountCreationPlugins?: AccountCreationPlugin[];
}
/**
 * Request a session from an account.
 */
declare class SessionKit {
    readonly abis: TransactABIDef[];
    readonly allowModify: boolean;
    readonly appName: string;
    readonly expireSeconds: number;
    readonly fetch: Fetch;
    readonly loginPlugins: AbstractLoginPlugin[];
    readonly storage: SessionStorage;
    readonly transactPlugins: AbstractTransactPlugin[];
    readonly transactPluginsOptions: TransactPluginsOptions;
    readonly ui: UserInterface;
    readonly walletPlugins: WalletPlugin[];
    readonly accountCreationPlugins: AccountCreationPlugin[];
    chains: ChainDefinition[];
    constructor(args: SessionKitArgs, options?: SessionKitOptions);
    /**
     * Alters the session kit config for a specific chain to change the API endpoint in use
     */
    setEndpoint(id: Checksum256Type, url: string): void;
    getChainDefinition(id: Checksum256Type, override?: ChainDefinition[]): ChainDefinition;
    /**
     * Request account creation.
     */
    createAccount(options?: CreateAccountOptions): Promise<CreateAccountResponse>;
    /**
     * Request a session from an account.
     *
     * @mermaid - Login sequence diagram
     * flowchart LR
     *   A((Login)) --> B{{"Hook(s): beforeLogin"}}
     *   B --> C[Wallet Plugin]
     *   C --> D{{"Hook(s): afterLogin"}}
     *   D --> E[Session]
     */
    login(options?: LoginOptions): Promise<LoginResult>;
    logoutParams(session: Session | SerializedSession, walletPlugin: WalletPlugin): LogoutContext;
    logout(session?: Session | SerializedSession): Promise<void>;
    restore(args?: RestoreArgs, options?: LoginOptions): Promise<Session | undefined>;
    restoreAll(): Promise<Session[]>;
    persistSession(session: Session, setAsDefault?: boolean): Promise<void>;
    getSessions(): Promise<SerializedSession[]>;
    getSessionOptions(options?: LoginOptions): {
        abis: TransactABIDef[];
        allowModify: boolean;
        appName: string;
        expireSeconds: number;
        fetch: Fetch;
        storage: SessionStorage;
        transactPlugins: AbstractTransactPlugin[];
        transactPluginsOptions: TransactPluginsOptions;
        ui: UserInterface;
    };
}

/**
 * Return an instance of fetch.
 *
 * @param options FetchProviderOptions
 * @returns Fetch
 */
declare function getFetch(options?: FetchProviderOptions): Fetch;
/**
 * Append an action to the end of the array of actions in a SigningRequest.
 *
 * @param request SigningRequest
 * @param action AnyAction
 * @returns SigningRequest
 */
declare function appendAction(request: SigningRequest, action: AnyAction): SigningRequest;
/**
 * Prepend an action to the end of the array of actions in a SigningRequest.
 *
 * @param request SigningRequest
 * @param action AnyAction
 * @returns SigningRequest
 */
declare function prependAction(request: SigningRequest, action: AnyAction): SigningRequest;

export { AbstractAccountCreationPlugin, AbstractLoginPlugin, AbstractTransactPlugin, AbstractUserInterface, AbstractWalletPlugin, AccountCreationPlugin, AccountCreationPluginConfig, AccountCreationPluginMetadata, BaseLoginPlugin, BaseTransactPlugin, BrowserLocalStorage, CreateAccountContext, CreateAccountContextOptions, CreateAccountOptions, CreateAccountResponse, LoginContext, LoginContextOptions, LoginHook, LoginHookTypes, LoginHooks, LoginOptions, LoginPlugin, LoginResult, LogoutContext, PromptArgs, PromptElement, PromptResponse, RestoreArgs, SerializedSession, SerializedWalletPlugin, Session, SessionArgs, SessionKit, SessionKitArgs, SessionKitOptions, SessionOptions, SessionStorage, TransactABIDef, TransactArgs, TransactContext, TransactContextOptions, TransactHookImmutable, TransactHookMutable, TransactHookResponse, TransactHookResponseType, TransactHookTypes, TransactHooks, TransactOptions, TransactPlugin, TransactPluginsOptions, TransactResult, TransactResultReturnType, TransactResultReturnValue, TransactRevision, TransactRevisions, UserInterface, UserInterfaceAccountCreationRequirements, UserInterfaceAccountCreationResponse, UserInterfaceLoginResponse, UserInterfaceRequirements, UserInterfaceTranslateFunction, UserInterfaceTranslateOptions, UserInterfaceWalletPlugin, WalletPlugin, WalletPluginConfig, WalletPluginData, WalletPluginLoginResponse, WalletPluginMetadata, WalletPluginSignResponse, appendAction, SessionKit as default, getFetch, prependAction };
