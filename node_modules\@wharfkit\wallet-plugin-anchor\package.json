{"name": "@wharfkit/wallet-plugin-anchor", "description": "An Anchor plugin for use with @wharfkit/session.", "version": "1.5.0", "homepage": "https://github.com/wharfkit/wallet-plugin-anchor", "license": "BSD-3-<PERSON><PERSON>", "main": "lib/wallet-plugin-anchor.js", "module": "lib/wallet-plugin-anchor.m.js", "types": "lib/wallet-plugin-anchor.d.ts", "sideEffects": false, "files": ["lib/*", "src/*"], "scripts": {"prepare": "make"}, "dependencies": {"@wharfkit/antelope": "^1.0.5", "@wharfkit/protocol-esr": "^1.4.0", "isomorphic-ws": "^5.0.0", "ws": "^8.13.0"}, "resolutions": {"@wharfkit/antelope": "^1.0.5"}, "peerDependencies": {"@wharfkit/session": "^1.2.7"}, "devDependencies": {"@rollup/plugin-alias": "^3.1.4", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-replace": "^5.0.1", "@rollup/plugin-typescript": "^10.0.1", "@rollup/plugin-virtual": "^2.0.3", "@types/chai": "^4.3.1", "@types/mocha": "^9.0.0", "@types/node": "^18.7.18", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "@wharfkit/mock-data": "^1.3.0", "@wharfkit/session": "^1.2.7", "@wharfkit/signing-request": "^3.1.0", "@wharfkit/wallet-plugin-privatekey": "^1.1.0", "chai": "^4.3.4", "eslint": "^8.13.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0", "gh-pages": "^4.0.0", "mocha": "^10.0.0", "node-fetch": "^2.6.1", "nyc": "^15.1.0", "pako": "^2.1.0", "prettier": "^2.2.1", "rollup": "^2.70.2", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-terser": "^7.0.2", "sinon": "^15.0.1", "sinon-chai": "^3.7.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.1.1", "tslib": "^2.1.0", "typedoc": "^0.23.14", "typescript": "^4.1.2", "yarn-deduplicate": "^6.0.1"}}