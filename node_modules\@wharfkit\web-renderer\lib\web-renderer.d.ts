import { AbstractUserInterface, UserInterface, LoginContext, UserInterfaceLoginResponse, CreateAccountContext, UserInterfaceAccountCreationResponse, PromptArgs, Cancelable, PromptResponse, UserInterfaceTranslateOptions } from '@wharfkit/session';

interface WebRendererOptions {
    id?: string;
    logging?: boolean;
    minimal?: boolean;
    translations?: Record<string, Record<string, string>>;
}
declare const defaultWebRendererOptions: {
    id: string;
    minimal: boolean;
};
declare class WebRenderer extends AbstractUserInterface implements UserInterface {
    static version: string;
    elementId: string;
    element: Element | undefined;
    shadow: ShadowRoot | undefined;
    options: WebRendererOptions;
    i18n: any;
    initialized: boolean;
    logging: boolean;
    minimal: boolean;
    constructor(options?: WebRendererOptions);
    initialize(): void;
    appendDialogElement(): void;
    addCancelablePromise: (promise: any) => void;
    log(...args: any[]): void;
    login(context: LoginContext): Promise<UserInterfaceLoginResponse>;
    onError(error: Error): Promise<void>;
    onAccountCreate(context: CreateAccountContext): Promise<UserInterfaceAccountCreationResponse>;
    onAccountCreateComplete(): Promise<void>;
    onLogin(): Promise<void>;
    onLoginComplete(): Promise<void>;
    onTransact(): Promise<void>;
    onTransactComplete(): Promise<void>;
    onSign(): Promise<void>;
    onSignComplete(): Promise<void>;
    onBroadcast(): Promise<void>;
    onBroadcastComplete(): Promise<void>;
    prompt(args: PromptArgs): Cancelable<PromptResponse>;
    status(message: string): void;
    translate(key: string, options?: UserInterfaceTranslateOptions, namespace?: string): any;
    addTranslations(translations: any): void;
}

export { WebRenderer, WebRendererOptions, WebRenderer as default, defaultWebRendererOptions };
