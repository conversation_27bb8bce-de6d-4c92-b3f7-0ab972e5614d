<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NFT Access Demo</title>
    <style>
        :root {
            --primary-color: #3e67ff;
            --secondary-color: #ff5e3e;
            --background-color: #f9f9f9;
            --card-background: #ffffff;
            --text-color: #333333;
            --border-radius: 12px;
            --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }
        
        h1, h2, h3 {
            color: var(--primary-color);
        }
        
        button { 
            padding: 12px 20px; 
            cursor: pointer; 
            margin-right: 10px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background-color: #2a4cd7;
            transform: translateY(-2px);
        }
        
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        #content { 
            display: none; 
            margin-top: 30px;
            background-color: var(--card-background);
            padding: 25px;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }
        
        #login-section { 
            text-align: center; 
            margin-top: 50px;
            background-color: var(--card-background);
            padding: 30px;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }
        
        .error { 
            color: var(--secondary-color); 
            margin-top: 20px;
            background-color: rgba(255, 94, 62, 0.1);
            padding: 15px;
            border-radius: var(--border-radius);
        }
        
        .button-group { 
            margin-top: 25px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .nft-card {
            background-color: rgba(62, 103, 255, 0.05);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-top: 20px;
            border: 1px solid rgba(62, 103, 255, 0.2);
        }
        
        .nft-image {
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius);
            margin-top: 15px;
            box-shadow: var(--box-shadow);
        }
        
        .buy-nft-container {
            margin-top: 20px;
        }
        
        .buy-nft-button {
            display: inline-block;
            padding: 12px 20px;
            background-color: var(--secondary-color);
            color: white;
            text-decoration: none;
            border-radius: var(--border-radius);
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .buy-nft-button:hover {
            background-color: #e54935;
            transform: translateY(-2px);
        }
        
        .error-details {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        #account-info, #nft-info {
            margin-top: 20px;
        }
        
        #logout-button {
            margin-top: 20px;
            background-color: var(--secondary-color);
        }
        
        #logout-button:hover {
            background-color: #e54935;
        }
        
        #clear-sessions-button {
            background-color: #6c757d;
        }
        
        #clear-sessions-button:hover {
            background-color: #5a6268;
        }
    </style>
</head>
<body>
    <div id="login-section">
        <h1>NFT Access Demo</h1>
        <p>Login with Anchor to access this exclusive content. You need an NFT from the "Easter PAX 2023" collection.</p>
        <div class="button-group">
            <button id="login-button">Login with Anchor</button>
            <button id="clear-sessions-button">Clear Stored Sessions</button>
        </div>
        <div id="login-error" class="error"></div>
    </div>
    
    <div id="content">
        <h2>Welcome! You have access</h2>
        <p>You successfully authenticated and own an NFT from the required collection.</p>
        <div id="account-info"></div>
        <div id="nft-info"></div>
        <button id="logout-button">Logout</button>
    </div>

    <script type="module" src="./main.js"></script>
</body>
</html>


