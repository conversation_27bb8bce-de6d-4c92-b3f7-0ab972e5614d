
import { Session, SessionKit } from '@wharfkit/session';
import { WalletPluginAnchor } from '@wharfkit/wallet-plugin-anchor';
import { WebRenderer } from '@wharfkit/web-renderer';

// Configuration
const APP_NAME = 'NFT Access Control';
const CHAIN_ID = 'aca376f206b8fc25a6ed44dbdc66547c36c6c33e3a119ffbeaef943642f0e906'; // WAX mainnet
const CHAIN_URL = 'https://eos.greymass.com';
const NFT_COLLECTION = 'easterpax23y';
const ATOMICASSETS_CONTRACT = 'atomicassets';

// Initialize SessionKit
const sessionKit = new SessionKit({
    appName: APP_NAME,
    chains: [{
        id: CHAIN_ID,
        url: CHAIN_URL
    }],
    ui: new WebRenderer(),
    walletPlugins: [new WalletPluginAnchor()]
});

// UI Elements
let elements = {};

// Initialize UI elements after DOM is loaded
function initializeElements() {
    elements = {
        loginButton: document.getElementById('login-button'),
        logoutButton: document.getElementById('logout-button'),
        clearSessionsButton: document.getElementById('clear-sessions-button'),
        loginSection: document.getElementById('login-section'),
        content: document.getElementById('content'),
        loginError: document.getElementById('login-error'),
        accountInfo: document.getElementById('account-info'),
        nftInfo: document.getElementById('nft-info')
    };
}

// Session management
async function saveSession(session) {
    try {
        const sessionData = {
            actor: session.actor.toString(),
            permission: session.permission.toString(),
            walletPlugin: session.walletPlugin.id,
            chain: CHAIN_ID,
            timestamp: Date.now()
        };
        localStorage.setItem('wax_session', JSON.stringify(sessionData));
        console.log('Session saved to localStorage');
    } catch (error) {
        console.error('Error saving session:', error);
    }
}

async function loadSession() {
    try {
        const savedSession = localStorage.getItem('wax_session');
        if (!savedSession) {
            console.log('No saved session found');
            return null;
        }

        const sessionData = JSON.parse(savedSession);
        
        // Check if session is less than 24 hours old
        const sessionAge = Date.now() - sessionData.timestamp;
        if (sessionAge > 24 * 60 * 60 * 1000) {
            console.log('Session expired');
            localStorage.removeItem('wax_session');
            return null;
        }

        // Restore session
        const session = await sessionKit.restore({
            actor: sessionData.actor,
            permission: sessionData.permission,
            walletPlugin: sessionData.walletPlugin,
            chain: sessionData.chain
        });

        console.log('Session restored from localStorage');
        return session;
    } catch (error) {
        console.error('Error loading session:', error);
        localStorage.removeItem('wax_session');
        return null;
    }
}

function clearSession() {
    localStorage.removeItem('wax_session');
    console.log('Session cleared from localStorage');
}

// NFT Verification
async function verifyNFTOwnership(session, accountName) {
    try {
        console.log(`Verifying NFT ownership for ${accountName} in collection: ${NFT_COLLECTION}`);
        
        // Query the blockchain directly using the session's client
        const result = await session.client.v1.chain.get_table_rows({
            code: ATOMICASSETS_CONTRACT,
            scope: accountName,
            table: 'assets',
            limit: 1000,
            json: true
        });

        if (!result.rows || result.rows.length === 0) {
            console.log('No assets found for account');
            return false;
        }

        // Check if user owns any NFT from the specified collection
        const hasNFT = result.rows.some(asset => {
            // Parse the immutable data to get collection name
            try {
                const immutableData = typeof asset.immutable_serialized_data === 'string' 
                    ? JSON.parse(asset.immutable_serialized_data) 
                    : asset.immutable_serialized_data;
                
                return asset.collection_name === NFT_COLLECTION || 
                       (immutableData && immutableData.collection === NFT_COLLECTION);
            } catch (e) {
                // If parsing fails, just check the collection_name field
                return asset.collection_name === NFT_COLLECTION;
            }
        });

        console.log(`NFT verification result: ${hasNFT}`);
        return hasNFT;
    } catch (error) {
        console.error('Error verifying NFT ownership:', error);
        
        // Fallback: Try alternative query structure
        try {
            const fallbackResult = await session.client.v1.chain.get_table_rows({
                code: ATOMICASSETS_CONTRACT,
                scope: ATOMICASSETS_CONTRACT,
                table: 'assets',
                lower_bound: accountName,
                upper_bound: accountName,
                index_position: 2,
                key_type: 'name',
                limit: 100,
                json: true
            });

            if (fallbackResult.rows && fallbackResult.rows.length > 0) {
                return fallbackResult.rows.some(asset => 
                    asset.owner === accountName && asset.collection_name === NFT_COLLECTION
                );
            }
        } catch (fallbackError) {
            console.error('Fallback query also failed:', fallbackError);
        }
        
        return false;
    }
}

// UI Update Functions
function showError(message) {
    if (elements.loginError) {
        elements.loginError.textContent = message;
        elements.loginError.style.display = 'block';
        setTimeout(() => {
            elements.loginError.style.display = 'none';
        }, 5000);
    }
}

function hideError() {
    if (elements.loginError) {
        elements.loginError.style.display = 'none';
    }
}

function updateUIForLoggedInUser(accountName, hasNFT) {
    // Hide login section
    if (elements.loginSection) {
        elements.loginSection.style.display = 'none';
    }
    
    // Show content section
    if (elements.content) {
        elements.content.style.display = 'block';
    }
    
    // Update account info
    if (elements.accountInfo) {
        elements.accountInfo.innerHTML = `
            <p><strong>Logged in as:</strong> ${accountName}</p>
            <p><strong>Status:</strong> ${hasNFT ? '✅ Verified' : '❌ No NFT Found'}</p>
        `;
    }
    
    // Update NFT info
    if (elements.nftInfo) {
        if (hasNFT) {
            elements.nftInfo.innerHTML = `
                <div class="success-message">
                    <h3>🎉 Access Granted!</h3>
                    <p>You own an NFT from the "${NFT_COLLECTION}" collection.</p>
                    <p>You now have full access to the exclusive content.</p>
                </div>
            `;
        } else {
            elements.nftInfo.innerHTML = `
                <div class="warning-message">
                    <h3>⚠️ NFT Required</h3>
                    <p>You need to own an NFT from the "${NFT_COLLECTION}" collection to access this content.</p>
                    <p>Please acquire the required NFT and try logging in again.</p>
                </div>
            `;
        }
    }
}

function updateUIForLoggedOutUser() {
    // Show login section
    if (elements.loginSection) {
        elements.loginSection.style.display = 'block';
    }
    
    // Hide content section
    if (elements.content) {
        elements.content.style.display = 'none';
    }
    
    // Clear account and NFT info
    if (elements.accountInfo) {
        elements.accountInfo.innerHTML = '';
    }
    if (elements.nftInfo) {
        elements.nftInfo.innerHTML = '';
    }
}

// Authentication Functions
async function login() {
    try {
        hideError();
        console.log('Starting login process...');
        
        // Show loading state
        if (elements.loginButton) {
            elements.loginButton.disabled = true;
            elements.loginButton.textContent = 'Connecting...';
        }
        
        // Perform login
        const response = await sessionKit.login();
        
        if (!response || !response.session) {
            throw new Error('Login failed: No session returned');
        }
        
        const session = response.session;
        const accountName = session.actor.toString();
        
        console.log(`Logged in successfully as ${accountName}`);
        
        // Save session
        await saveSession(session);
        
        // Verify NFT ownership
        const hasNFT = await verifyNFTOwnership(session, accountName);
        
        // Update UI
        updateUIForLoggedInUser(accountName, hasNFT);
        
    } catch (error) {
        console.error('Login error:', error);
        showError(`Login failed: ${error.message || 'Unknown error occurred'}`);
    } finally {
        // Reset button state
        if (elements.loginButton) {
            elements.loginButton.disabled = false;
            elements.loginButton.textContent = 'Login with Anchor';
        }
    }
}

async function logout() {
    try {
        console.log('Logging out...');
        
        // Clear session from localStorage
        clearSession();
        
        // Clear any active sessions in SessionKit
        await sessionKit.logout();
        
        // Update UI
        updateUIForLoggedOutUser();
        
        console.log('Logged out successfully');
    } catch (error) {
        console.error('Logout error:', error);
        showError(`Logout failed: ${error.message || 'Unknown error occurred'}`);
    }
}

function clearAllSessions() {
    try {
        console.log('Clearing all sessions...');
        
        // Clear all localStorage data related to sessions
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && (key.includes('wax') || key.includes('session') || key.includes('wharf'))) {
                keysToRemove.push(key);
            }
        }
        
        keysToRemove.forEach(key => localStorage.removeItem(key));
        
        // Update UI
        updateUIForLoggedOutUser();
        
        console.log('All sessions cleared');
        showError('All sessions have been cleared');
    } catch (error) {
        console.error('Error clearing sessions:', error);
        showError(`Failed to clear sessions: ${error.message || 'Unknown error occurred'}`);
    }
}

// Auto-login on page load
async function autoLogin() {
    try {
        console.log('Checking for existing session...');
        
        const session = await loadSession();
        
        if (session) {
            const accountName = session.actor.toString();
            console.log(`Found existing session for ${accountName}`);
            
            // Verify NFT ownership
            const hasNFT = await verifyNFTOwnership(session, accountName);
            
            // Update UI
            updateUIForLoggedInUser(accountName, hasNFT);
        } else {
            console.log('No existing session found');
            updateUIForLoggedOutUser();
        }
    } catch (error) {
        console.error('Auto-login error:', error);
        updateUIForLoggedOutUser();
    }
}

// Initialize application
async function init() {
    console.log('Initializing NFT Access Control application...');
    
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        await new Promise(resolve => {
            document.addEventListener('DOMContentLoaded', resolve);
        });
    }
    
    // Initialize UI elements
    initializeElements();
    
    // Attach event listeners
    if (elements.loginButton) {
        elements.loginButton.addEventListener('click', login);
    }
    
    if (elements.logoutButton) {
        elements.logoutButton.addEventListener('click', logout);
    }
    
    if (elements.clearSessionsButton) {
        elements.clearSessionsButton.addEventListener('click', clearAllSessions);
    }
    
    // Attempt auto-login
    await autoLogin();
    
    console.log('Application initialized successfully');
}

// Start the application
init().catch(error => {
    console.error('Failed to initialize application:', error);
    showError('Failed to initialize application. Please refresh the page.');
});

// Export for module usage
export { login, logout, clearAllSessions, verifyNFTOwnership };
