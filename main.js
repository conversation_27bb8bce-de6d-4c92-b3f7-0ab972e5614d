import { SessionKit } from '@wharfkit/session'
import { WalletPluginAnchor } from '@wharfkit/wallet-plugin-anchor'
import { WebRenderer } from '@wharfkit/web-renderer'

// Initialize SessionKit with the correct EOS mainnet chain ID
const sessionKit = new SessionKit({
    appName: 'NFT Access Demo',
    chains: [
        {
            id: 'aca376f206b8fc25a6ed44dbdc66547c36c6c33e3a119ffbeaef943642f0e906',
            url: 'https://eos.greymass.com',
            // Adding these additional properties to ensure proper chain definition
            name: 'EOS',
            displayName: 'EOS Mainnet'
        }
    ],
    walletPlugins: [new WalletPluginAnchor()],
    ui: new WebRenderer()
})

let currentSession = null

// Clear any stored sessions
function clearStoredSessions() {
    // Clear WharfKit stored sessions
    localStorage.removeItem('wharf--session')
    localStorage.removeItem('wharf--sessions')
    
    // Clear any web-renderer settings
    localStorage.removeItem('wharf-web.renderer-settings')
    
    console.log('Cleared all stored sessions')
}

// Check if user has NFTs from the required collection using native EOS API
async function checkNFTAccess(accountName) {
    try {
        // First, verify the collection exists
        const collectionResponse = await fetch('https://eos.greymass.com/v1/chain/get_table_rows', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                code: 'atomicassets',
                scope: 'atomicassets',
                table: 'collections',
                lower_bound: 'yourfreenfts',
                upper_bound: 'yourfreenfts',
                limit: 1,
                json: true
            })
        })

        if (!collectionResponse.ok) {
            throw new Error(`HTTP error! status: ${collectionResponse.status}`)
        }

        const collectionData = await collectionResponse.json()

        if (collectionData.rows.length === 0) {
            document.getElementById('login-error').textContent =
                "Error: The 'yourfreenfts' collection does not exist on EOS mainnet."
            return false
        }

        // Check if the user is the collection owner (special case for demo)
        const collection = collectionData.rows[0]
        if (collection.author === accountName) {
            // Collection owner gets automatic access
            document.getElementById('login-section').style.display = 'none'
            document.getElementById('content').style.display = 'block'

            // Display account info
            document.getElementById('account-info').innerHTML = `
                <p><strong>Account:</strong> ${accountName}</p>
                <p><strong>Status:</strong> Collection Owner</p>
            `

            // Display collection info
            document.getElementById('nft-info').innerHTML = `
                <p><strong>Collection:</strong> ${collection.collection_name}</p>
                <p><strong>Role:</strong> Collection Author/Owner</p>
                <p><strong>Market Fee:</strong> ${(parseFloat(collection.market_fee) * 100).toFixed(2)}%</p>
            `
            return true
        }

        // Query for assets owned by the user in this collection
        // Try multiple approaches since the index structure might vary
        const assetResponse = await fetch('https://eos.greymass.com/v1/chain/get_table_rows', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                code: 'atomicassets',
                scope: 'atomicassets',
                table: 'assets',
                limit: 1000, // Increase limit to find user's assets
                json: true
            })
        })

        if (!assetResponse.ok) {
            throw new Error(`HTTP error! status: ${assetResponse.status}`)
        }

        const assetData = await assetResponse.json()

        // Filter assets by owner and collection
        const userAssets = assetData.rows.filter(asset => {
            // Check if asset has owner field and matches our criteria
            return asset.owner === accountName && asset.collection_name === 'yourfreenfts'
        })

        if (userAssets.length > 0) {
            // User has at least one NFT from the collection
            document.getElementById('login-section').style.display = 'none'
            document.getElementById('content').style.display = 'block'

            // Display account info
            document.getElementById('account-info').innerHTML = `
                <p><strong>Account:</strong> ${accountName}</p>
                <p><strong>NFTs Found:</strong> ${userAssets.length}</p>
            `

            // Display first NFT info
            const nft = userAssets[0]
            document.getElementById('nft-info').innerHTML = `
                <p><strong>NFT ID:</strong> ${nft.asset_id}</p>
                <p><strong>Collection:</strong> ${nft.collection_name}</p>
                <p><strong>Schema:</strong> ${nft.schema_name}</p>
                <p><strong>Template ID:</strong> ${nft.template_id || 'N/A'}</p>
            `
            return true
        } else {
            // User doesn't have any NFTs from the collection
            document.getElementById('login-error').textContent =
                `Access denied: You don't own any NFTs from the 'yourfreenfts' collection. The collection exists but no assets were found for account '${accountName}'.`
            return false
        }
    } catch (error) {
        console.error('Error checking NFT access:', error)
        document.getElementById('login-error').textContent =
            `Error checking NFT access: ${error.message}. Please try again.`
        return false
    }
}

// Login function
async function login() {
    try {
        document.getElementById('login-error').textContent = ''
        
        // Clear any existing sessions first
        clearStoredSessions()
        
        // Create new session
        const { session: newSession } = await sessionKit.login()
        currentSession = newSession
        await checkNFTAccess(newSession.actor.toString())
    } catch (error) {
        console.error('Login error:', error)
        document.getElementById('login-error').textContent = 
            "Error during login. Please try again."
    }
}

// Logout function
async function logout() {
    if (currentSession) {
        await sessionKit.logout(currentSession)
        currentSession = null
    }
    // Also clear stored sessions
    clearStoredSessions()
    document.getElementById('content').style.display = 'none'
    document.getElementById('login-section').style.display = 'block'
    document.getElementById('login-error').textContent = ''
}

// Event listeners
document.getElementById('login-button').addEventListener('click', login)
document.getElementById('logout-button').addEventListener('click', logout)
document.getElementById('clear-sessions-button').addEventListener('click', () => {
    clearStoredSessions()
    alert('All stored sessions have been cleared. You can now try logging in again.')
})

// Clear sessions on page load instead of trying to restore
clearStoredSessions()




