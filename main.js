import { SessionKit } from '@wharfkit/session'
import { WalletPluginAnchor } from '@wharfkit/wallet-plugin-anchor'
import { WebRenderer } from '@wharfkit/web-renderer'

// Initialize SessionKit with the correct EOS mainnet chain ID
const sessionKit = new SessionKit({
    appName: 'NFT Access Demo',
    chains: [
        {
            id: 'aca376f206b8fc25a6ed44dbdc66547c36c6c33e3a119ffbeaef943642f0e906',
            url: 'https://eos.greymass.com',
            // Adding these additional properties to ensure proper chain definition
            name: 'EOS',
            displayName: 'EOS Mainnet'
        }
    ],
    walletPlugins: [new WalletPluginAnchor()],
    ui: new WebRenderer()
})

let currentSession = null

// Clear any stored sessions
function clearStoredSessions() {
    // Clear WharfKit stored sessions
    localStorage.removeItem('wharf--session')
    localStorage.removeItem('wharf--sessions')
    
    // Clear any web-renderer settings
    localStorage.removeItem('wharf-web.renderer-settings')
    
    console.log('Cleared all stored sessions')
}

// Check if user has NFTs from the required collection
async function checkNFTAccess(accountName) {
    try {
        const response = await fetch(`https://eos.api.atomicassets.io/atomicassets/v1/assets?owner=${accountName}&collection_name=yourfreenfts&limit=1`)
        const data = await response.json()
        
        if (data.success && data.data.length > 0) {
            // User has at least one NFT from the collection
            document.getElementById('login-section').style.display = 'none'
            document.getElementById('content').style.display = 'block'
            
            // Display account info
            document.getElementById('account-info').innerHTML = `
                <p><strong>Account:</strong> ${accountName}</p>
            `
            
            // Display NFT info
            const nft = data.data[0]
            document.getElementById('nft-info').innerHTML = `
                <p><strong>NFT ID:</strong> ${nft.asset_id}</p>
                <p><strong>Collection:</strong> ${nft.collection.name}</p>
                <p><strong>Template:</strong> ${nft.template?.immutable_data?.name || 'N/A'}</p>
            `
            return true
        } else {
            // User doesn't have any NFTs from the collection
            document.getElementById('login-error').textContent = 
                "Access denied: You don't own any NFTs from the 'yourfreenfts' collection."
            return false
        }
    } catch (error) {
        console.error('Error checking NFT access:', error)
        document.getElementById('login-error').textContent = 
            "Error checking NFT access. Please try again."
        return false
    }
}

// Login function
async function login() {
    try {
        document.getElementById('login-error').textContent = ''
        
        // Clear any existing sessions first
        clearStoredSessions()
        
        // Create new session
        const { session: newSession } = await sessionKit.login()
        currentSession = newSession
        await checkNFTAccess(newSession.actor.toString())
    } catch (error) {
        console.error('Login error:', error)
        document.getElementById('login-error').textContent = 
            "Error during login. Please try again."
    }
}

// Logout function
async function logout() {
    if (currentSession) {
        await sessionKit.logout(currentSession)
        currentSession = null
    }
    // Also clear stored sessions
    clearStoredSessions()
    document.getElementById('content').style.display = 'none'
    document.getElementById('login-section').style.display = 'block'
    document.getElementById('login-error').textContent = ''
}

// Event listeners
document.getElementById('login-button').addEventListener('click', login)
document.getElementById('logout-button').addEventListener('click', logout)
document.getElementById('clear-sessions-button').addEventListener('click', () => {
    clearStoredSessions()
    alert('All stored sessions have been cleared. You can now try logging in again.')
})

// Clear sessions on page load instead of trying to restore
clearStoredSessions()




