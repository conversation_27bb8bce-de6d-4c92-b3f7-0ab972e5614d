import { ABI, API, NameType, ABIDef, APIClient } from '@wharfkit/antelope';
import { AbiProvider } from '@wharfkit/signing-request';

interface ABICacheInterface extends AbiProvider {
    readonly cache: Map<string, ABI>;
    readonly pending: Map<string, Promise<API.v1.GetRawAbiResponse>>;
    getAbi(account: NameType): Promise<ABI>;
    setAbi(account: NameType, abi: ABIDef, merge?: boolean): void;
}
/**
 * Given an APIClient instance, this class provides an AbiProvider interface for retrieving and caching ABIs.
 */
declare class ABICache implements ABICacheInterface {
    readonly client: APIClient;
    readonly cache: Map<string, ABI>;
    readonly pending: Map<string, Promise<API.v1.GetRawAbiResponse>>;
    constructor(client: APIClient);
    getAbi(account: NameType): Promise<ABI>;
    setAbi(account: NameType, abiDef: ABIDef, merge?: boolean): void;
}

export { ABICache, ABICacheInterface, ABIC<PERSON> as default };
