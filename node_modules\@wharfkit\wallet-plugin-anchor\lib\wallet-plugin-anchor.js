/**
 * @wharfkit/wallet-plugin-anchor v1.5.0
 * https://github.com/wharfkit/wallet-plugin-anchor
 *
 * @license
 * Copyright (c) 2023 Greymass Inc. All Rights Reserved.
 * 
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 
 * 1.  Redistribution of source code must retain the above copyright notice, this
 *     list of conditions and the following disclaimer.
 * 
 * 2.  Redistribution in binary form must reproduce the above copyright notice,
 *     this list of conditions and the following disclaimer in the documentation
 *     and/or other materials provided with the distribution.
 * 
 * 3.  Neither the name of the copyright holder nor the names of its contributors
 *     may be used to endorse or promote products derived from this software without
 *     specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
 * OF THE POSSIBILITY OF SUCH DAMAGE.
 * 
 * YOU ACKNOWLEDGE THAT THIS SOFTWARE IS NOT DESIGNED, LICENSED OR INTENDED FOR USE
 * IN THE DESIGN, CONSTRUCTION, OPERATION OR MAINTENANCE OF ANY MILITARY FACILITY.
 */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var buoy = require('@greymass/buoy');
var session = require('@wharfkit/session');
var protocolEsr = require('@wharfkit/protocol-esr');

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}

var login$1 = {
	title: "Connect with Anchor",
	body: "Scan with Anchor on your mobile device or click the button below to open on this device.",
	link: "Launch Anchor"
};
var transact$1 = {
	title: "Complete using Anchor",
	body: "Please open your Anchor Wallet on \"{{channelName}}\" to review and approve this transaction.",
	label: "Sign manually or with another device",
	link: "Trigger Manually",
	"await": "Waiting for response from Anchor"
};
var error$1 = {
	expired: "The request expired, please try again.",
	invalid_response: "Invalid response from Anchor, must contain link_ch, link_key, link_name and cid flags.",
	not_completed: "The request was not completed.",
	cancelled: "The request was cancelled from Anchor."
};
var en = {
	login: login$1,
	transact: transact$1,
	error: error$1
};

var ko = {
	
};

var login = {
	link: "启动Anchor",
	body: "在您的设备上使用Anchor扫描或者点击下方按钮打开。",
	title: "连接Anchor"
};
var error = {
	cancelled: "请求已从Anchor取消。",
	not_completed: "此请求未完成。",
	invalid_response: "无效的Anchor响应，必须包含link_ch, link_key, link_name和cid标识符。",
	expired: "请求已过期，请重试。"
};
var transact = {
	"await": "等待Anchor响应",
	link: "手动触发",
	label: "手动或使用其他设备签约",
	body: "请在\"{{channelName}}\"上打开您的Anchor钱包以浏览并批准此交易。",
	title: "完成使用Anchor"
};
var zh_hans = {
	login: login,
	error: error,
	transact: transact
};

var zh_hant = {
	
};

var defaultTranslations = {
    en,
    ko,
    'zh-Hans': zh_hans,
    'zh-Hant': zh_hant,
};

class WalletPluginAnchor extends session.AbstractWalletPlugin {
    constructor(options) {
        super();
        /**
         * The unique identifier for the wallet plugin.
         */
        this.id = 'anchor';
        /**
         * The translations for this plugin
         */
        this.translations = defaultTranslations;
        /**
         * The logic configuration for the wallet plugin.
         */
        this.config = {
            // Should the user interface display a chain selector?
            requiresChainSelect: false,
            // Should the user interface display a permission selector?
            requiresPermissionSelect: false,
        };
        /**
         * The metadata for the wallet plugin to be displayed in the user interface.
         */
        this.metadata = session.WalletPluginMetadata.from({
            name: 'Anchor',
            description: '',
            logo: session.Logo.from({
                dark: 'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyB3aWR0aD0iMTYwIiBoZWlnaHQ9IjE2MCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGcgdHJhbnNmb3JtPSJtYXRyaXgoMS40NCwgMCwgMCwgMS40NCwgLTguNTAxOTI1LCAtNTcuMDc0NTcpIiBzdHlsZT0iIj4KICAgIDx0aXRsZT5XaGl0ZTwvdGl0bGU+CiAgICA8Y2lyY2xlIGN4PSI5NC43OTMiIGN5PSIxMjguNTI0IiByPSI4MCIgZmlsbD0iI0ZCRkRGRiIvPgogICAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0gOTQuNzk5IDc4LjUyNCBDIDk3LjA5OCA3OC41MjQgOTkuMTk1IDc5LjgzNyAxMDAuMTk4IDgxLjkwNiBMIDEyNC4yMDQgMTMxLjQwNiBMIDEyNC43NDYgMTMyLjUyNCBMIDExMS40MDkgMTMyLjUyNCBMIDEwNy41MyAxMjQuNTI0IEwgODIuMDY5IDEyNC41MjQgTCA3OC4xODkgMTMyLjUyNCBMIDY0Ljg1MyAxMzIuNTI0IEwgNjUuMzk1IDEzMS40MDYgTCA4OS40MDEgODEuOTA2IEMgOTAuNDA0IDc5LjgzNyA5Mi41MDEgNzguNTI0IDk0Ljc5OSA3OC41MjQgWiBNIDg2LjkxOSAxMTQuNTI0IEwgMTAyLjY4IDExNC41MjQgTCA5NC43OTkgOTguMjc0IEwgODYuOTE5IDExNC41MjQgWiBNIDExMi43OTMgMTQ5LjUyNCBMIDEyNC43OTggMTQ5LjUyNCBDIDEyNC40MzcgMTY1LjY3NiAxMTEuMDY3IDE3OC41MjQgOTQuNzk5IDE3OC41MjQgQyA3OC41MzIgMTc4LjUyNCA2NS4xNjIgMTY1LjY3NiA2NC44MDEgMTQ5LjUyNCBMIDc2LjgwNiAxNDkuNTI0IEMgNzcuMDg3IDE1Ni44NzggODEuOTc0IDE2My4xNTUgODguNzkzIDE2NS41MiBMIDg4Ljc5MyAxNDEuNTI0IEMgODguNzkzIDEzOC4yMSA5MS40OCAxMzUuNTI0IDk0Ljc5MyAxMzUuNTI0IEMgOTguMTA3IDEzNS41MjQgMTAwLjc5MyAxMzguMjEgMTAwLjc5MyAxNDEuNTI0IEwgMTAwLjc5MyAxNjUuNTI0IEMgMTA3LjYyIDE2My4xNjIgMTEyLjUxMSAxNTYuODgzIDExMi43OTMgMTQ5LjUyNCBaIiBmaWxsPSIjMzY1MEEyIi8+CiAgPC9nPgo8L3N2Zz4=',
                light: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYwIiBoZWlnaHQ9IjE2MCIgdmlld0JveD0iMCAwIDE2MCAxNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjgwIiBjeT0iODAiIHI9IjgwIiBmaWxsPSIjMzY1MEEyIi8+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNODAuMDA2MyAzMEM4Mi4zMDUxIDMwIDg0LjQwMTkgMzEuMzEzNCA4NS40MDUgMzMuMzgxOEwxMDkuNDExIDgyLjg4MjJMMTA5Ljk1MyA4NEg5Ni42MTYzTDkyLjczNjYgNzZINjcuMjc1OUw2My4zOTYxIDg0SDUwLjA1OTRMNTAuNjAxNyA4Mi44ODE4TDc0LjYwNzcgMzMuMzgxOEM3NS42MTA4IDMxLjMxMzQgNzcuNzA3NSAzMCA4MC4wMDYzIDMwWk03Mi4xMjU2IDY2SDg3Ljg4N0w4MC4wMDYzIDQ5Ljc1MDFMNzIuMTI1NiA2NlpNOTcuOTk5NSAxMDFIMTEwLjAwNUMxMDkuNjQ0IDExNy4xNTIgOTYuMjczOCAxMzAgODAuMDA2MyAxMzBDNjMuNzM4OCAxMzAgNTAuMzY4NiAxMTcuMTUyIDUwLjAwNzggMTAxSDYyLjAxMzFDNjIuMjk0MSAxMDguMzU0IDY3LjE4MDQgMTE0LjYzMSA3NC4wMDAzIDExNi45OTZWOTNDNzQuMDAwMyA4OS42ODYzIDc2LjY4NjYgODcgODAuMDAwMyA4N0M4My4zMTQgODcgODYuMDAwMyA4OS42ODYzIDg2LjAwMDMgOTNWMTE3QzkyLjgyNjUgMTE0LjYzOCA5Ny43MTgzIDEwOC4zNTkgOTcuOTk5NSAxMDFaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K',
            }),
            homepage: 'https://greymass.com/anchor',
            download: 'https://greymass.com/anchor/download',
        });
        this.buoyUrl = (options === null || options === void 0 ? void 0 : options.buoyUrl) || 'https://cb.anchor.link';
        this.buoyWs = options === null || options === void 0 ? void 0 : options.buoyWs;
    }
    /**
     * Performs the wallet logic required to login and return the chain and permission level to use.
     *
     * @param options WalletPluginLoginOptions
     * @returns Promise<WalletPluginLoginResponse>
     */
    login(context) {
        return new Promise((resolve, reject) => {
            this.handleLogin(context)
                .then((response) => {
                resolve(response);
            })
                .catch((error) => {
                reject(error);
            });
        });
    }
    handleLogin(context) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            if (!context.ui) {
                throw new Error('No UI available');
            }
            // Retrieve translation helper from the UI, passing the app ID
            const t = context.ui.getTranslate(this.id);
            // Create the identity request to be presented to the user
            const { callback, request, sameDeviceRequest, requestKey, privateKey } = yield protocolEsr.createIdentityRequest(context, this.buoyUrl);
            // Elements for the login prompt
            const elements = [
                {
                    type: 'link',
                    label: t('login.link', { default: 'Launch Anchor' }),
                    data: {
                        href: sameDeviceRequest.encode(true, false, 'esr:'),
                        label: t('login.link', { default: 'Launch Anchor' }),
                        variant: 'primary',
                    },
                },
            ];
            // If we know this is NOT a mobile device, show the QR code
            if (!protocolEsr.isKnownMobile()) {
                elements.unshift({
                    type: 'qr',
                    data: request.encode(true, false, 'esr:'),
                });
            }
            // Automatically try to open the link
            window.location.href = sameDeviceRequest.encode(true, false, 'esr:');
            // Tell Wharf we need to prompt the user with a QR code and a button
            const promptResponse = (_a = context.ui) === null || _a === void 0 ? void 0 : _a.prompt({
                title: t('login.title', { default: 'Connect with Anchor' }),
                body: t('login.body', {
                    default: 'Scan with Anchor on your mobile device or click the button below to open on this device.',
                }),
                elements,
            });
            promptResponse.catch(() => {
                // eslint-disable-next-line no-console
                console.info('Modal closed');
            });
            // Await a promise race to wait for either the wallet response or the cancel
            const callbackResponse = yield protocolEsr.waitForCallback(callback, this.buoyWs, t);
            protocolEsr.verifyLoginCallbackResponse(callbackResponse, context);
            if (!callbackResponse.cid || !callbackResponse.sa || !callbackResponse.sp) {
                throw new Error('Invalid callback response');
            }
            if (callbackResponse.link_ch && callbackResponse.link_key && callbackResponse.link_name) {
                this.data.requestKey = requestKey;
                this.data.privateKey = privateKey;
                this.data.signerKey =
                    callbackResponse.link_key && session.PublicKey.from(callbackResponse.link_key);
                this.data.channelUrl = callbackResponse.link_ch;
                this.data.channelName = callbackResponse.link_name;
                try {
                    if (callbackResponse.link_meta) {
                        const metadata = JSON.parse(callbackResponse.link_meta);
                        this.data.sameDevice = metadata.sameDevice;
                        this.data.launchUrl = metadata.launchUrl;
                        this.data.triggerUrl = metadata.triggerUrl;
                    }
                }
                catch (e) {
                    // console.log('Error processing link_meta', e)
                }
            }
            const resolvedResponse = yield session.ResolvedSigningRequest.fromPayload(callbackResponse, context.esrOptions);
            const identityProof = resolvedResponse.getIdentityProof(callbackResponse.sig);
            return {
                chain: session.Checksum256.from(callbackResponse.cid),
                permissionLevel: session.PermissionLevel.from({
                    actor: callbackResponse.sa,
                    permission: callbackResponse.sp,
                }),
                identityProof,
            };
        });
    }
    /**
     * Performs the wallet logic required to sign a transaction and return the signature.
     *
     * @param chain ChainDefinition
     * @param resolved ResolvedSigningRequest
     * @returns Promise<Signature>
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    sign(resolved, context) {
        return this.handleSigningRequest(resolved, context);
    }
    handleSigningRequest(resolved, context) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!context.ui) {
                throw new Error('No UI available');
            }
            // Retrieve translation helper from the UI, passing the app ID
            const t = context.ui.getTranslate(this.id);
            // Set expiration time frames for the request
            const expiration = resolved.transaction.expiration.toDate();
            const now = new Date();
            const expiresIn = Math.floor(expiration.getTime() - now.getTime());
            // Create a new signing request based on the existing resolved request
            const modifiedRequest = yield context.createRequest({ transaction: resolved.transaction });
            // Set the expiration on the request LinkInfo
            modifiedRequest.setInfoKey('link', protocolEsr.LinkInfo.from({
                expiration,
            }));
            // Add the callback to the request
            const callback = protocolEsr.setTransactionCallback(modifiedRequest, this.buoyUrl);
            const request = modifiedRequest.encode(true, false);
            // Mobile will return true or false, desktop will return undefined
            const isSameDevice = this.data.sameDevice !== false;
            // Same device request
            const sameDeviceRequest = modifiedRequest.clone();
            const returnUrl = protocolEsr.generateReturnUrl();
            sameDeviceRequest.setInfoKey('same_device', true);
            if (returnUrl) {
                sameDeviceRequest.setInfoKey('return_path', returnUrl);
            }
            if (this.data.sameDevice) {
                if (this.data.launchUrl) {
                    window.location.href = this.data.launchUrl;
                }
                else if (protocolEsr.isAppleHandheld()) {
                    window.location.href = 'anchor://link';
                }
            }
            const signManually = () => {
                var _a;
                (_a = context.ui) === null || _a === void 0 ? void 0 : _a.prompt({
                    title: t('transact.sign_manually.title', { default: 'Sign manually' }),
                    body: t('transact.sign_manually.body', {
                        default: 'Scan the QR-code with Anchor on another device or use the button to open it here.',
                    }),
                    elements: [
                        {
                            type: 'qr',
                            data: String(request),
                        },
                        {
                            type: 'link',
                            label: t('transact.sign_manually.link.title', { default: 'Open Anchor' }),
                            data: {
                                href: String(sameDeviceRequest),
                                label: t('transact.sign_manually.link.title', { default: 'Open Anchor' }),
                            },
                        },
                    ],
                });
            };
            // Tell Wharf we need to prompt the user with a QR code and a button
            const promptPromise = context.ui.prompt({
                title: t('transact.title', { default: 'Complete using Anchor' }),
                body: t('transact.body', {
                    channelName: this.data.channelName,
                    default: `Please open your Anchor Wallet on "${this.data.channelName}" to review and approve this transaction.`,
                }),
                elements: [
                    {
                        type: 'countdown',
                        data: {
                            label: t('transact.await', { default: 'Waiting for response from Anchor' }),
                            end: expiration.toISOString(),
                        },
                    },
                    {
                        type: 'button',
                        label: t('transact.label', { default: 'Sign manually or with another device' }),
                        data: {
                            onClick: isSameDevice
                                ? () => (window.location.href = sameDeviceRequest.encode())
                                : signManually,
                            label: t('transact.label', {
                                default: 'Sign manually or with another device',
                            }),
                        },
                    },
                ],
            });
            // Create a timer to test the external cancelation of the prompt, if defined
            const timer = setTimeout(() => {
                if (!context.ui) {
                    throw new Error('No UI available');
                }
                promptPromise.cancel(t('error.expired', { default: 'The request expired, please try again.' }));
            }, expiresIn);
            // Clear the timeout if the UI throws (which generally means it closed)
            promptPromise.catch(() => clearTimeout(timer));
            // Wait for the callback from the wallet
            const callbackPromise = protocolEsr.waitForCallback(callback, this.buoyWs, t);
            // Assemble and send the payload to the wallet
            if (this.data.channelUrl) {
                const service = new URL(this.data.channelUrl).origin;
                const channel = new URL(this.data.channelUrl).pathname.substring(1);
                const sealedMessage = protocolEsr.sealMessage((this.data.sameDevice ? sameDeviceRequest : modifiedRequest).encode(true, false, 'esr:'), session.PrivateKey.from(this.data.privateKey), session.PublicKey.from(this.data.signerKey));
                buoy.send(session.Serializer.encode({ object: sealedMessage }).array, {
                    service,
                    channel,
                });
            }
            else {
                // If no channel is defined, fallback to the same device request and trigger immediately
                window.location.href = sameDeviceRequest.encode();
            }
            // Wait for either the callback or the prompt to resolve
            const callbackResponse = yield Promise.race([callbackPromise, promptPromise]).finally(() => {
                // Clear the automatic timeout once the race resolves
                clearTimeout(timer);
            });
            const wasSuccessful = protocolEsr.isCallback(callbackResponse) &&
                protocolEsr.extractSignaturesFromCallback(callbackResponse).length > 0;
            if (wasSuccessful) {
                // If the callback was resolved, create a new request from the response
                const resolvedRequest = yield session.ResolvedSigningRequest.fromPayload(callbackResponse, context.esrOptions);
                // Return the new request and the signatures from the wallet
                return {
                    signatures: protocolEsr.extractSignaturesFromCallback(callbackResponse),
                    resolved: resolvedRequest,
                };
            }
            const errorString = t('error.not_completed', { default: 'The request was not completed.' });
            promptPromise.cancel(errorString);
            // This shouldn't ever trigger, but just in case
            throw new Error(errorString);
        });
    }
}

exports.WalletPluginAnchor = WalletPluginAnchor;
//# sourceMappingURL=wallet-plugin-anchor.js.map
