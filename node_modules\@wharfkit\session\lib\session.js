'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib = require('tslib');
var common = require('@wharfkit/common');
var antelope = require('@wharfkit/antelope');
var zlib = require('pako');
var signingRequest = require('@wharfkit/signing-request');
var abicache = require('@wharfkit/abicache');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var zlib__default = /*#__PURE__*/_interopDefaultLegacy(zlib);

exports.LoginHookTypes = void 0;
(function (LoginHookTypes) {
    LoginHookTypes["beforeLogin"] = "beforeLogin";
    LoginHookTypes["afterLogin"] = "afterLogin";
})(exports.LoginHookTypes || (exports.LoginHookTypes = {}));
class LoginContext {
    constructor(options) {
        var _a;
        this.arbitrary = {};
        this.chains = [];
        this.hooks = {
            afterLogin: [],
            beforeLogin: [],
        };
        this.uiRequirements = {
            requiresChainSelect: true,
            requiresPermissionSelect: true,
            requiresPermissionEntry: false,
            requiresWalletSelect: true,
        };
        this.walletPlugins = [];
        this.appName = String(options.appName);
        if (options.arbitrary) {
            this.arbitrary = options.arbitrary;
        }
        if (options.chains) {
            this.chains = options.chains;
        }
        if (options.chain) {
            this.chain = options.chain;
        }
        this.fetch = options.fetch;
        this.permissionLevel = options.permissionLevel;
        this.walletPlugins = options.walletPlugins || [];
        this.ui = options.ui;
        (_a = options.loginPlugins) === null || _a === void 0 ? void 0 : _a.forEach((plugin) => {
            plugin.register(this);
        });
    }
    addHook(t, hook) {
        this.hooks[t].push(hook);
    }
    getClient(chain) {
        return new antelope.APIClient({ provider: new antelope.FetchProvider(chain.url, { fetch: this.fetch }) });
    }
    get esrOptions() {
        return {
            zlib: zlib__default["default"],
        };
    }
}
class AbstractLoginPlugin {
}
class BaseLoginPlugin extends AbstractLoginPlugin {
    register() {
    }
}

exports.TransactHookTypes = void 0;
(function (TransactHookTypes) {
    TransactHookTypes["beforeSign"] = "beforeSign";
    TransactHookTypes["afterSign"] = "afterSign";
    TransactHookTypes["afterBroadcast"] = "afterBroadcast";
})(exports.TransactHookTypes || (exports.TransactHookTypes = {}));
class TransactContext {
    constructor(options) {
        var _a;
        this.hooks = {
            afterBroadcast: [],
            afterSign: [],
            beforeSign: [],
        };
        this.abiCache = options.abiCache;
        this.appName = String(options.appName);
        this.chain = options.chain;
        this.client = options.client;
        this.createRequest = options.createRequest;
        this.fetch = options.fetch;
        this.permissionLevel = options.permissionLevel;
        if (options.storage) {
            this.storage = options.storage;
        }
        this.transactPluginsOptions = options.transactPluginsOptions || {};
        this.ui = options.ui;
        (_a = options.transactPlugins) === null || _a === void 0 ? void 0 : _a.forEach((plugin) => {
            plugin.register(this);
        });
    }
    get accountName() {
        return this.permissionLevel.actor;
    }
    get permissionName() {
        return this.permissionLevel.permission;
    }
    get esrOptions() {
        return {
            abiProvider: this.abiCache,
            zlib: zlib__default["default"],
        };
    }
    addHook(t, hook) {
        switch (t) {
            case exports.TransactHookTypes.beforeSign: {
                this.hooks[t].push(hook);
                break;
            }
            case exports.TransactHookTypes.afterSign:
            case exports.TransactHookTypes.afterBroadcast: {
                this.hooks[t].push(hook);
                break;
            }
        }
    }
    getInfo() {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            let info = this.info;
            if (this.info) {
                info = this.info;
            }
            else {
                this.info = info = yield this.client.v1.chain.get_info();
            }
            return info;
        });
    }
    resolve(request, expireSeconds = 120) {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            let resolveArgs = {
                chainId: this.chain.id,
            };
            if (request.requiresTapos()) {
                const info = yield this.getInfo();
                const header = info.getTransactionHeader(expireSeconds);
                resolveArgs = Object.assign(Object.assign({}, resolveArgs), header);
            }
            const abis = yield request.fetchAbis(this.abiCache);
            return request.resolve(abis, this.permissionLevel, resolveArgs);
        });
    }
}
class TransactRevisions {
    constructor(request) {
        this.revisions = [];
        this.addRevision({ request, signatures: [] }, 'original', true);
    }
    addRevision(response, code, allowModify) {
        let modified = false;
        const previous = this.revisions[this.revisions.length - 1];
        if (previous) {
            modified = previous.response.request !== String(response.request);
        }
        this.revisions.push({
            allowModify,
            code: String(code),
            modified,
            response: {
                request: String(response.request),
                signatures: response.signatures ? antelope.Serializer.objectify(response.signatures) : [],
            },
        });
    }
}
class AbstractTransactPlugin {
}
class BaseTransactPlugin extends AbstractTransactPlugin {
    get id() {
        return 'base-transact-plugin';
    }
    register() {
    }
}

function getFetch(options) {
    if (options && options.fetch) {
        return options.fetch;
    }
    if (typeof window !== 'undefined' && window.fetch) {
        return window.fetch.bind(window);
    }
    if (typeof global !== 'undefined' && global.fetch) {
        return global.fetch.bind(global);
    }
    throw new Error('Missing fetch');
}
function appendAction(request, action) {
    const newAction = antelope.Action.from(action);
    const cloned = request.clone();
    switch (cloned.data.req.variantName) {
        case 'action': {
            cloned.data.req.value = [cloned.data.req.value, newAction];
            cloned.data.req.variantIdx = 1;
            break;
        }
        case 'action[]': {
            const array = cloned.data.req.value;
            array.push(newAction);
            cloned.data.req.value = array;
            break;
        }
        case 'transaction': {
            const tx = cloned.data.req.value;
            tx.actions.push(newAction);
            cloned.data.req.value = tx;
            break;
        }
        default: {
            throw new Error('unknown data req type');
        }
    }
    return cloned;
}
function prependAction(request, action) {
    const newAction = antelope.Action.from(action);
    const cloned = request.clone();
    switch (cloned.data.req.variantName) {
        case 'action': {
            cloned.data.req.value = [newAction, cloned.data.req.value];
            cloned.data.req.variantIdx = 1;
            break;
        }
        case 'action[]': {
            const array = cloned.data.req.value;
            array.unshift(newAction);
            cloned.data.req.value = array;
            break;
        }
        case 'transaction': {
            const tx = cloned.data.req.value;
            tx.actions.unshift(newAction);
            cloned.data.req.value = tx;
            break;
        }
        default: {
            throw new Error('unknown data req type');
        }
    }
    return cloned;
}

class Session {
    get data() {
        return this._data;
    }
    set data(data) {
        this._data = data;
    }
    constructor(args, options = {}) {
        this.abis = [];
        this.allowModify = true;
        this.broadcast = true;
        this.expireSeconds = 120;
        this.transactPluginsOptions = {};
        this._data = {};
        this.serialize = () => {
            const serializableData = {
                chain: this.chain.id,
                actor: this.permissionLevel.actor,
                permission: this.permissionLevel.permission,
                walletPlugin: {
                    id: this.walletPlugin.id,
                    data: this.walletPlugin.data,
                },
            };
            if (Object.keys(this._data).length > 0) {
                serializableData.data = this.data;
            }
            return antelope.Serializer.objectify(serializableData);
        };
        this.chain = common.ChainDefinition.from(args.chain);
        if (args.permissionLevel) {
            this.permissionLevel = antelope.PermissionLevel.from(args.permissionLevel);
        }
        else if (args.actor && args.permission) {
            this.permissionLevel = antelope.PermissionLevel.from(`${args.actor}@${args.permission}`);
        }
        else {
            throw new Error('Either a permissionLevel or actor/permission must be provided when creating a new Session.');
        }
        this.walletPlugin = args.walletPlugin;
        if (options.appName) {
            this.appName = String(options.appName);
        }
        if (options.abis) {
            this.abis = [...options.abis];
        }
        if (options.contracts) {
            this.abis.push(...options.contracts.map((c) => ({ account: c.account, abi: c.abi })));
        }
        if (options.allowModify !== undefined) {
            this.allowModify = options.allowModify;
        }
        if (options.broadcast !== undefined) {
            this.broadcast = options.broadcast;
        }
        if (options.expireSeconds) {
            this.expireSeconds = options.expireSeconds;
        }
        if (options.fetch) {
            this.fetch = options.fetch;
        }
        else {
            this.fetch = getFetch(options);
        }
        if (options.storage) {
            this.storage = options.storage;
        }
        if (options.transactPlugins) {
            this.transactPlugins = options.transactPlugins;
        }
        else {
            this.transactPlugins = [new BaseTransactPlugin()];
        }
        if (options.transactPluginsOptions) {
            this.transactPluginsOptions = options.transactPluginsOptions;
        }
        if (options.abiCache) {
            this.abiCache = options.abiCache;
        }
        else {
            this.abiCache = new abicache.ABICache(this.client);
        }
        if (options.ui) {
            this.ui = options.ui;
        }
    }
    get actor() {
        return this.permissionLevel.actor;
    }
    get permission() {
        return this.permissionLevel.permission;
    }
    get client() {
        return new antelope.APIClient({ provider: new antelope.FetchProvider(this.chain.url, { fetch: this.fetch }) });
    }
    setEndpoint(url) {
        this.chain.url = url;
    }
    upgradeTransaction(args) {
        const anyArgs = args;
        if (args.actions &&
            (anyArgs.expiration ||
                anyArgs.ref_block_num ||
                anyArgs.ref_block_prefix ||
                anyArgs.max_net_usage_words ||
                anyArgs.max_cpu_usage_ms ||
                anyArgs.delay_sec)) {
            return (args = {
                transaction: Object.assign({ expiration: '1970-01-01T00:00:00', ref_block_num: 0, ref_block_prefix: 0, max_net_usage_words: 0, max_cpu_usage_ms: 0, delay_sec: 0 }, anyArgs),
            });
        }
        if (args.context_free_actions || args.context_free_data) {
            const actions = args.actions || [args.action];
            delete args.action;
            return {
                transaction: Object.assign({ expiration: '1970-01-01T00:00:00', ref_block_num: 0, ref_block_prefix: 0, max_net_usage_words: 0, max_cpu_usage_ms: 0, delay_sec: 0, context_free_actions: [], context_free_data: [], actions }, anyArgs),
            };
        }
        return args;
    }
    storageType(version) {
        return version === 2 ? signingRequest.RequestDataV2 : signingRequest.RequestDataV3;
    }
    cloneRequest(request, abiCache) {
        let signature;
        if (request.signature) {
            signature = signingRequest.RequestSignature.from(JSON.parse(JSON.stringify(request.signature)));
        }
        const RequestData = this.storageType(request.version);
        const data = RequestData.from(JSON.parse(JSON.stringify(request.data)));
        return new signingRequest.SigningRequest(request.version, data, zlib__default["default"], abiCache, signature);
    }
    createRequest(args, abiCache) {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            let request;
            const options = {
                abiProvider: abiCache,
                zlib: zlib__default["default"],
            };
            if (args.request && args.request instanceof signingRequest.SigningRequest) {
                request = this.cloneRequest(args.request, abiCache);
            }
            else if (args.request) {
                request = signingRequest.SigningRequest.from(args.request, options);
            }
            else {
                args = this.upgradeTransaction(args);
                request = yield signingRequest.SigningRequest.create(Object.assign(Object.assign({}, args), { chainId: this.chain.id }), options);
            }
            request.setBroadcast(false);
            return request;
        });
    }
    updateRequest(previous, modified, abiCache) {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            const updatedRequest = this.cloneRequest(modified, abiCache);
            const info = updatedRequest.getRawInfo();
            previous.data.info.forEach((metadata) => {
                if (info[metadata.key]) {
                    console.warn(`During an updateRequest call, the previous request had already set the ` +
                        `metadata key of "${metadata.key}" which will not be overwritten.`);
                }
                updatedRequest.setRawInfoKey(metadata.key, metadata.value);
            });
            return updatedRequest;
        });
    }
    transact(args, options) {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            try {
                const expireSeconds = options && options.expireSeconds ? options.expireSeconds : this.expireSeconds;
                const willBroadcast = options && typeof options.broadcast !== 'undefined'
                    ? options.broadcast
                    : this.broadcast;
                const abiCache = this.getMergedAbiCache(args, options);
                const transactPlugins = (options === null || options === void 0 ? void 0 : options.transactPlugins) || this.transactPlugins;
                const transactPluginsOptions = (options === null || options === void 0 ? void 0 : options.transactPluginsOptions) || this.transactPluginsOptions;
                let allowModify = options && typeof options.allowModify !== 'undefined'
                    ? options.allowModify
                    : this.allowModify;
                const context = new TransactContext({
                    abiCache,
                    appName: this.appName,
                    chain: this.chain,
                    client: this.client,
                    createRequest: (a) => this.createRequest(a, abiCache),
                    fetch: this.fetch,
                    permissionLevel: this.permissionLevel,
                    storage: this.storage,
                    transactPlugins,
                    transactPluginsOptions,
                    ui: this.ui,
                });
                if (context.ui) {
                    yield context.ui.onTransact();
                    for (const translation of transactPlugins.map((transactPlugin) => this.getPluginTranslations(transactPlugin))) {
                        context.ui.addTranslations(translation);
                    }
                }
                let request = yield this.createRequest(args, abiCache);
                const result = {
                    chain: this.chain,
                    request,
                    resolved: undefined,
                    returns: [],
                    revisions: new TransactRevisions(request),
                    signatures: [],
                    signer: this.permissionLevel,
                    transaction: undefined,
                };
                for (const hook of context.hooks.beforeSign) {
                    const response = yield hook(request.clone(), context);
                    if (response) {
                        result.revisions.addRevision(response, String(hook), allowModify);
                        if (allowModify) {
                            request = yield this.updateRequest(request, response.request, abiCache);
                        }
                        if (response.signatures) {
                            result.signatures = [...result.signatures, ...response.signatures];
                            allowModify = false;
                        }
                    }
                }
                result.request = request;
                result.resolved = yield context.resolve(request, expireSeconds);
                result.transaction = result.resolved.resolvedTransaction;
                if (context.ui) {
                    yield context.ui.onSign();
                    context.ui.addTranslations(this.getPluginTranslations(this.walletPlugin));
                }
                const walletResponse = yield this.walletPlugin.sign(result.resolved, context);
                result.signatures.push(...walletResponse.signatures);
                if (walletResponse.resolved) {
                    const { resolved } = walletResponse;
                    const requestWasModified = !result.resolved.transaction.equals(resolved.transaction);
                    if (requestWasModified) {
                        if (allowModify) {
                            result.request = resolved.request;
                            result.resolved = resolved;
                            result.transaction = resolved.resolvedTransaction;
                        }
                        else {
                            throw new Error(`The ${this.walletPlugin.metadata.name} plugin modified the transaction when it was not allowed to.`);
                        }
                    }
                }
                for (const hook of context.hooks.afterSign)
                    yield hook(result, context);
                if (context.ui) {
                    yield context.ui.onSignComplete();
                }
                if (willBroadcast) {
                    if (context.ui) {
                        yield context.ui.onBroadcast();
                    }
                    const signed = antelope.SignedTransaction.from(Object.assign(Object.assign({}, result.resolved.transaction), { signatures: result.signatures }));
                    result.response = yield context.client.v1.chain.send_transaction(signed);
                    if (result.response.processed && result.response.processed.action_traces) {
                        result.returns = yield processReturnValues(result.response, abiCache);
                    }
                    for (const hook of context.hooks.afterBroadcast)
                        yield hook(result, context);
                    if (context.ui) {
                        yield context.ui.onBroadcastComplete();
                    }
                }
                if (context.ui) {
                    yield context.ui.onTransactComplete();
                }
                return result;
            }
            catch (error) {
                if (error.response && error.response.json) {
                    const { json } = error.response;
                    if (json.error && json.error.details) {
                        const e = new Error(json.error.details[0].message);
                        if (this.ui) {
                            yield this.ui.onError(e);
                        }
                        throw e;
                    }
                }
                else {
                    if (this.ui) {
                        yield this.ui.onError(error);
                    }
                }
                throw new Error(error);
            }
        });
    }
    signTransaction(transaction) {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            const context = new TransactContext({
                abiCache: this.abiCache,
                chain: this.chain,
                client: this.client,
                createRequest: (args) => this.createRequest(args, this.abiCache),
                fetch: this.fetch,
                permissionLevel: this.permissionLevel,
            });
            const request = yield signingRequest.SigningRequest.create({
                transaction,
                chainId: this.chain.id,
            }, context.esrOptions);
            request.setBroadcast(false);
            const resolvedRequest = new signingRequest.ResolvedSigningRequest(request, this.permissionLevel, antelope.Transaction.from(transaction), antelope.Serializer.objectify(antelope.Transaction.from(transaction)), signingRequest.ChainId.from(this.chain.id));
            const walletResponse = yield this.walletPlugin.sign(resolvedRequest, context);
            return walletResponse.signatures;
        });
    }
    getPluginTranslations(transactPlugin) {
        if (!transactPlugin.translations) {
            return {};
        }
        const prefixed = {};
        const languages = Object.keys(transactPlugin.translations);
        languages.forEach((lang) => {
            if (transactPlugin.translations) {
                prefixed[lang] = { [transactPlugin.id]: transactPlugin.translations[lang] };
            }
        });
        return prefixed;
    }
    getMergedAbiCache(args, options) {
        const abiCache = (options === null || options === void 0 ? void 0 : options.abiCache) || this.abiCache;
        if (!abiCache['setAbi']) {
            throw new Error('Custom `abiCache` does not support `setAbi` method.');
        }
        this.abis.forEach((def) => abiCache.setAbi(def.account, def.abi));
        if (options === null || options === void 0 ? void 0 : options.abis) {
            options.abis.forEach((def) => abiCache.setAbi(def.account, def.abi));
        }
        if (options === null || options === void 0 ? void 0 : options.contracts) {
            options.contracts.forEach((c) => abiCache.setAbi(c.account, c.abi));
        }
        if (args.action && args.action['abi']) {
            abiCache.setAbi(args.action.account, args.action['abi'], true);
        }
        if (args.actions) {
            args.actions.forEach((action) => {
                if (action['abi']) {
                    abiCache.setAbi(action.account, action['abi'], true);
                }
            });
        }
        if (args.transaction && args.transaction.actions) {
            args.transaction.actions.forEach((action) => {
                if (action['abi']) {
                    abiCache.setAbi(action.account, action['abi'], true);
                }
            });
        }
        return abiCache;
    }
}
function processReturnValues(response, abiCache) {
    return tslib.__awaiter(this, void 0, void 0, function* () {
        const decoded = [];
        for (const actionTrace of response.processed.action_traces) {
            if (actionTrace.return_value_hex_data) {
                const contract = antelope.Name.from(actionTrace.act.account);
                const action = antelope.Name.from(actionTrace.act.name);
                const abi = yield abiCache.getAbi(contract);
                const returnType = abi.action_results.find((a) => antelope.Name.from(a.name).equals(action));
                if (returnType) {
                    try {
                        const data = antelope.Serializer.decode({
                            data: actionTrace.return_value_hex_data,
                            type: returnType.result_type,
                            abi,
                        });
                        decoded.push({
                            contract,
                            action,
                            hex: actionTrace.return_value_hex_data,
                            data,
                            returnType,
                        });
                    }
                    catch (error) {
                        console.warn(`Error decoding return value for ${contract}::${action}:`, error);
                        decoded.push({
                            contract,
                            action,
                            hex: actionTrace.return_value_hex_data,
                            data: '',
                            returnType,
                        });
                    }
                }
                else {
                    console.warn(`No return type found for ${contract}::${action}`);
                    decoded.push({
                        contract,
                        action,
                        hex: actionTrace.return_value_hex_data,
                        data: '',
                        returnType: {
                            name: action,
                            result_type: '',
                        },
                    });
                }
            }
        }
        return decoded;
    });
}

class BrowserLocalStorage {
    constructor(keyPrefix = '') {
        this.keyPrefix = keyPrefix;
    }
    write(key, data) {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            localStorage.setItem(this.storageKey(key), data);
        });
    }
    read(key) {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            return localStorage.getItem(this.storageKey(key));
        });
    }
    remove(key) {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            localStorage.removeItem(this.storageKey(key));
        });
    }
    storageKey(key) {
        return `wharf-${this.keyPrefix}-${key}`;
    }
}

var WalletPluginMetadata_1;
exports.WalletPluginMetadata = WalletPluginMetadata_1 = class WalletPluginMetadata extends antelope.Struct {
    static from(data) {
        return new WalletPluginMetadata_1(Object.assign(Object.assign({}, data), { logo: data.logo ? common.Logo.from(data.logo) : undefined }));
    }
};
tslib.__decorate([
    antelope.Struct.field('string', { optional: true })
], exports.WalletPluginMetadata.prototype, "name", void 0);
tslib.__decorate([
    antelope.Struct.field('string', { optional: true })
], exports.WalletPluginMetadata.prototype, "description", void 0);
tslib.__decorate([
    antelope.Struct.field(common.Logo, { optional: true })
], exports.WalletPluginMetadata.prototype, "logo", void 0);
tslib.__decorate([
    antelope.Struct.field('string', { optional: true })
], exports.WalletPluginMetadata.prototype, "homepage", void 0);
tslib.__decorate([
    antelope.Struct.field('string', { optional: true })
], exports.WalletPluginMetadata.prototype, "download", void 0);
tslib.__decorate([
    antelope.Struct.field('string', { optional: true })
], exports.WalletPluginMetadata.prototype, "publicKey", void 0);
exports.WalletPluginMetadata = WalletPluginMetadata_1 = tslib.__decorate([
    antelope.Struct.type('wallet_plugin_metadata')
], exports.WalletPluginMetadata);
class AbstractWalletPlugin {
    constructor() {
        this._data = {};
        this.config = {
            requiresChainSelect: true,
            requiresPermissionSelect: false,
            requiresPermissionEntry: false,
        };
        this.metadata = new exports.WalletPluginMetadata({});
    }
    get data() {
        return this._data;
    }
    set data(data) {
        this._data = data;
    }
    serialize() {
        return {
            id: this.id,
            data: this.data,
        };
    }
}

var AccountCreationPluginMetadata_1;
exports.AccountCreationPluginMetadata = AccountCreationPluginMetadata_1 = class AccountCreationPluginMetadata extends antelope.Struct {
    static from(data) {
        return new AccountCreationPluginMetadata_1(Object.assign(Object.assign({}, data), { logo: data.logo ? common.Logo.from(data.logo) : undefined }));
    }
};
tslib.__decorate([
    antelope.Struct.field('string')
], exports.AccountCreationPluginMetadata.prototype, "name", void 0);
tslib.__decorate([
    antelope.Struct.field('string', { optional: true })
], exports.AccountCreationPluginMetadata.prototype, "description", void 0);
tslib.__decorate([
    antelope.Struct.field(common.Logo, { optional: true })
], exports.AccountCreationPluginMetadata.prototype, "logo", void 0);
tslib.__decorate([
    antelope.Struct.field('string', { optional: true })
], exports.AccountCreationPluginMetadata.prototype, "homepage", void 0);
exports.AccountCreationPluginMetadata = AccountCreationPluginMetadata_1 = tslib.__decorate([
    antelope.Struct.type('account_creation_plugin_metadata')
], exports.AccountCreationPluginMetadata);
class CreateAccountContext {
    constructor(options) {
        this.accountCreationPlugins = [];
        this.uiRequirements = {
            requiresChainSelect: true,
            requiresPluginSelect: true,
        };
        this.appName = String(options.appName);
        if (options.chains) {
            this.chains = options.chains;
        }
        if (options.chain) {
            this.chain = options.chain;
        }
        this.fetch = options.fetch;
        this.ui = options.ui;
        if (options.accountCreationPlugins) {
            this.accountCreationPlugins = options.accountCreationPlugins;
        }
        if (options.uiRequirements) {
            this.uiRequirements = options.uiRequirements;
        }
    }
    getClient(chain) {
        return new antelope.APIClient({ provider: new antelope.FetchProvider(chain.url, { fetch: this.fetch }) });
    }
}
class AbstractAccountCreationPlugin {
    constructor() {
        this.config = {
            requiresChainSelect: true,
        };
        this.metadata = new exports.AccountCreationPluginMetadata({});
    }
}

class SessionKit {
    constructor(args, options = {}) {
        this.abis = [];
        this.allowModify = true;
        this.expireSeconds = 120;
        this.transactPluginsOptions = {};
        this.accountCreationPlugins = [];
        this.appName = String(args.appName);
        this.chains = args.chains.map((chain) => common.ChainDefinition.from(chain));
        this.ui = args.ui;
        this.walletPlugins = args.walletPlugins;
        if (options.fetch) {
            this.fetch = options.fetch;
        }
        else {
            this.fetch = getFetch(options);
        }
        if (options.abis) {
            this.abis = [...options.abis];
        }
        if (options.contracts) {
            this.abis.push(...options.contracts.map((c) => ({ account: c.account, abi: c.abi })));
        }
        if (options.loginPlugins) {
            this.loginPlugins = options.loginPlugins;
        }
        else {
            this.loginPlugins = [new BaseLoginPlugin()];
        }
        if (options.storage) {
            this.storage = options.storage;
        }
        else {
            this.storage = new BrowserLocalStorage();
        }
        if (options.transactPlugins) {
            this.transactPlugins = options.transactPlugins;
        }
        else {
            this.transactPlugins = [new BaseTransactPlugin()];
        }
        if (typeof options.allowModify !== 'undefined') {
            this.allowModify = options.allowModify;
        }
        if (options.expireSeconds) {
            this.expireSeconds = options.expireSeconds;
        }
        if (options.transactPluginsOptions) {
            this.transactPluginsOptions = options.transactPluginsOptions;
        }
        if (options.accountCreationPlugins) {
            this.accountCreationPlugins = options.accountCreationPlugins;
        }
    }
    setEndpoint(id, url) {
        const modifiedChains = [...this.chains];
        const chainId = antelope.Checksum256.from(id);
        const chainIndex = this.chains.findIndex((c) => c.id.equals(chainId));
        if (chainIndex < 0) {
            throw new Error('Chain with specified ID not found.');
        }
        modifiedChains[chainIndex].url = url;
        this.chains = modifiedChains;
    }
    getChainDefinition(id, override) {
        const chains = override ? override : this.chains;
        const chainId = antelope.Checksum256.from(id);
        const chain = chains.find((c) => c.id.equals(chainId));
        if (!chain) {
            throw new Error(`No chain defined with an ID of: ${chainId}`);
        }
        return chain;
    }
    createAccount(options) {
        var _a;
        return tslib.__awaiter(this, void 0, void 0, function* () {
            try {
                if (this.accountCreationPlugins.length === 0) {
                    throw new Error('No account creation plugins available.');
                }
                let chain = options === null || options === void 0 ? void 0 : options.chain;
                let requiresChainSelect = !chain;
                let requiresPluginSelect = !(options === null || options === void 0 ? void 0 : options.pluginId);
                let accountCreationPlugin;
                if (options === null || options === void 0 ? void 0 : options.pluginId) {
                    requiresPluginSelect = false;
                    accountCreationPlugin = this.accountCreationPlugins.find((p) => p.id === options.pluginId);
                    if (!accountCreationPlugin) {
                        throw new Error('Invalid account creation plugin selected.');
                    }
                    if ((accountCreationPlugin === null || accountCreationPlugin === void 0 ? void 0 : accountCreationPlugin.config.requiresChainSelect) !== undefined) {
                        requiresChainSelect = accountCreationPlugin === null || accountCreationPlugin === void 0 ? void 0 : accountCreationPlugin.config.requiresChainSelect;
                    }
                    if (!accountCreationPlugin.config.requiresChainSelect &&
                        accountCreationPlugin.config.supportedChains &&
                        accountCreationPlugin.config.supportedChains.length === 1) {
                        chain = accountCreationPlugin.config.supportedChains[0];
                    }
                }
                let chains = this.chains;
                if (accountCreationPlugin && ((_a = accountCreationPlugin === null || accountCreationPlugin === void 0 ? void 0 : accountCreationPlugin.config.supportedChains) === null || _a === void 0 ? void 0 : _a.length)) {
                    chains = chains.filter((availableChain) => {
                        var _a;
                        return (_a = accountCreationPlugin === null || accountCreationPlugin === void 0 ? void 0 : accountCreationPlugin.config.supportedChains) === null || _a === void 0 ? void 0 : _a.find((c) => {
                            return c.id.equals(availableChain.id);
                        });
                    });
                }
                const context = new CreateAccountContext({
                    accountCreationPlugins: this.accountCreationPlugins,
                    appName: this.appName,
                    chain,
                    chains,
                    fetch: this.fetch,
                    ui: this.ui,
                    uiRequirements: {
                        requiresChainSelect,
                        requiresPluginSelect,
                    },
                });
                if (requiresPluginSelect || requiresChainSelect) {
                    const response = yield context.ui.onAccountCreate(context);
                    const pluginId = (options === null || options === void 0 ? void 0 : options.pluginId) || response.pluginId;
                    if (!pluginId) {
                        throw new Error('No account creation plugin selected.');
                    }
                    accountCreationPlugin = context.accountCreationPlugins.find((p) => p.id === pluginId);
                    if (!accountCreationPlugin) {
                        throw new Error('No account creation plugin selected.');
                    }
                    if (!accountCreationPlugin.config.requiresChainSelect &&
                        accountCreationPlugin.config.supportedChains &&
                        accountCreationPlugin.config.supportedChains.length === 1) {
                        context.chain = accountCreationPlugin.config.supportedChains[0];
                    }
                    if (response.chain) {
                        context.chain = this.getChainDefinition(response.chain, context.chains);
                    }
                    if (accountCreationPlugin.config.requiresChainSelect && !context.chain) {
                        throw new Error(`Account creation plugin (${pluginId}) requires chain selection, and no chain was selected.`);
                    }
                }
                if (!accountCreationPlugin) {
                    throw new Error('No account creation plugin selected');
                }
                const accountCreationData = yield accountCreationPlugin.create(context);
                yield context.ui.onAccountCreateComplete();
                return accountCreationData;
            }
            catch (error) {
                yield this.ui.onError(error);
                throw new Error(error);
            }
        });
    }
    login(options) {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            try {
                const context = new LoginContext({
                    appName: this.appName,
                    arbitrary: (options === null || options === void 0 ? void 0 : options.arbitrary) || {},
                    chain: undefined,
                    chains: options && (options === null || options === void 0 ? void 0 : options.chains)
                        ? options.chains.map((c) => this.getChainDefinition(c))
                        : this.chains,
                    fetch: this.fetch,
                    loginPlugins: this.loginPlugins,
                    ui: this.ui,
                    walletPlugins: this.walletPlugins.map((plugin) => {
                        var _a;
                        return {
                            config: plugin.config,
                            metadata: exports.WalletPluginMetadata.from(plugin.metadata),
                            retrievePublicKey: (_a = plugin.retrievePublicKey) === null || _a === void 0 ? void 0 : _a.bind(plugin),
                        };
                    }),
                });
                yield context.ui.onLogin();
                let walletPlugin = undefined;
                if (this.walletPlugins.length === 1) {
                    walletPlugin = this.walletPlugins[0];
                    context.walletPluginIndex = 0;
                    context.uiRequirements.requiresWalletSelect = false;
                }
                else if (options === null || options === void 0 ? void 0 : options.walletPlugin) {
                    const index = this.walletPlugins.findIndex((p) => p.id === options.walletPlugin);
                    if (index >= 0) {
                        walletPlugin = this.walletPlugins[index];
                        context.walletPluginIndex = index;
                        context.uiRequirements.requiresWalletSelect = false;
                    }
                }
                if (walletPlugin) {
                    context.uiRequirements = Object.assign(Object.assign({}, context.uiRequirements), walletPlugin.config);
                }
                if (options && options.chain) {
                    if (options.chain instanceof common.ChainDefinition) {
                        context.chain = options.chain;
                    }
                    else {
                        context.chain = this.getChainDefinition(options.chain, context.chains);
                    }
                    context.uiRequirements.requiresChainSelect = false;
                }
                else if (context.chains.length === 1) {
                    context.chain = context.chains[0];
                    context.uiRequirements.requiresChainSelect = false;
                }
                else {
                    context.uiRequirements.requiresChainSelect = true;
                }
                if (options === null || options === void 0 ? void 0 : options.permissionLevel) {
                    context.permissionLevel = antelope.PermissionLevel.from(options.permissionLevel);
                    context.uiRequirements.requiresPermissionSelect = false;
                }
                if (context.uiRequirements.requiresChainSelect ||
                    context.uiRequirements.requiresPermissionSelect ||
                    context.uiRequirements.requiresPermissionEntry ||
                    context.uiRequirements.requiresWalletSelect) {
                    const uiLoginResponse = yield context.ui.login(context);
                    if (uiLoginResponse.walletPluginIndex !== undefined) {
                        walletPlugin = this.walletPlugins[uiLoginResponse.walletPluginIndex];
                    }
                    if (!walletPlugin) {
                        throw new Error('UserInterface did not return a valid WalletPlugin index.');
                    }
                    if (uiLoginResponse.chainId) {
                        if (!context.chains.some((c) => c.id.equals(uiLoginResponse.chainId))) {
                            throw new Error('UserInterface did not return a chain ID matching the subset of chains.');
                        }
                        context.chain = this.getChainDefinition(uiLoginResponse.chainId, context.chains);
                    }
                    if (uiLoginResponse.permissionLevel) {
                        context.permissionLevel = antelope.PermissionLevel.from(uiLoginResponse.permissionLevel);
                    }
                }
                if (!walletPlugin) {
                    throw new Error('No WalletPlugin available to perform the login.');
                }
                const { supportedChains } = walletPlugin.config;
                if (context.chain &&
                    supportedChains &&
                    supportedChains.length &&
                    !supportedChains.includes(String(context.chain.id))) {
                    throw new Error(`The wallet plugin '${walletPlugin.metadata.name}' does not support the chain '${context.chain.id}'`);
                }
                for (const hook of context.hooks.beforeLogin)
                    yield hook(context);
                const response = yield walletPlugin.login(context);
                const session = new Session({
                    chain: this.getChainDefinition(response.chain),
                    permissionLevel: response.permissionLevel,
                    walletPlugin,
                }, this.getSessionOptions(options));
                for (const hook of context.hooks.afterLogin)
                    yield hook(context);
                this.persistSession(session, options === null || options === void 0 ? void 0 : options.setAsDefault);
                yield context.ui.onLoginComplete();
                return {
                    context,
                    response,
                    session,
                };
            }
            catch (error) {
                yield this.ui.onError(error);
                throw new Error(error);
            }
        });
    }
    logoutParams(session, walletPlugin) {
        if (session instanceof Session) {
            return {
                session,
                appName: this.appName,
            };
        }
        else {
            return {
                session: new Session({
                    chain: this.getChainDefinition(session.chain),
                    permissionLevel: antelope.PermissionLevel.from({
                        actor: session.actor,
                        permission: session.permission,
                    }),
                    walletPlugin,
                }),
                appName: this.appName,
            };
        }
    }
    logout(session) {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            if (!this.storage) {
                throw new Error('An instance of Storage must be provided to utilize the logout method.');
            }
            yield this.storage.remove('session');
            if (session) {
                const walletPlugin = this.walletPlugins.find((wPlugin) => (session === null || session === void 0 ? void 0 : session.walletPlugin.id) === wPlugin.id);
                if (walletPlugin === null || walletPlugin === void 0 ? void 0 : walletPlugin.logout) {
                    yield walletPlugin.logout(this.logoutParams(session, walletPlugin));
                }
                const sessions = yield this.getSessions();
                if (sessions) {
                    let serialized = session;
                    if (session instanceof Session) {
                        serialized = session.serialize();
                    }
                    const other = sessions.filter((s) => {
                        return (!antelope.Checksum256.from(s.chain).equals(antelope.Checksum256.from(String(serialized.chain))) ||
                            !antelope.Name.from(s.actor).equals(antelope.Name.from(serialized.actor)) ||
                            !antelope.Name.from(s.permission).equals(antelope.Name.from(serialized.permission)));
                    });
                    yield this.storage.write('sessions', JSON.stringify(other));
                }
            }
            else {
                const sessions = yield this.getSessions();
                yield this.storage.remove('sessions');
                if (sessions) {
                    Promise.all(sessions.map((s) => {
                        const walletPlugin = this.walletPlugins.find((wPlugin) => s.walletPlugin.id === wPlugin.id);
                        if (walletPlugin === null || walletPlugin === void 0 ? void 0 : walletPlugin.logout) {
                            return walletPlugin.logout(this.logoutParams(s, walletPlugin));
                        }
                        else {
                            return Promise.resolve();
                        }
                    }));
                }
            }
        });
    }
    restore(args, options) {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            if (!args) {
                const data = yield this.storage.read('session');
                if (data) {
                    args = JSON.parse(data);
                }
                else {
                    return;
                }
            }
            if (!args) {
                throw new Error('Either a RestoreArgs object or a Storage instance must be provided.');
            }
            const chainId = antelope.Checksum256.from(args.chain instanceof common.ChainDefinition ? args.chain.id : args.chain);
            let serializedSession;
            const data = yield this.storage.read('sessions');
            if (data) {
                const sessions = JSON.parse(data);
                if (args.actor && args.permission) {
                    serializedSession = sessions.find((s) => {
                        return (args &&
                            chainId.equals(s.chain) &&
                            s.actor === args.actor &&
                            s.permission === args.permission);
                    });
                }
                else {
                    serializedSession = sessions.find((s) => {
                        return args && chainId.equals(s.chain) && s.default;
                    });
                }
            }
            else {
                if (args.actor && args.permission && args.walletPlugin) {
                    serializedSession = {
                        chain: String(chainId),
                        actor: args.actor,
                        permission: args.permission,
                        walletPlugin: {
                            id: args.walletPlugin.id,
                            data: args.walletPlugin.data,
                        },
                        data: args.data,
                    };
                }
                else {
                    throw new Error('No sessions found in storage. A wallet plugin must be provided.');
                }
            }
            if (!serializedSession) {
                return;
            }
            const walletPlugin = this.walletPlugins.find((p) => {
                if (!args) {
                    return false;
                }
                return p.id === serializedSession.walletPlugin.id;
            });
            if (!walletPlugin) {
                throw new Error(`No WalletPlugin found with the ID of: '${serializedSession.walletPlugin.id}'`);
            }
            if (serializedSession.walletPlugin.data) {
                walletPlugin.data = serializedSession.walletPlugin.data;
            }
            if (args.walletPlugin && args.walletPlugin.data) {
                walletPlugin.data = args.walletPlugin.data;
            }
            const session = new Session({
                chain: this.getChainDefinition(serializedSession.chain),
                permissionLevel: antelope.PermissionLevel.from({
                    actor: serializedSession.actor,
                    permission: serializedSession.permission,
                }),
                walletPlugin,
            }, this.getSessionOptions(options));
            if (serializedSession.data) {
                session.data = serializedSession.data;
            }
            this.persistSession(session, options === null || options === void 0 ? void 0 : options.setAsDefault);
            return session;
        });
    }
    restoreAll() {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            const sessions = [];
            const serializedSessions = yield this.getSessions();
            if (serializedSessions) {
                for (const s of serializedSessions) {
                    const session = yield this.restore(s);
                    if (session) {
                        sessions.push(session);
                    }
                }
            }
            return sessions;
        });
    }
    persistSession(session, setAsDefault = true) {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            if (!this.storage) {
                return;
            }
            const serialized = session.serialize();
            serialized.default = setAsDefault;
            if (setAsDefault) {
                this.storage.write('session', JSON.stringify(serialized));
            }
            const existing = yield this.storage.read('sessions');
            if (existing) {
                const stored = JSON.parse(existing);
                const sessions = stored
                    .filter((s) => {
                    return (!antelope.Checksum256.from(s.chain).equals(antelope.Checksum256.from(serialized.chain)) ||
                        !antelope.Name.from(s.actor).equals(antelope.Name.from(serialized.actor)) ||
                        !antelope.Name.from(s.permission).equals(antelope.Name.from(serialized.permission)));
                })
                    .map((s) => {
                    if (session.chain.id.equals(s.chain)) {
                        s.default = false;
                    }
                    return s;
                });
                const orderedSessions = [...sessions, serialized];
                orderedSessions.sort((a, b) => {
                    const chain = String(a.chain).localeCompare(String(b.chain));
                    const actor = String(a.actor).localeCompare(String(b.actor));
                    const permission = String(a.permission).localeCompare(String(b.permission));
                    return chain || actor || permission;
                });
                this.storage.write('sessions', JSON.stringify(orderedSessions));
            }
            else {
                this.storage.write('sessions', JSON.stringify([serialized]));
            }
        });
    }
    getSessions() {
        return tslib.__awaiter(this, void 0, void 0, function* () {
            if (!this.storage) {
                throw new Error('No storage instance is available to retrieve sessions from.');
            }
            const data = yield this.storage.read('sessions');
            if (!data)
                return [];
            try {
                const parsed = JSON.parse(data);
                const filtered = parsed.filter((s) => this.walletPlugins.some((p) => {
                    return p.id === s.walletPlugin.id;
                }));
                return filtered;
            }
            catch (e) {
                throw new Error(`Failed to parse sessions from storage (${e})`);
            }
        });
    }
    getSessionOptions(options) {
        return {
            abis: this.abis,
            allowModify: this.allowModify,
            appName: this.appName,
            expireSeconds: this.expireSeconds,
            fetch: this.fetch,
            storage: this.storage,
            transactPlugins: (options === null || options === void 0 ? void 0 : options.transactPlugins) || this.transactPlugins,
            transactPluginsOptions: (options === null || options === void 0 ? void 0 : options.transactPluginsOptions) || this.transactPluginsOptions,
            ui: this.ui,
        };
    }
}

class AbstractUserInterface {
    translate(key, options, namespace) {
        throw new Error('The `translate` method must be implemented in this UserInterface. Called with: ' +
            JSON.stringify({
                key,
                options,
                namespace,
            }));
    }
    getTranslate(namespace) {
        return (key, options) => this.translate(key, options, namespace);
    }
    addTranslations(translations) {
        throw new Error('The `addTranslations` method must be implemented in this UserInterface. Called with: ' +
            JSON.stringify(translations));
    }
}

exports.AbstractAccountCreationPlugin = AbstractAccountCreationPlugin;
exports.AbstractLoginPlugin = AbstractLoginPlugin;
exports.AbstractTransactPlugin = AbstractTransactPlugin;
exports.AbstractUserInterface = AbstractUserInterface;
exports.AbstractWalletPlugin = AbstractWalletPlugin;
exports.BaseLoginPlugin = BaseLoginPlugin;
exports.BaseTransactPlugin = BaseTransactPlugin;
exports.BrowserLocalStorage = BrowserLocalStorage;
exports.CreateAccountContext = CreateAccountContext;
exports.LoginContext = LoginContext;
exports.Session = Session;
exports.SessionKit = SessionKit;
exports.TransactContext = TransactContext;
exports.TransactRevisions = TransactRevisions;
exports.appendAction = appendAction;
exports["default"] = SessionKit;
exports.getFetch = getFetch;
exports.prependAction = prependAction;
Object.keys(common).forEach(function (k) {
    if (k !== 'default' && !exports.hasOwnProperty(k)) Object.defineProperty(exports, k, {
        enumerable: true,
        get: function () { return common[k]; }
    });
});
Object.keys(antelope).forEach(function (k) {
    if (k !== 'default' && !exports.hasOwnProperty(k)) Object.defineProperty(exports, k, {
        enumerable: true,
        get: function () { return antelope[k]; }
    });
});
Object.keys(signingRequest).forEach(function (k) {
    if (k !== 'default' && !exports.hasOwnProperty(k)) Object.defineProperty(exports, k, {
        enumerable: true,
        get: function () { return signingRequest[k]; }
    });
});
Object.keys(abicache).forEach(function (k) {
    if (k !== 'default' && !exports.hasOwnProperty(k)) Object.defineProperty(exports, k, {
        enumerable: true,
        get: function () { return abicache[k]; }
    });
});
//# sourceMappingURL=session.js.map
